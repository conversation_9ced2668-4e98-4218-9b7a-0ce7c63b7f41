"""Missing BIM component classes identified in coverage analysis.

This file contains the classes that were missing from the Python implementation
when compared to the C# ShedBim.cs file.
"""

# Fix imports to use absolute paths
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from dataclasses import dataclass, field
from typing import List, Optional, TYPE_CHECKING

from src.geometry.primitives import Vec3

# Avoid circular imports
if TYPE_CHECKING:
    from .components import ShedBimSection, ShedBimColumn, ShedBimBracket, ShedBimPair
    from .accessories import ShedBimFlashing
    from .openings import MezzanineStairsInfo, RoofOpeningInfo


# C# Ref: Lines 232-257 - public class ShedBimEavePurlinBracket
@dataclass
class ShedBimEavePurlinBracket:
    """Eave purlin bracket with support and slider options.
    
    C# Reference: ShedBim.cs lines 232-257
    """
    # C# Ref: Line 237 - public ShedBimBracket Bracket { get; set; }
    bracket: Optional['ShedBimBracket'] = None
    
    # C# Ref: Line 243 - public ShedBimBracket Cleat { get; set; }
    cleat: Optional['ShedBimBracket'] = None
    
    # C# Ref: Line 250 - public ShedBimPair<ShedBimBracket> Support { get; set; }
    support: Optional['ShedBimPair'] = None
    
    # C# Ref: Line 256 - public ShedBimBracket Slider { get; set; }
    slider: Optional['ShedBimBracket'] = None


# C# Ref: Lines 342-347 - public class ShedBimMullion
@dataclass
class ShedBimMullion:
    """Mullion (vertical structural member) specification.
    
    C# Reference: ShedBim.cs lines 342-347
    """
    # C# Ref: Line 344 - public ShedBimColumn Mullion { get; set; }
    mullion: Optional['ShedBimColumn'] = None
    
    # C# Ref: Line 345 - public ShedBimColumn RotatedMullion1 { get; set; }
    rotated_mullion1: Optional['ShedBimColumn'] = None
    
    # C# Ref: Line 346 - public ShedBimColumn RotatedMullion2 { get; set; }
    rotated_mullion2: Optional['ShedBimColumn'] = None


# C# Ref: Lines 357-368 - public class ShedBimMezzStairs
@dataclass
class ShedBimMezzStairs:
    """Mezzanine staircase specification.
    
    C# Reference: ShedBim.cs lines 357-368
    """
    # C# Ref: Line 359 - public MezzanineStairsInfo Info { get; set; }
    info: Optional['MezzanineStairsInfo'] = None
    
    # C# Ref: Line 361 - public Vec3 ClearanceUpper { get; set; }
    clearance_upper: Optional[Vec3] = None
    
    # C# Ref: Line 362 - public Vec3 StairsUpper { get; set; }
    stairs_upper: Optional[Vec3] = None
    
    # C# Ref: Line 363 - public Vec3 StairsLower { get; set; }
    stairs_lower: Optional[Vec3] = None
    
    # C# Ref: Line 364 - public Vec3 ClearanceLower { get; set; }
    clearance_lower: Optional[Vec3] = None
    
    # C# Ref: Line 366 - public ShedBimSection Bearer { get; set; }
    bearer: Optional['ShedBimSection'] = None
    
    # C# Ref: Line 367 - public List<ShedBimColumn> Posts { get; set; }
    posts: List['ShedBimColumn'] = field(default_factory=list)


# C# Ref: Lines 487-491 - public class ShedBimWallGirtBay
@dataclass
class ShedBimWallGirtBay:
    """Wall girt bay specification.
    
    C# Reference: ShedBim.cs lines 487-491
    """
    # C# Ref: Line 489 - public List<ShedBimSection> Girts { get; set; }
    girts: List['ShedBimSection'] = field(default_factory=list)
    
    # C# Ref: Line 490 - public List<ShedBimSection> Bridgings { get; set; }
    bridgings: List['ShedBimSection'] = field(default_factory=list)


# C# Ref: Lines 513-518 - public class ShedBimRoofPurlinBay
@dataclass
class ShedBimRoofPurlinBay:
    """Roof purlin bay specification.
    
    C# Reference: ShedBim.cs lines 513-518
    """
    # C# Ref: Line 515 - public List<ShedBimRoofPurlin> RoofPurlins { get; set; }
    roof_purlins: List['ShedBimRoofPurlin'] = field(default_factory=list)
    
    # C# Ref: Line 517 - public List<ShedBimSection> Bridgings { get; set; }
    bridgings: List['ShedBimSection'] = field(default_factory=list)


# C# Ref: Lines 523-531 - public class ShedBimRoofPurlin
@dataclass
class ShedBimRoofPurlin:
    """Roof purlin with optional overhang cap.
    
    C# Reference: ShedBim.cs lines 523-531
    """
    # C# Ref: Line 525 - public ShedBimSection RoofPurlin { get; set; }
    roof_purlin: Optional['ShedBimSection'] = None
    
    # C# Ref: Line 530 - public ShedBimSection OverhangCap { get; set; }
    overhang_cap: Optional['ShedBimSection'] = None


# C# Ref: Lines 533-559 - public class ShedBimEaveBeam
@dataclass
class ShedBimEaveBeam:
    """Eave beam or header beam specification.
    
    C# Reference: ShedBim.cs lines 533-559
    """
    # C# Ref: Line 535 - public int BayFirst { get; set; }
    bay_first: int = 0
    
    # C# Ref: Line 537 - public int BayLast { get; set; }
    bay_last: int = 0
    
    # C# Ref: Line 542 - public ShedBimSection Beam { get; set; }
    beam: Optional['ShedBimSection'] = None
    
    # C# Ref: Line 547 - public List<ShedBimSection> Girts { get; set; }
    girts: List['ShedBimSection'] = field(default_factory=list)
    
    # C# Ref: Line 552 - public ShedBimSection Slider { get; set; }
    slider: Optional['ShedBimSection'] = None
    
    # C# Ref: Line 558 - public ShedBimPair<ShedBimFlashing> Flashing { get; set; }
    flashing: Optional['ShedBimPair'] = None


# C# Ref: Lines 561-565 - public class ShedBimLongitudinalBraceBay
@dataclass
class ShedBimLongitudinalBraceBay:
    """Longitudinal brace bay specification.
    
    C# Reference: ShedBim.cs lines 561-565
    """
    # C# Ref: Line 563 - public ShedBimSection BraceLeft { get; set; }
    brace_left: Optional['ShedBimSection'] = None
    
    # C# Ref: Line 564 - public ShedBimSection BraceRight { get; set; }
    brace_right: Optional['ShedBimSection'] = None


# C# Ref: Lines 304-313 - public class ShedBimOutrigger
@dataclass
class ShedBimOutrigger:
    """Outrigger structural component.
    
    C# Reference: ShedBim.cs lines 304-313
    """
    # C# Ref: Line 306 - public ShedBimSection Header { get; set; }
    header: Optional['ShedBimSection'] = None
    
    # C# Ref: Line 308 - public ShedBimColumn Column { get; set; }
    column: Optional['ShedBimColumn'] = None
    
    # C# Ref: Line 310 - public ShedBimColumn Brace { get; set; }
    brace: Optional['ShedBimColumn'] = None
    
    # C# Ref: Line 312 - public ShedBimPair<ShedBimBracket> HeaderBracket { get; set; }
    header_bracket: Optional['ShedBimPair'] = None


# C# Ref: Lines 661-666 - public class ShedBimRollerDoorCylinder
@dataclass
class ShedBimRollerDoorCylinder:
    """Roller door cylinder/drum specification.
    
    C# Reference: ShedBim.cs lines 661-666
    """
    # C# Ref: Line 663 - public Vec3 StartPos { get; set; }
    start_pos: Optional[Vec3] = None
    
    # C# Ref: Line 664 - public Vec3 EndPos { get; set; }
    end_pos: Optional[Vec3] = None
    
    # C# Ref: Line 665 - public double DrumRadius { get; set; }
    drum_radius: float = 0.0


# C# Ref: Lines 668-674 - public class ShedBimGlassSlidingDoor
@dataclass
class ShedBimGlassSlidingDoor:
    """Glass sliding door specification.
    
    C# Reference: ShedBim.cs lines 668-674
    """
    # C# Ref: Line 670 - public double FrameWidth { get; set; } = 50
    frame_width: float = 50.0
    
    # C# Ref: Line 671 - public double FrameThickness { get; set; } = 30
    frame_thickness: float = 30.0
    
    # C# Ref: Line 672 - public double GlassThickness { get; set; } = 5
    glass_thickness: float = 5.0
    
    # C# Ref: Line 673 - public int FrontPanel { get; set; } = 0
    front_panel: int = 0


# C# Ref: Lines 676-689 - public class ShedBimGlassSlidingDoorPanel
@dataclass
class ShedBimGlassSlidingDoorPanel:
    """Glass sliding door panel specification.
    
    C# Reference: ShedBim.cs lines 676-689
    """
    # C# Ref: Line 678 - public Vec3 OuterBottomLeft { get; set; }
    outer_bottom_left: Optional[Vec3] = None
    
    # C# Ref: Line 679 - public Vec3 OuterBottomRight { get; set; }
    outer_bottom_right: Optional[Vec3] = None
    
    # C# Ref: Line 680 - public Vec3 OuterTopLeft { get; set; }
    outer_top_left: Optional[Vec3] = None
    
    # C# Ref: Line 681 - public Vec3 OuterTopRight { get; set; }
    outer_top_right: Optional[Vec3] = None
    
    # C# Ref: Line 682 - public Vec3 InnerBottomLeft { get; set; }
    inner_bottom_left: Optional[Vec3] = None
    
    # C# Ref: Line 683 - public Vec3 InnerBottomRight { get; set; }
    inner_bottom_right: Optional[Vec3] = None
    
    # C# Ref: Line 684 - public Vec3 InnerTopLeft { get; set; }
    inner_top_left: Optional[Vec3] = None
    
    # C# Ref: Line 685 - public Vec3 InnerTopRight { get; set; }
    inner_top_right: Optional[Vec3] = None
    
    # C# Ref: Line 686 - public Vec3 FrameDepth { get; set; }
    frame_depth: Optional[Vec3] = None
    
    # C# Ref: Line 687 - public Vec3 GlassShift { get; set; }
    glass_shift: Optional[Vec3] = None
    
    # C# Ref: Line 688 - public Vec3 GlassDepth { get; set; }
    glass_depth: Optional[Vec3] = None


# C# Ref: Lines 692-700 - public class ShedBimWindow
@dataclass
class ShedBimWindow:
    """Window specification.
    
    C# Reference: ShedBim.cs lines 692-700
    """
    # C# Ref: Line 694 - public double FrameWidth { get; set; } = 50
    frame_width: float = 50.0
    
    # C# Ref: Line 695 - public double FrameThickness { get; set; } = 30
    frame_thickness: float = 30.0
    
    # C# Ref: Line 696 - public double GlassThickness { get; set; } = 5
    glass_thickness: float = 5.0
    
    # C# Ref: Line 697 - public double Overlap { get; set; } = 50
    overlap: float = 50.0
    
    # C# Ref: Line 698 - public bool isLeftFront { get; set; } = true
    is_left_front: bool = True
    
    # C# Ref: Line 699 - public string type { get; set; } = "sliding"
    type: str = "sliding"


# C# Ref: Lines 622-629 - public class ShedBimRoofOpening
@dataclass
class ShedBimRoofOpening:
    """Roof opening (skylight, vent) specification.
    
    C# Reference: ShedBim.cs lines 622-629
    """
    # C# Ref: Line 624 - public RoofOpeningInfo Info { get; set; }
    info: Optional['RoofOpeningInfo'] = None
    
    # C# Ref: Line 626 - public List<Vec3> Outline { get; set; }
    outline: List[Vec3] = field(default_factory=list)
    
    # C# Ref: Line 628 - public Vec3 Normal { get; set; }
    normal: Optional[Vec3] = None