"""BIM Data Model Module - Task 3

This module contains the Building Information Model data structures that represent
the complete hierarchical structure of a building (shed/carport).

C# Reference: ShedBim.cs, Openings.cs
"""

# Core BIM model classes
from .shed_bim import (
    Shed<PERSON>im,
    ShedBimPart,
    ShedBimPartMain,
    ShedBimPartLeanto,
    ShedBimSide,
    ShedBimRoof,
    ShedBimEnd,
    ShedBimWall,
    ShedBimMezz,
    ShedBimSlab,
    ShedBimSlabPiece,
    ShedBimRidgeDivider,
    ShedBimSamples,
    ShedBimComponentTag
)

# Structural components
from .components import (
    ShedBimSection,
    ShedBimColumn,
    ShedBimColumnCompositeSection,
    ShedBimFooting,
    ShedBimBracket,
    ShedBimFastener,
    ShedBimPair,
    ShedBimSectionCut,
    BracketAttachment
)

# Wall and roof components
from .wall_roof import (
    ShedBimWallGirtBay,
    ShedBimRoofPurlinBay,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>aveBeam,
    ShedBimLongitudinalBraceBay,
    ShedBimMullion,
    ShedBimOutrigger
)

# Cladding and finishing
from .cladding import (
    ShedBimCladding,
    ShedBimCladdingSegment,
    ShedBimCladdingHole,
    ShedBimLiningSegment,
    ShedBimSkylight,
    MaterialSegmentData
)

# Openings
from .openings import (
    ShedBimOpening,
    ShedBimRoofOpening,
    ShedBimOpenBay,
    OpeningInfo,
    OpeningInfoDesign,
    RoofOpeningInfo,
    RoofOpeningInfoDesign,
    IndustrialSlidingDoorDesign,
    MezzanineStairsInfo,
    MezzanineStairsOrientation
)

# Accessories
from .accessories import (
    ShedBimFlashing,
    ShedBimFlashingCut,
    ShedBimDownpipe,
    ShedBimStrapBrace,
    ShedBimStrapLine,
    ShedBimRollerDoorCylinder
)

# Mezzanine
from .mezzanine import (
    ShedBimMezzStairs
)

__all__ = [
    # Core model
    'ShedBim',
    'ShedBimPart',
    'ShedBimPartMain',
    'ShedBimPartLeanto',
    'ShedBimSide',
    'ShedBimRoof',
    'ShedBimEnd',
    'ShedBimWall',
    'ShedBimMezz',
    'ShedBimSlab',
    'ShedBimSlabPiece',
    'ShedBimRidgeDivider',
    'ShedBimSamples',
    'ShedBimComponentTag',
    
    # Components
    'ShedBimSection',
    'ShedBimColumn',
    'ShedBimColumnCompositeSection',
    'ShedBimFooting',
    'ShedBimBracket',
    'ShedBimFastener',
    'ShedBimPair',
    'ShedBimSectionCut',
    'BracketAttachment',
    
    # Wall/Roof
    'ShedBimWallGirtBay',
    'ShedBimRoofPurlinBay',
    'ShedBimRoofPurlin',
    'ShedBimEaveBeam',
    'ShedBimLongitudinalBraceBay',
    'ShedBimMullion',
    'ShedBimOutrigger',
    
    # Cladding
    'ShedBimCladding',
    'ShedBimCladdingSegment',
    'ShedBimCladdingHole',
    'ShedBimLiningSegment',
    'ShedBimSkylight',
    'MaterialSegmentData',
    
    # Openings
    'ShedBimOpening',
    'ShedBimRoofOpening',
    'ShedBimOpenBay',
    'OpeningInfo',
    'OpeningInfoDesign',
    'RoofOpeningInfo',
    'RoofOpeningInfoDesign',
    'IndustrialSlidingDoorDesign',
    'MezzanineStairsInfo',
    'MezzanineStairsOrientation',
    
    # Accessories
    'ShedBimFlashing',
    'ShedBimFlashingCut',
    'ShedBimDownpipe',
    'ShedBimStrapBrace',
    'ShedBimStrapLine',
    'ShedBimRollerDoorCylinder',
    
    # Mezzanine
    'ShedBimMezzStairs'
]