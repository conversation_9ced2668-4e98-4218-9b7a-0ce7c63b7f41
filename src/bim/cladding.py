"""Cladding and lining component classes for the BIM model.

C# Reference: ShedBim.cs Lines 723-760
"""

# Fix imports to use absolute paths
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from dataclasses import dataclass, field
from typing import List, Optional

from src.geometry.primitives import Vec3
from src.materials.base import CladdingMaterial, LiningMaterial
from .components import ColorMaterial


# Base class for material segment data
@dataclass
class MaterialSegmentData:
    """Base class for material segment data.
    
    Not explicitly in C# but used as base for segments.
    """
    start_offset: float = 0.0
    end_offset: float = 0.0
    width: float = 0.0


# C# Ref: Lines 723-737 - public class ShedBimCladding
@dataclass
class ShedBimCladding:
    """Cladding specification with geometry and materials.
    
    C# Reference: ShedBim.cs lines 723-737
    """
    # C# Ref: Line 725 - public List<Vec3> Points { get; set; }
    points: List[Vec3] = field(default_factory=list)
    
    # C# Ref: Line 726 - public Vec3 Normal { get; set; }
    normal: Optional[Vec3] = None
    
    # C# Ref: Line 727 - public Vec3 Right { get; set; }
    right: Optional[Vec3] = None
    
    # C# Ref: Line 728 - public List<ShedBimCladdingHole> Holes { get; set; }
    holes: List['ShedBimCladdingHole'] = field(default_factory=list)
    
    # C# Ref: Line 729 - public CladdingMaterial Cladding { get; set; }
    cladding: Optional[CladdingMaterial] = None
    
    # C# Ref: Line 730 - public List<ShedBimCladdingSegment> CladdingSegments { get; set; }
    cladding_segments: List['ShedBimCladdingSegment'] = field(default_factory=list)
    
    # C# Ref: Line 731 - public List<ShedBimSkylight> Skylights { get; set; }
    skylights: List['ShedBimSkylight'] = field(default_factory=list)
    
    # C# Ref: Line 732 - public ColorMaterial TopColor { get; set; }
    top_color: Optional[ColorMaterial] = None
    
    # C# Ref: Line 733 - public ColorMaterial BottomColor { get; set; }
    bottom_color: Optional[ColorMaterial] = None
    
    # C# Ref: Line 734 - public ColorMaterial Color { get; set; }
    color: Optional[ColorMaterial] = None
    
    # C# Ref: Line 735 - public List<LiningMaterial> LiningMaterials { get; set; }
    lining_materials: List[LiningMaterial] = field(default_factory=list)
    
    # C# Ref: Line 736 - public List<ShedBimLiningSegment> LiningSegments { get; set; }
    lining_segments: List['ShedBimLiningSegment'] = field(default_factory=list)


# C# Ref: Lines 739-744 - public class ShedBimLiningSegment : MaterialSegmentData
@dataclass
class ShedBimLiningSegment(MaterialSegmentData):
    """Lining segment with material and cuts.
    
    C# Reference: ShedBim.cs lines 739-744
    """
    # C# Ref: Line 741 - public LiningMaterial Material { get; set; }
    material: Optional[LiningMaterial] = None
    
    # C# Ref: Line 743 - public List<ShedBimCladdingHole> Cuts { get; set; }
    cuts: List['ShedBimCladdingHole'] = field(default_factory=list)


# C# Ref: Lines 746-749 - public class ShedBimCladdingSegment : MaterialSegmentData
@dataclass
class ShedBimCladdingSegment(MaterialSegmentData):
    """Cladding segment with holes.
    
    C# Reference: ShedBim.cs lines 746-749
    """
    # C# Ref: Line 748 - public List<ShedBimCladdingHole> Holes { get; set; }
    holes: List['ShedBimCladdingHole'] = field(default_factory=list)


# C# Ref: Lines 751-754 - public class ShedBimCladdingHole
@dataclass
class ShedBimCladdingHole:
    """Hole or cutout in cladding.
    
    C# Reference: ShedBim.cs lines 751-754
    """
    # C# Ref: Line 753 - public List<Vec3> Points { get; set; }
    points: List[Vec3] = field(default_factory=list)


# C# Ref: Lines 756-760 - public class ShedBimSkylight : MaterialSegmentData
@dataclass
class ShedBimSkylight(MaterialSegmentData):
    """Skylight specification.
    
    C# Reference: ShedBim.cs lines 756-760
    """
    # C# Ref: Line 758 - public Vec3 Centroid { get; set; }
    centroid: Optional[Vec3] = None
    
    # C# Ref: Line 759 - public double SheetLength { get; set; }
    sheet_length: float = 0.0