"""Core BIM model classes representing the building structure.

C# Reference: ShedBim.cs Lines 14-461
"""

from dataclasses import dataclass, field
from typing import List, Optional, TYPE_CHECKING
from abc import ABC, abstractmethod

# Fix imports
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.geometry.primitives import Vec2, Vec3
from src.geometry.lines import Line1

# Avoid circular imports
if TYPE_CHECKING:
    from .components import ShedBimColumn, ShedBimSection, ShedBimFooting, ShedBimBracket, ShedBimPair, ShedBimColumnCompositeSection
    from .wall_roof import ShedBimMullion, ShedBimWallGirtBay, ShedBimEaveBeam, ShedBimLongitudinalBraceBay, ShedBimRoofPurlinBay, ShedBimOutrigger
    from .accessories import ShedBimFlashing, ShedBimDownpipe, ShedBimStrapBrace
    from .openings import Shed<PERSON>im<PERSON>penBay, Shed<PERSON><PERSON><PERSON>pening, ShedBimRoofOpening
    from .cladding import ShedBimCladding
    from .mezzanine import ShedBimMezzStairs


# C# Ref: Lines 14-27 - public class ShedBim
@dataclass
class ShedBim:
    """Root building information model class.
    
    C# Reference: ShedBim.cs lines 14-27
    Contains the main building part and optional lean-to extensions.
    """
    # C# Ref: Line 16 - public ShedBimPartMain Main { get; set; }
    main: Optional['ShedBimPartMain'] = None
    
    # C# Ref: Line 18 - public ShedBimPartLeanto LeantoLeft { get; set; }
    leanto_left: Optional['ShedBimPartLeanto'] = None
    
    # C# Ref: Line 20 - public ShedBimPartLeanto LeantoRight { get; set; }
    leanto_right: Optional['ShedBimPartLeanto'] = None
    
    # C# Ref: Line 22 - public List<List<Vec3>> OutlineFrames { get; set; }
    outline_frames: List[List[Vec3]] = field(default_factory=list)
    
    # C# Ref: Line 24 - public List<ShedBimComponentTag> ComponentTags { get; set; }
    component_tags: List['ShedBimComponentTag'] = field(default_factory=list)
    
    # C# Ref: Line 26 - public ShedBimSamples Samples { get; set; }
    samples: Optional['ShedBimSamples'] = None


# C# Ref: Lines 29-60 - public abstract class ShedBimPart
@dataclass
class ShedBimPart(ABC):
    """Abstract base class for building parts.
    
    C# Reference: ShedBim.cs lines 29-60
    Contains common properties for main building and lean-to parts.
    """
    # C# Ref: Line 31 - public List<double> EndbaySizes { get; set; }
    endbay_sizes: List[float] = field(default_factory=list)
    
    # C# Ref: Line 33 - public List<double> EndbayOffsets { get; set; }
    endbay_offsets: List[float] = field(default_factory=list)
    
    # C# Ref: Line 35 - public Line1 WallSpanExtents { get; set; }
    wall_span_extents: Optional[Line1] = None
    
    # C# Ref: Line 37 - public Line1 WallSpanInnerExtents { get; set; }
    wall_span_inner_extents: Optional[Line1] = None
    
    # C# Ref: Line 39 - public Line1 WallLengthExtents { get; set; }
    wall_length_extents: Optional[Line1] = None
    
    # C# Ref: Line 41 - public List<Vec2> RoofFrameExtents { get; set; }
    roof_frame_extents: List[Vec2] = field(default_factory=list)
    
    # C# Ref: Line 43 - public Line1 RoofSpanExtents { get; set; }
    roof_span_extents: Optional[Line1] = None
    
    # C# Ref: Line 45 - public Line1 RoofLengthExtents { get; set; }
    roof_length_extents: Optional[Line1] = None
    
    # C# Ref: Line 47 - public List<ShedBimEnd> Ends { get; set; }
    ends: List['ShedBimEnd'] = field(default_factory=list)
    
    # C# Ref: Line 49 - public ShedBimMezz Mezz { get; set; }
    mezz: Optional['ShedBimMezz'] = None
    
    # C# Ref: Line 51 - public ShedBimSlab Slab { get; set; }
    slab: Optional['ShedBimSlab'] = None
    
    # C# Ref: Line 53 - public ShedBimRidgeDivider RidgeDivider { get; set; }
    ridge_divider: Optional['ShedBimRidgeDivider'] = None
    
    # C# Ref: Line 55 - public List<ShedBimFlashing> OpenEndBayCornerFlashings { get; set; }
    open_end_bay_corner_flashings: List['ShedBimFlashing'] = field(default_factory=list)
    
    # C# Ref: Line 57 - public abstract List<ShedBimSide> GetSides();
    @abstractmethod
    def get_sides(self) -> List['ShedBimSide']:
        """Get all sides of this building part."""
        pass
    
    # C# Ref: Line 59 - public abstract List<ShedBimRoof> GetRoofs();
    @abstractmethod
    def get_roofs(self) -> List['ShedBimRoof']:
        """Get all roofs of this building part."""
        pass


# C# Ref: Lines 62-126 - public class ShedBimPartMain : ShedBimPart
@dataclass
class ShedBimPartMain(ShedBimPart):
    """Main building part with roofs, sides, and ends.
    
    C# Reference: ShedBim.cs lines 62-126
    The primary structure of the building.
    """
    # C# Ref: Line 64 - public string RoofType { get; set; }
    roof_type: str = ""
    
    # C# Ref: Line 65 - public ShedBimRoof RoofLeft { get; set; }
    roof_left: Optional['ShedBimRoof'] = None
    
    # C# Ref: Line 66 - public ShedBimRoof RoofRight { get; set; }
    roof_right: Optional['ShedBimRoof'] = None
    
    # C# Ref: Line 67 - public ShedBimRoof RoofMansardLeft { get; set; }
    roof_mansard_left: Optional['ShedBimRoof'] = None
    
    # C# Ref: Line 68 - public ShedBimRoof RoofMansardRight { get; set; }
    roof_mansard_right: Optional['ShedBimRoof'] = None
    
    # C# Ref: Line 73 - public List<ShedBimSection> ApexBraces { get; set; }
    apex_braces: List['ShedBimSection'] = field(default_factory=list)
    
    # C# Ref: Line 78 - public List<ShedBimSection> ApexBracesLeft { get; set; }
    apex_braces_left: List['ShedBimSection'] = field(default_factory=list)
    
    # C# Ref: Line 83 - public List<ShedBimSection> ApexBracesRight { get; set; }
    apex_braces_right: List['ShedBimSection'] = field(default_factory=list)
    
    # C# Ref: Line 88 - public List<ShedBimFlashing> RidgeFlashings { get; set; }
    ridge_flashings: List['ShedBimFlashing'] = field(default_factory=list)
    
    # C# Ref: Line 90 - public List<ShedBimPair<ShedBimBracket>> ApexBrackets { get; set; }
    apex_brackets: List['ShedBimPair'] = field(default_factory=list)
    
    # C# Ref: Line 92 - public List<ShedBimPair<ShedBimBracket>> ApexBracketsLeft { get; set; }
    apex_brackets_left: List['ShedBimPair'] = field(default_factory=list)
    
    # C# Ref: Line 94 - public List<ShedBimPair<ShedBimBracket>> ApexBracketsRight { get; set; }
    apex_brackets_right: List['ShedBimPair'] = field(default_factory=list)
    
    # C# Ref: Line 96 - public ShedBimSide SideLeft { get; set; }
    side_left: Optional['ShedBimSide'] = None
    
    # C# Ref: Line 97 - public ShedBimSide SideRight { get; set; }
    side_right: Optional['ShedBimSide'] = None
    
    # C# Ref: Lines 99-106 - public override List<ShedBimSide> GetSides()
    def get_sides(self) -> List['ShedBimSide']:
        """Get both sides of the main building."""
        sides = []
        if self.side_left:
            sides.append(self.side_left)
        if self.side_right:
            sides.append(self.side_right)
        return sides
    
    # C# Ref: Lines 108-116 - public override List<ShedBimRoof> GetRoofs()
    def get_roofs(self) -> List['ShedBimRoof']:
        """Get all roofs including mansard roofs if present."""
        roofs = []
        if self.roof_mansard_left:
            roofs.append(self.roof_mansard_left)
        if self.roof_left:
            roofs.append(self.roof_left)
        if self.roof_right:
            roofs.append(self.roof_right)
        if self.roof_mansard_right:
            roofs.append(self.roof_mansard_right)
        return roofs
    
    # C# Ref: Lines 118-125 - public List<List<ShedBimSection>> GetApexBraceLists()
    def get_apex_brace_lists(self) -> List[List['ShedBimSection']]:
        """Get all apex brace lists."""
        apex_brace_lists = []
        if self.apex_braces_left:
            apex_brace_lists.append(self.apex_braces_left)
        if self.apex_braces:
            apex_brace_lists.append(self.apex_braces)
        if self.apex_braces_right:
            apex_brace_lists.append(self.apex_braces_right)
        return apex_brace_lists


# C# Ref: Lines 128-155 - public class ShedBimPartLeanto : ShedBimPart
@dataclass
class ShedBimPartLeanto(ShedBimPart):
    """Lean-to building extension.
    
    C# Reference: ShedBim.cs lines 128-155
    A single-sided extension attached to the main building.
    """
    # C# Ref: Line 130 - public string RoofType { get; set; }
    roof_type: str = ""
    
    # C# Ref: Line 132 - public ShedBimRoof Roof { get; set; }
    roof: Optional['ShedBimRoof'] = None
    
    # C# Ref: Line 134 - public ShedBimSide Side { get; set; }
    side: Optional['ShedBimSide'] = None
    
    # C# Ref: Line 136 - public List<ShedBimFlashing> ParapetFlashings { get; set; }
    parapet_flashings: List['ShedBimFlashing'] = field(default_factory=list)
    
    # C# Ref: Line 138 - public List<ShedBimPair<ShedBimBracket>> ConnectionBrackets { get; set; }
    connection_brackets: List['ShedBimPair'] = field(default_factory=list)
    
    # C# Ref: Lines 140-146 - public override List<ShedBimSide> GetSides()
    def get_sides(self) -> List['ShedBimSide']:
        """Get the single side of the lean-to."""
        if self.side:
            return [self.side]
        return []
    
    # C# Ref: Lines 148-154 - public override List<ShedBimRoof> GetRoofs()
    def get_roofs(self) -> List['ShedBimRoof']:
        """Get the single roof of the lean-to."""
        if self.roof:
            return [self.roof]
        return []


# C# Ref: Lines 157-243 - public class ShedBimSide
@dataclass
class ShedBimSide:
    """Wall/side components of a building part.
    
    C# Reference: ShedBim.cs lines 157-243
    Contains columns, wall elements, and structural components.
    """
    # C# Ref: Line 159 - public double WallHeight { get; set; }
    wall_height: float = 0.0
    
    # C# Ref: Line 161 - public List<ShedBimColumn> Columns { get; set; }
    columns: List['ShedBimColumn'] = field(default_factory=list)
    
    # C# Ref: Line 167 - public List<double> ColumnHaunchBracketOffsets { get; set; }
    column_haunch_bracket_offsets: List[float] = field(default_factory=list)
    
    # C# Ref: Line 172 - public List<ShedBimColumn> ColumnStiffeners { get; set; }
    column_stiffeners: List['ShedBimColumn'] = field(default_factory=list)
    
    # C# Ref: Line 177 - public List<ShedBimColumnCompositeSection> ColumnCompositeSections { get; set; }
    column_composite_sections: List['ShedBimColumnCompositeSection'] = field(default_factory=list)
    
    # C# Ref: Line 179 - public List<ShedBimPair<ShedBimFlashing>> ColumnCoverFlashings { get; set; }
    column_cover_flashings: List['ShedBimPair'] = field(default_factory=list)
    
    # C# Ref: Line 188 - public List<ShedBimEaveBeam> EaveBeams { get; set; }
    eave_beams: List['ShedBimEaveBeam'] = field(default_factory=list)
    
    # C# Ref: Line 190 - public List<ShedBimSection> EaveTrimmers { get; set; }
    eave_trimmers: List['ShedBimSection'] = field(default_factory=list)
    
    # C# Ref: Line 192 - public List<ShedBimSection> KneeBraces { get; set; }
    knee_braces: List['ShedBimSection'] = field(default_factory=list)
    
    # C# Ref: Line 194 - public List<ShedBimLongitudinalBraceBay> LongitudinalBraceBays { get; set; }
    longitudinal_brace_bays: List['ShedBimLongitudinalBraceBay'] = field(default_factory=list)
    
    # C# Ref: Line 203 - public List<ShedBimEaveBeam> HeaderBeams { get; set; }
    header_beams: List['ShedBimEaveBeam'] = field(default_factory=list)
    
    # C# Ref: Line 205 - public List<ShedBimSection> FlybracingPurlins { get; set; }
    flybracing_purlins: List['ShedBimSection'] = field(default_factory=list)
    
    # C# Ref: Line 207 - public List<ShedBimSection> EavePurlinOverWallBracings { get; set; }
    eave_purlin_over_wall_bracings: List['ShedBimSection'] = field(default_factory=list)
    
    # C# Ref: Line 209 - public List<ShedBimFlashing> EaveFlashings { get; set; }
    eave_flashings: List['ShedBimFlashing'] = field(default_factory=list)
    
    # C# Ref: Line 211 - public List<ShedBimDownpipe> Downpipes { get; set; }
    downpipes: List['ShedBimDownpipe'] = field(default_factory=list)
    
    # C# Ref: Line 213 - public ShedBimWall Wall { get; set; }
    wall: Optional['ShedBimWall'] = None
    
    # C# Ref: Line 215 - public ShedBimOpening Opening { get; set; }
    opening: Optional['ShedBimOpening'] = None
    
    # C# Ref: Line 217 - public List<ShedBimBracket> ColumnEavePurlinBrackets { get; set; }
    column_eave_purlin_brackets: List['ShedBimBracket'] = field(default_factory=list)
    
    # C# Ref: Line 219 - public List<ShedBimBracket> ColumnEavePurlinCleats { get; set; }
    column_eave_purlin_cleats: List['ShedBimBracket'] = field(default_factory=list)
    
    # C# Ref: Line 221 - public List<ShedBimBracket> EavePurlinRafterBrackets { get; set; }
    eave_purlin_rafter_brackets: List['ShedBimBracket'] = field(default_factory=list)
    
    # C# Ref: Line 223 - public List<ShedBimBracket> EavePurlinSupportBrackets { get; set; }
    eave_purlin_support_brackets: List['ShedBimBracket'] = field(default_factory=list)
    
    # C# Ref: Line 230 - public List<ShedBimBracket> EavePurlinOverhangSupports { get; set; }
    eave_purlin_overhang_supports: List['ShedBimBracket'] = field(default_factory=list)
    
    # C# Ref: Line 232 - public List<ShedBimBracket> ColumnEaveTrimmerBrackets { get; set; }
    column_eave_trimmer_brackets: List['ShedBimBracket'] = field(default_factory=list)
    
    # C# Ref: Line 234 - public List<ShedBimBracket> RafterHaunchBrackets { get; set; }
    rafter_haunch_brackets: List['ShedBimBracket'] = field(default_factory=list)
    
    # C# Ref: Line 236 - public List<ShedBimBracket> KneeBraceBrackets { get; set; }
    knee_brace_brackets: List['ShedBimBracket'] = field(default_factory=list)
    
    # C# Ref: Line 238 - public List<ShedBimBracket> LongitudinalBraceBrackets { get; set; }
    longitudinal_brace_brackets: List['ShedBimBracket'] = field(default_factory=list)
    
    # C# Ref: Line 240 - public List<ShedBimBracket> RafterEavePurlinLaps { get; set; }
    rafter_eave_purlin_laps: List['ShedBimBracket'] = field(default_factory=list)
    
    # C# Ref: Line 242 - public List<ShedBimOutrigger> Outriggers { get; set; }
    outriggers: List['ShedBimOutrigger'] = field(default_factory=list)


# C# Ref: Lines 245-257 - public class ShedBimHeaderConnection
@dataclass
class ShedBimHeaderConnection:
    """Header beam connection details.
    
    C# Reference: ShedBim.cs lines 245-257
    """
    # C# Ref: Line 250 - public ShedBimPair<ShedBimBracket> Support { get; set; }
    support: Optional['ShedBimPair'] = None
    
    # C# Ref: Line 256 - public ShedBimBracket Slider { get; set; }
    slider: Optional['ShedBimBracket'] = None


# C# Ref: Lines 315-340 - public class ShedBimEnd
@dataclass
class ShedBimEnd:
    """End wall components.
    
    C# Reference: ShedBim.cs lines 315-340
    """
    # C# Ref: Line 317 - public int FrameNum { get; set; }
    frame_num: int = 0
    
    # C# Ref: Line 318 - public List<ShedBimMullion> Mullions { get; set; }
    mullions: List['ShedBimMullion'] = field(default_factory=list)
    
    # C# Ref: Line 320 - public ShedBimWall Wall { get; set; }
    wall: Optional['ShedBimWall'] = None
    
    # C# Ref: Line 322 - public List<ShedBimSection> HeaderBeams { get; set; }
    header_beams: List['ShedBimSection'] = field(default_factory=list)
    
    # C# Ref: Line 324 - public List<ShedBimSection> HeaderDroppers { get; set; }
    header_droppers: List['ShedBimSection'] = field(default_factory=list)
    
    # C# Ref: Line 326 - public List<ShedBimSection> HeaderBraces { get; set; }
    header_braces: List['ShedBimSection'] = field(default_factory=list)
    
    # C# Ref: Line 328 - public ShedBimBracket HeaderBeamBracket1 { get; set; }
    header_beam_bracket1: Optional['ShedBimBracket'] = None
    
    # C# Ref: Line 329 - public ShedBimBracket HeaderBeamBracket2 { get; set; }
    header_beam_bracket2: Optional['ShedBimBracket'] = None
    
    # C# Ref: Line 331 - public ShedBimBracket HeaderBeamColumnBracket1 { get; set; }
    header_beam_column_bracket1: Optional['ShedBimBracket'] = None
    
    # C# Ref: Line 332 - public ShedBimBracket HeaderBeamColumnBracket2 { get; set; }
    header_beam_column_bracket2: Optional['ShedBimBracket'] = None
    
    # C# Ref: Line 334 - public List<ShedBimBracket> HeaderBeamJoiners { get; set; }
    header_beam_joiners: List['ShedBimBracket'] = field(default_factory=list)
    
    # C# Ref: Line 336 - public List<ShedBimBracket> HeaderDropperBaseBrackets { get; set; }
    header_dropper_base_brackets: List['ShedBimBracket'] = field(default_factory=list)
    
    # C# Ref: Line 337 - public List<ShedBimBracket> HeaderDropperTopBrackets { get; set; }
    header_dropper_top_brackets: List['ShedBimBracket'] = field(default_factory=list)
    
    # C# Ref: Line 339 - public ShedBimFlashing HeaderFlashing { get; set; }
    header_flashing: Optional['ShedBimFlashing'] = None


# C# Ref: Lines 349-368 - public class ShedBimMezz
@dataclass
class ShedBimMezz:
    """Mezzanine floor structure.
    
    C# Reference: ShedBim.cs lines 349-368
    """
    # C# Ref: Line 351 - public List<ShedBimSection> Bearers { get; set; }
    bearers: List['ShedBimSection'] = field(default_factory=list)
    
    # C# Ref: Line 352 - public List<List<ShedBimSection>> Joists { get; set; }
    joists: List[List['ShedBimSection']] = field(default_factory=list)
    
    # C# Ref: Line 353 - public List<List<ShedBimColumn>> Posts { get; set; }
    posts: List[List['ShedBimColumn']] = field(default_factory=list)
    
    # C# Ref: Line 354 - public List<ShedBimMezzStairs> Stairs { get; set; }
    stairs: List['ShedBimMezzStairs'] = field(default_factory=list)


# C# Ref: Lines 370-383 - public class ShedBimRidgeDivider
@dataclass
class ShedBimRidgeDivider:
    """Ridge divider structure.
    
    C# Reference: ShedBim.cs lines 370-383
    """
    # C# Ref: Line 380 - public List<ShedBimColumn> RidgeMullions { get; set; }
    ridge_mullions: List['ShedBimColumn'] = field(default_factory=list)
    
    # C# Ref: Line 382 - public ShedBimWall Wall { get; set; }
    wall: Optional['ShedBimWall'] = None


# C# Ref: Lines 385-432 - public class ShedBimSlab
@dataclass
class ShedBimSlab:
    """Slab/foundation structure.
    
    C# Reference: ShedBim.cs lines 385-432
    """
    # C# Ref: Line 394 - public ShedBimSlabPiece Middle { get; set; }
    middle: Optional['ShedBimSlabPiece'] = None
    
    # C# Ref: Line 402 - public ShedBimSlabPiece Front { get; set; }
    front: Optional['ShedBimSlabPiece'] = None
    
    # C# Ref: Line 410 - public ShedBimSlabPiece Back { get; set; }
    back: Optional['ShedBimSlabPiece'] = None
    
    # C# Ref: Line 415 - public ShedBimSlabPiece GetFront()
    def get_front(self) -> Optional['ShedBimSlabPiece']:
        """Returns front if not null, otherwise middle."""
        return self.front if self.front else self.middle
    
    # C# Ref: Line 420 - public ShedBimSlabPiece GetBack()
    def get_back(self) -> Optional['ShedBimSlabPiece']:
        """Returns back if not null, otherwise middle."""
        return self.back if self.back else self.middle
    
    # C# Ref: Lines 422-431 - public List<ShedBimSlabPiece> GetPieces()
    def get_pieces(self) -> List['ShedBimSlabPiece']:
        """Get all non-null slab pieces."""
        pieces = []
        if self.front:
            pieces.append(self.front)
        if self.middle:
            pieces.append(self.middle)
        if self.back:
            pieces.append(self.back)
        return pieces


# C# Ref: Lines 434-462 - public class ShedBimSlabPiece
@dataclass
class ShedBimSlabPiece:
    """Individual slab piece.
    
    C# Reference: ShedBim.cs lines 434-462
    """
    # C# Ref: Line 439 - public double Thickness { get; set; }
    thickness: float = 0.0
    
    # C# Ref: Line 444 - public Vec3 BottomLeft { get; set; }
    bottom_left: Optional[Vec3] = None
    
    # C# Ref: Line 449 - public Vec3 TopLeft { get; set; }
    top_left: Optional[Vec3] = None
    
    # C# Ref: Line 454 - public Vec3 BottomRight { get; set; }
    bottom_right: Optional[Vec3] = None
    
    # C# Ref: Line 459 - public Vec3 TopRight { get; set; }
    top_right: Optional[Vec3] = None
    
    # C# Ref: Line 461 - public string Tag { get; set; }
    tag: str = ""
    
    # C# Ref: Line 436 - public bool IsSlab => Thickness > 0;
    @property
    def is_slab(self) -> bool:
        """Check if this is a slab (has thickness)."""
        return self.thickness > 0
    
    # C# Ref: Line 437 - public bool IsGround => !IsSlab;
    @property
    def is_ground(self) -> bool:
        """Check if this is ground (no thickness)."""
        return not self.is_slab


# C# Ref: Lines 464-485 - public class ShedBimWall
@dataclass
class ShedBimWall:
    """Wall structure with girts, cladding, and openings.
    
    C# Reference: ShedBim.cs lines 464-485
    """
    # C# Ref: Line 466 - public List<ShedBimWallGirtBay> GirtBays { get; set; }
    girt_bays: List['ShedBimWallGirtBay'] = field(default_factory=list)
    
    # C# Ref: Line 467 - public List<ShedBimSection> HorizBattens { get; set; }
    horiz_battens: List['ShedBimSection'] = field(default_factory=list)
    
    # C# Ref: Line 468 - public List<ShedBimCladding> Claddings { get; set; }
    claddings: List['ShedBimCladding'] = field(default_factory=list)
    
    # C# Ref: Line 469 - public List<ShedBimOpening> Openings { get; set; }
    openings: List['ShedBimOpening'] = field(default_factory=list)
    
    # C# Ref: Line 470 - public List<ShedBimOpenBay> OpenBays { get; set; }
    open_bays: List['ShedBimOpenBay'] = field(default_factory=list)
    
    # C# Ref: Line 471 - public List<ShedBimStrapBrace> XBraceBays { get; set; }
    x_brace_bays: List['ShedBimStrapBrace'] = field(default_factory=list)
    
    # C# Ref: Line 472 - public ShedBimStrapBrace FlyBracing { get; set; }
    fly_bracing: Optional['ShedBimStrapBrace'] = None
    
    # C# Ref: Line 473 - public List<ShedBimBracket> FlyBracingPrefolded { get; set; }
    fly_bracing_prefolded: List['ShedBimBracket'] = field(default_factory=list)
    
    # C# Ref: Line 478 - public ShedBimSection HorseyPlyStop { get; set; }
    horsey_ply_stop: Optional['ShedBimSection'] = None
    
    # C# Ref: Line 480 - public List<ShedBimFlashing> SliderFlashing { get; set; }
    slider_flashing: List['ShedBimFlashing'] = field(default_factory=list)
    
    # C# Ref: Line 482 - public List<ShedBimBracket> GirtBrackets { get; set; }
    girt_brackets: List['ShedBimBracket'] = field(default_factory=list)
    
    # C# Ref: Line 484 - public List<ShedBimFlashing> StarterChannels { get; set; }
    starter_channels: List['ShedBimFlashing'] = field(default_factory=list)


# C# Ref: Lines 494-511 - public class ShedBimRoof
@dataclass
class ShedBimRoof:
    """Roof structure with rafters, purlins, and cladding.
    
    C# Reference: ShedBim.cs lines 494-511
    """
    # C# Ref: Line 495 - public List<ShedBimSection> Rafters { get; set; }
    rafters: List['ShedBimSection'] = field(default_factory=list)
    
    # C# Ref: Line 497 - public List<ShedBimRoofPurlinBay> RoofPurlinBays { get; set; }
    roof_purlin_bays: List['ShedBimRoofPurlinBay'] = field(default_factory=list)
    
    # C# Ref: Line 499 - public ShedBimCladding Cladding { get; set; }
    cladding: Optional['ShedBimCladding'] = None
    
    # C# Ref: Line 501 - public List<ShedBimStrapBrace> XBraceBays { get; set; }
    x_brace_bays: List['ShedBimStrapBrace'] = field(default_factory=list)
    
    # C# Ref: Line 503 - public List<ShedBimFlashing> BargeFlashings { get; set; }
    barge_flashings: List['ShedBimFlashing'] = field(default_factory=list)
    
    # C# Ref: Line 505 - public List<ShedBimFlashing> BargeBottomFlashings { get; set; }
    barge_bottom_flashings: List['ShedBimFlashing'] = field(default_factory=list)
    
    # C# Ref: Line 507 - public ShedBimStrapBrace FlyBracing { get; set; }
    fly_bracing: Optional['ShedBimStrapBrace'] = None
    
    # C# Ref: Line 508 - public List<ShedBimBracket> FlyBracingPrefolded { get; set; }
    fly_bracing_prefolded: List['ShedBimBracket'] = field(default_factory=list)
    
    # C# Ref: Line 510 - public List<ShedBimRoofOpening> Openings { get; set; }
    openings: List['ShedBimRoofOpening'] = field(default_factory=list)


# C# Ref: Lines 775-782 - public class ShedBimComponentTag
@dataclass
class ShedBimComponentTag:
    """Component tagging information.
    
    C# Reference: ShedBim.cs lines 775-782
    """
    # C# Ref: Line 777 - public string Tag { get; set; }
    tag: str = ""
    
    # C# Ref: Line 778 - public string Member { get; set; }
    member: str = ""
    
    # C# Ref: Line 779 - public string Component { get; set; }
    component: str = ""
    
    # C# Ref: Line 780 - public string Spacing { get; set; }
    spacing: str = ""


# C# Ref: Lines 882-892 - public class ShedBimSamples
@dataclass
class ShedBimSamples:
    """Sample components for testing.
    
    C# Reference: ShedBim.cs lines 882-892
    """
    # C# Ref: Line 884 - public List<ShedBimSection> Sections { get; set; }
    sections: List['ShedBimSection'] = field(default_factory=list)
    
    # C# Ref: Line 885 - public List<ShedBimBracket> Brackets { get; set; }
    brackets: List['ShedBimBracket'] = field(default_factory=list)
    
    # C# Ref: Line 886 - public List<ShedBimFlashing> Flashings { get; set; }
    flashings: List['ShedBimFlashing'] = field(default_factory=list)
    
    # C# Ref: Line 887 - public List<ShedBimFooting> Footings { get; set; }
    footings: List['ShedBimFooting'] = field(default_factory=list)
    
    # C# Ref: Line 888 - public List<ShedBimSlab> Slabs { get; set; }
    slabs: List['ShedBimSlab'] = field(default_factory=list)
    
    # C# Ref: Line 889 - public List<ShedBimCladding> Claddings { get; set; }
    claddings: List['ShedBimCladding'] = field(default_factory=list)
    
    # C# Ref: Line 890 - public List<ShedBimDownpipe> Downpipes { get; set; }
    downpipes: List['ShedBimDownpipe'] = field(default_factory=list)
    
    # C# Ref: Line 891 - public object Debug { get; set; }
    debug: Optional[object] = None