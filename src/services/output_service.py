"""
Output generation service for managing file exports.

This service provides a unified interface for generating various
output formats from BIM models with file management capabilities.
"""

import asyncio
import shutil
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
import logging
import json

from src.bim.shed_bim import ShedB<PERSON>
from src.output import GLTFGenerator, DXFGenerator, IFCGenerator, OutputFormat, OutputResult


class OutputService:
    """
    Service for managing output file generation and storage.
    
    Handles generation of multiple formats, file storage, cleanup,
    and provides async interface for API integration.
    """
    
    def __init__(self, output_dir: Path = None, temp_dir: Path = None):
        """
        Initialize output service.
        
        Args:
            output_dir: Directory for permanent file storage
            temp_dir: Directory for temporary files (auto-cleaned)
        """
        self.output_dir = output_dir or Path("output")
        self.temp_dir = temp_dir or Path("temp/output")
        
        # Create directories
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize generators
        self.generators = {
            OutputFormat.GLTF: GLTFGenerator(self.temp_dir),
            OutputFormat.GLB: GLTFGenerator(self.temp_dir),
            OutputFormat.DXF: DXFGenerator(self.temp_dir),
            OutputFormat.IFC: IFCGenerator(self.temp_dir),
            OutputFormat.IFC4: IFCGenerator(self.temp_dir)
        }
        
        self.logger = logging.getLogger(__name__)
        
        # File tracking for cleanup
        self._temp_files: Dict[str, datetime] = {}
        
    async def generate_output(self, bim: ShedBim, filename: str, 
                            format: Union[OutputFormat, str], 
                            **options) -> OutputResult:
        """
        Generate output file asynchronously.
        
        Args:
            bim: BIM model to export
            filename: Base filename (without extension)
            format: Output format or format string
            **options: Format-specific options
            
        Returns:
            OutputResult with file information
        """
        # Convert string to enum if needed
        if isinstance(format, str):
            try:
                format = OutputFormat(format.lower())
            except ValueError:
                return OutputResult(
                    success=False,
                    format=None,
                    errors=[f"Unsupported format: {format}"]
                )
                
        # Get appropriate generator
        generator = self.generators.get(format)
        if not generator:
            return OutputResult(
                success=False,
                format=format,
                errors=[f"No generator available for {format.value}"]
            )
            
        # Run generation in thread pool
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            None,
            generator.generate,
            bim,
            filename,
            options
        )
        
        # Track temporary file
        if result.success and result.file_path:
            self._temp_files[str(result.file_path)] = datetime.now()
            
        return result
        
    async def generate_multiple(self, bim: ShedBim, filename: str,
                              formats: List[Union[OutputFormat, str]],
                              **options) -> Dict[str, OutputResult]:
        """
        Generate multiple output formats concurrently.
        
        Args:
            bim: BIM model to export
            filename: Base filename
            formats: List of formats to generate
            **options: Shared options for all formats
            
        Returns:
            Dictionary mapping format to result
        """
        tasks = []
        for format in formats:
            task = self.generate_output(bim, filename, format, **options)
            tasks.append(task)
            
        results = await asyncio.gather(*tasks)
        
        # Map results to formats
        return {
            format.value if isinstance(format, OutputFormat) else format: result
            for format, result in zip(formats, results)
        }
        
    def move_to_permanent(self, temp_path: Path, permanent_name: str = None) -> Path:
        """
        Move file from temp to permanent storage.
        
        Args:
            temp_path: Path to temporary file
            permanent_name: Optional new filename
            
        Returns:
            Path to permanent file
        """
        if not temp_path.exists():
            raise FileNotFoundError(f"Temporary file not found: {temp_path}")
            
        # Determine permanent path
        if permanent_name:
            permanent_path = self.output_dir / permanent_name
        else:
            permanent_path = self.output_dir / temp_path.name
            
        # Move file
        shutil.move(str(temp_path), str(permanent_path))
        
        # Remove from temp tracking
        self._temp_files.pop(str(temp_path), None)
        
        return permanent_path
        
    async def cleanup_temp_files(self, max_age_hours: int = 24):
        """
        Clean up old temporary files.
        
        Args:
            max_age_hours: Maximum age in hours before deletion
        """
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        files_to_remove = []
        
        # Find old files
        for file_path, created_time in self._temp_files.items():
            if created_time < cutoff_time:
                files_to_remove.append(file_path)
                
        # Remove files
        for file_path in files_to_remove:
            try:
                Path(file_path).unlink()
                self._temp_files.pop(file_path)
                self.logger.info(f"Cleaned up temp file: {file_path}")
            except Exception as e:
                self.logger.error(f"Failed to clean up {file_path}: {e}")
                
    def get_available_formats(self) -> List[str]:
        """Get list of available output formats."""
        return [format.value for format in OutputFormat]
        
    def validate_format(self, format: str) -> bool:
        """Check if format is supported."""
        try:
            OutputFormat(format.lower())
            return True
        except ValueError:
            return False
            
    async def generate_preview(self, bim: ShedBim, size: str = "medium") -> OutputResult:
        """
        Generate a preview file optimized for web display.
        
        Args:
            bim: BIM model
            size: Preview size ('small', 'medium', 'large')
            
        Returns:
            OutputResult with GLB file optimized for web
        """
        # Size presets
        sizes = {
            "small": {"max_vertices": 10000, "texture_size": 512},
            "medium": {"max_vertices": 50000, "texture_size": 1024},
            "large": {"max_vertices": 200000, "texture_size": 2048}
        }
        
        options = sizes.get(size, sizes["medium"])
        options["binary"] = True  # Use GLB for web
        options["optimize"] = True  # Enable optimization
        
        filename = f"preview_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        return await self.generate_output(bim, filename, OutputFormat.GLB, **options)
        
    def create_export_manifest(self, results: Dict[str, OutputResult]) -> Dict[str, Any]:
        """
        Create manifest of exported files.
        
        Args:
            results: Dictionary of format to OutputResult
            
        Returns:
            Manifest dictionary with file information
        """
        manifest = {
            "timestamp": datetime.now().isoformat(),
            "files": [],
            "errors": []
        }
        
        for format, result in results.items():
            if result.success:
                manifest["files"].append({
                    "format": format,
                    "path": str(result.file_path),
                    "size": result.file_size,
                    "metadata": result.metadata
                })
            else:
                manifest["errors"].append({
                    "format": format,
                    "errors": result.errors
                })
                
        return manifest
        
    async def export_with_manifest(self, bim: ShedBim, project_name: str,
                                 formats: List[str]) -> Path:
        """
        Export multiple formats and create manifest file.
        
        Args:
            bim: BIM model
            project_name: Name for the project
            formats: List of formats to export
            
        Returns:
            Path to manifest file
        """
        # Generate all formats
        results = await self.generate_multiple(bim, project_name, formats)
        
        # Create manifest
        manifest = self.create_export_manifest(results)
        
        # Save manifest
        manifest_path = self.temp_dir / f"{project_name}_manifest.json"
        with open(manifest_path, 'w') as f:
            json.dump(manifest, f, indent=2)
            
        return manifest_path


class OutputManager:
    """
    Manager for output service with additional business logic.
    
    Provides higher-level operations like batch processing,
    format conversion, and integration with other services.
    """
    
    def __init__(self, output_service: OutputService = None):
        """Initialize output manager."""
        self.service = output_service or OutputService()
        self.logger = logging.getLogger(__name__)
        
    async def process_order(self, bim: ShedBim, order_id: str,
                          customer_formats: List[str]) -> Dict[str, Any]:
        """
        Process a customer order for multiple file formats.
        
        Args:
            bim: BIM model
            order_id: Unique order identifier
            customer_formats: Requested formats
            
        Returns:
            Order processing result
        """
        start_time = datetime.now()
        
        # Validate formats
        valid_formats = []
        invalid_formats = []
        
        for format in customer_formats:
            if self.service.validate_format(format):
                valid_formats.append(format)
            else:
                invalid_formats.append(format)
                
        # Generate valid formats
        results = {}
        if valid_formats:
            results = await self.service.generate_multiple(
                bim, f"order_{order_id}", valid_formats
            )
            
        # Move successful files to permanent storage
        permanent_files = {}
        for format, result in results.items():
            if result.success and result.file_path:
                perm_path = self.service.move_to_permanent(
                    result.file_path,
                    f"{order_id}_{format}.{format}"
                )
                permanent_files[format] = str(perm_path)
                
        # Create order result
        return {
            "order_id": order_id,
            "status": "completed" if not invalid_formats else "partial",
            "processing_time": (datetime.now() - start_time).total_seconds(),
            "files": permanent_files,
            "invalid_formats": invalid_formats,
            "timestamp": datetime.now().isoformat()
        }
        
    async def create_download_package(self, bim: ShedBim, package_name: str,
                                    formats: List[str] = None) -> Path:
        """
        Create a ZIP package with multiple formats.
        
        Args:
            bim: BIM model
            package_name: Name for the package
            formats: Formats to include (default: all common)
            
        Returns:
            Path to ZIP file
        """
        if formats is None:
            formats = ["gltf", "dxf", "ifc"]
            
        # Generate files
        results = await self.service.generate_multiple(bim, package_name, formats)
        
        # Create ZIP
        zip_path = self.service.temp_dir / f"{package_name}.zip"
        
        import zipfile
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zf:
            # Add generated files
            for format, result in results.items():
                if result.success and result.file_path:
                    zf.write(result.file_path, result.file_path.name)
                    
            # Add manifest
            manifest = self.service.create_export_manifest(results)
            zf.writestr("manifest.json", json.dumps(manifest, indent=2))
            
            # Add README
            readme_content = self._create_readme(package_name, results)
            zf.writestr("README.txt", readme_content)
            
        return zip_path
        
    def _create_readme(self, package_name: str, results: Dict[str, OutputResult]) -> str:
        """Create README content for download package."""
        content = [
            f"BIM Export Package: {package_name}",
            f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "Included Files:",
            ""
        ]
        
        for format, result in results.items():
            if result.success:
                content.append(f"- {format.upper()}: {result.file_path.name}")
                if result.metadata:
                    for key, value in result.metadata.items():
                        content.append(f"  {key}: {value}")
                        
        content.extend([
            "",
            "File Formats:",
            "- GLTF: 3D model for web visualization",
            "- DXF: CAD drawing for AutoCAD",
            "- IFC: BIM data for interoperability",
            "",
            "Generated by BIM Backend System"
        ])
        
        return "\n".join(content)