"""
Mesh Converter for Carport Components

Converts carport structural elements into triangulated meshes for GLB export.
"""

import numpy as np
from typing import List, Tuple, Dict, Any
from src.geometry.primitives import Vec3


class CarportMeshConverter:
    """
    Converts carport components into mesh data suitable for GLB export.
    
    This class handles the conversion of various structural elements
    (columns, rafters, purlins, etc.) into triangulated mesh geometry.
    """
    
    @staticmethod
    def triangulate_polygon(vertices: List[Vec3]) -> List[Tuple[int, int, int]]:
        """
        Simple triangulation of a convex polygon.
        
        Args:
            vertices: List of vertices forming the polygon
            
        Returns:
            List of triangular faces as index tuples
        """
        triangles = []
        n = len(vertices)
        
        if n < 3:
            return triangles
            
        # Fan triangulation from first vertex
        for i in range(1, n - 1):
            triangles.append((0, i, i + 1))
            
        return triangles
    
    @staticmethod
    def create_mesh_from_extrusion(profile: List[Vec3], path: List[Vec3],
                                  closed_profile: bool = True) -> <PERSON><PERSON>[np.n<PERSON><PERSON>, np.ndar<PERSON>]:
        """
        Create a mesh by extruding a profile along a path.
        
        Args:
            profile: 2D profile points (in XY plane)
            path: 3D path points for extrusion
            closed_profile: Whether the profile forms a closed loop
            
        Returns:
            Tuple of (vertices, indices) as numpy arrays
        """
        vertices = []
        indices = []
        
        num_profile_points = len(profile)
        num_path_points = len(path)
        
        # Generate vertices
        for path_idx, path_point in enumerate(path):
            for prof_point in profile:
                # Transform profile point to path position
                vertex = Vec3(
                    path_point.x + prof_point.x,
                    path_point.y + prof_point.y,
                    path_point.z + prof_point.z
                )
                vertices.append([vertex.x, vertex.y, vertex.z])
        
        # Generate indices for quad faces
        for i in range(num_path_points - 1):
            for j in range(num_profile_points):
                next_j = (j + 1) % num_profile_points if closed_profile else j + 1
                
                if next_j < num_profile_points:
                    # Current ring vertices
                    v0 = i * num_profile_points + j
                    v1 = i * num_profile_points + next_j
                    
                    # Next ring vertices
                    v2 = (i + 1) * num_profile_points + j
                    v3 = (i + 1) * num_profile_points + next_j
                    
                    # Create two triangles for the quad
                    indices.extend([v0, v1, v2])
                    indices.extend([v1, v3, v2])
        
        # Add end caps if profile is closed
        if closed_profile and num_path_points >= 2:
            # Start cap
            cap_indices = CarportMeshConverter.triangulate_polygon(
                profile[:num_profile_points]
            )
            for tri in cap_indices:
                indices.extend([tri[0], tri[2], tri[1]])  # Reverse winding
                
            # End cap
            offset = (num_path_points - 1) * num_profile_points
            for tri in cap_indices:
                indices.extend([
                    offset + tri[0],
                    offset + tri[1], 
                    offset + tri[2]
                ])
        
        return np.array(vertices, dtype=np.float32), np.array(indices, dtype=np.uint16)
    
    @staticmethod
    def optimize_mesh(vertices: np.ndarray, indices: np.ndarray,
                     tolerance: float = 0.001) -> Tuple[np.ndarray, np.ndarray]:
        """
        Optimize mesh by removing duplicate vertices.
        
        Args:
            vertices: Vertex array
            indices: Index array
            tolerance: Distance tolerance for merging vertices
            
        Returns:
            Optimized (vertices, indices) arrays
        """
        unique_vertices = []
        vertex_map = {}
        
        for i, vertex in enumerate(vertices):
            # Check if this vertex is close to any existing unique vertex
            found = False
            for j, unique_v in enumerate(unique_vertices):
                if np.linalg.norm(vertex - unique_v) < tolerance:
                    vertex_map[i] = j
                    found = True
                    break
                    
            if not found:
                vertex_map[i] = len(unique_vertices)
                unique_vertices.append(vertex)
        
        # Remap indices
        new_indices = [vertex_map[idx] for idx in indices]
        
        return np.array(unique_vertices, dtype=np.float32), np.array(new_indices, dtype=np.uint16)
    
    @staticmethod
    def calculate_normals(vertices: np.ndarray, indices: np.ndarray) -> np.ndarray:
        """
        Calculate vertex normals for smooth shading.
        
        Args:
            vertices: Vertex positions
            indices: Triangle indices
            
        Returns:
            Array of vertex normals
        """
        normals = np.zeros_like(vertices)
        
        # Calculate face normals and accumulate at vertices
        for i in range(0, len(indices), 3):
            i0, i1, i2 = indices[i:i+3]
            v0, v1, v2 = vertices[i0], vertices[i1], vertices[i2]
            
            # Calculate face normal
            edge1 = v1 - v0
            edge2 = v2 - v0
            face_normal = np.cross(edge1, edge2)
            
            # Add to vertex normals
            normals[i0] += face_normal
            normals[i1] += face_normal
            normals[i2] += face_normal
        
        # Normalize
        lengths = np.linalg.norm(normals, axis=1)
        normals[lengths > 0] /= lengths[lengths > 0, np.newaxis]
        
        return normals
    
    @staticmethod
    def create_uv_coordinates(vertices: np.ndarray, profile_count: int, 
                            path_count: int) -> np.ndarray:
        """
        Generate UV coordinates for extruded meshes.
        
        Args:
            vertices: Vertex array
            profile_count: Number of profile points
            path_count: Number of path points
            
        Returns:
            UV coordinate array
        """
        uvs = np.zeros((len(vertices), 2), dtype=np.float32)
        
        for i in range(path_count):
            for j in range(profile_count):
                idx = i * profile_count + j
                if idx < len(vertices):
                    uvs[idx] = [j / (profile_count - 1), i / (path_count - 1)]
        
        return uvs