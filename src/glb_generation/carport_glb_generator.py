"""
Carport GLB Generator - Aligned with C# ShedGltf Implementation

This module generates GLB files for carport structures with proper 3D geometry,
materials, and metadata.
"""

import json
import struct
import numpy as np
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import logging

from src.business.structure_builder import CarportBuilder
from src.business.building_input import BuildingInput, CarportRoofType
from src.materials.profiles import CSection, SHS, TopHat
from src.geometry.primitives import Vec3
from src.geometry.matrix import Mat4

logger = logging.getLogger(__name__)


class CarportGLBGenerator:
    """
    Generates GLB files for carport structures.
    
    This class is aligned with the C# ShedGltf implementation and produces
    binary GLTF files that can be viewed in any GLTF-compatible viewer.
    """
    
    # GLTF constants
    GLTF_VERSION = "2.0"
    GLTF_MAGIC = 0x46546C67  # 'glTF' in ASCII
    GLTF_VERSION_NUMBER = 2
    CHUNK_TYPE_JSON = 0x4E4F534A  # 'JSON' in ASCII
    CHUNK_TYPE_BIN = 0x004E4942   # 'BIN\0' in ASCII
    
    # Default materials (matching C# implementation)
    MATERIAL_COLORS = {
        "steel": {"r": 0.7, "g": 0.7, "b": 0.75, "a": 1.0},  # Steel gray
        "concrete": {"r": 0.6, "g": 0.6, "b": 0.6, "a": 1.0},  # Concrete gray
        "default": {"r": 0.8, "g": 0.8, "b": 0.8, "a": 1.0}   # Light gray
    }
    
    def __init__(self):
        """Initialize the GLB generator."""
        self.buffer_data = bytearray()
        self.accessors = []
        self.buffer_views = []
        self.meshes = []
        self.nodes = []
        self.materials = []
        self.scenes = []
        
    def generate_carport_glb(
        self,
        span: float,
        length: float, 
        height: float,
        roof_type: CarportRoofType,
        pitch: float = 15,
        bays: int = 3,
        overhang: float = 0,
        slab: bool = True,
        slab_thickness: float = 100,
        output_path: Optional[str] = None,
        quality: str = "medium"
    ) -> Path:
        """
        Generate a GLB file for a carport.
        
        Args:
            span: Width of carport in mm
            length: Length of carport in mm
            height: Height of carport in mm
            roof_type: Type of roof (FLAT, GABLE, AWNING, ATTACHED_AWNING)
            pitch: Roof pitch in degrees
            bays: Number of bays
            overhang: Roof overhang in mm
            slab: Include concrete slab
            slab_thickness: Thickness of slab in mm
            output_path: Output file path
            quality: Mesh quality (low, medium, high)
            
        Returns:
            Path to generated GLB file
        """
        # Reset state
        self._reset()
        
        # Create building input
        building_input = BuildingInput()
        building_input.span = span
        building_input.length = length
        building_input.height = height
        building_input.roof_type = roof_type.value
        building_input.pitch = pitch
        building_input.bays = bays
        building_input.overhang = overhang
        building_input.slab = slab
        building_input.slab_thickness = slab_thickness
        
        # Generate carport structure
        carport = CarportBuilder.create_carport(building_input)
        
        # Create materials
        steel_material_idx = self._add_material("Steel", self.MATERIAL_COLORS["steel"])
        concrete_material_idx = self._add_material("Concrete", self.MATERIAL_COLORS["concrete"])
        
        # Create root node
        root_node_idx = len(self.nodes)
        self.nodes.append({
            "name": "Carport",
            "children": []
        })
        
        # Add structural elements
        if slab and carport.slabs:
            slab_node_idx = self._add_slab(carport.slabs[0], concrete_material_idx)
            self.nodes[root_node_idx]["children"].append(slab_node_idx)
            
        # Add footings
        for footing in carport.footings:
            footing_node_idx = self._add_footing(footing, concrete_material_idx)
            self.nodes[root_node_idx]["children"].append(footing_node_idx)
            
        # Add columns
        for column in carport.columns:
            column_node_idx = self._add_column(column, steel_material_idx)
            self.nodes[root_node_idx]["children"].append(column_node_idx)
            
        # Add rafters
        for rafter in carport.rafters:
            rafter_node_idx = self._add_rafter(rafter, steel_material_idx)
            self.nodes[root_node_idx]["children"].append(rafter_node_idx)
            
        # Add purlins
        for purlin in carport.purlins:
            purlin_node_idx = self._add_purlin(purlin, steel_material_idx)
            self.nodes[root_node_idx]["children"].append(purlin_node_idx)
            
        # Add eave purlins
        for eave_purlin in carport.eave_purlins:
            eave_purlin_node_idx = self._add_eave_purlin(eave_purlin, steel_material_idx)
            self.nodes[root_node_idx]["children"].append(eave_purlin_node_idx)
        
        # Create scene
        self.scenes.append({
            "name": f"Carport_{roof_type.value}",
            "nodes": [root_node_idx]
        })
        
        # Build GLTF structure
        gltf = self._build_gltf_json()
        
        # Write GLB file
        if output_path is None:
            output_path = f"carport_{roof_type.value.lower()}_{int(span)}x{int(length)}.glb"
        
        output_file = Path(output_path)
        self._write_glb(gltf, output_file)
        
        logger.info(f"Generated GLB file: {output_file}")
        return output_file
    
    def _reset(self):
        """Reset internal state."""
        self.buffer_data = bytearray()
        self.accessors = []
        self.buffer_views = []
        self.meshes = []
        self.nodes = []
        self.materials = []
        self.scenes = []
        
    def _add_material(self, name: str, color: Dict[str, float]) -> int:
        """Add a material and return its index."""
        material = {
            "name": name,
            "pbrMetallicRoughness": {
                "baseColorFactor": [color["r"], color["g"], color["b"], color["a"]],
                "metallicFactor": 0.3,
                "roughnessFactor": 0.7
            },
            "doubleSided": True
        }
        self.materials.append(material)
        return len(self.materials) - 1
    
    def _add_slab(self, slab: Any, material_idx: int) -> int:
        """Add a slab mesh and return node index."""
        # Create box geometry for slab
        vertices, indices = self._create_box_geometry(
            slab.width, slab.length, slab.thickness
        )
        
        # Adjust position (slab is at ground level)
        center_x = slab.width / 2
        center_y = slab.length / 2
        center_z = -slab.thickness / 2
        
        mesh_idx = self._add_mesh(
            vertices, indices, material_idx, "Slab"
        )
        
        node_idx = len(self.nodes)
        self.nodes.append({
            "name": "Slab",
            "mesh": mesh_idx,
            "translation": [center_x / 1000, center_z / 1000, center_y / 1000]  # Convert to meters
        })
        
        return node_idx
    
    def _add_footing(self, footing: Any, material_idx: int) -> int:
        """Add a footing mesh and return node index."""
        # Create cylinder or box based on footing type
        if hasattr(footing, 'diameter'):
            # Bored footing (cylinder)
            vertices, indices = self._create_cylinder_geometry(
                footing.diameter / 2, footing.depth, 16
            )
        else:
            # Block footing (box)
            vertices, indices = self._create_box_geometry(
                footing.width, footing.width, footing.depth
            )
        
        mesh_idx = self._add_mesh(
            vertices, indices, material_idx, f"Footing_{footing.id}"
        )
        
        node_idx = len(self.nodes)
        self.nodes.append({
            "name": f"Footing_{footing.id}",
            "mesh": mesh_idx,
            "translation": [
                footing.position.x / 1000,
                (footing.position.z - footing.depth / 2) / 1000,
                footing.position.y / 1000
            ]
        })
        
        return node_idx
    
    def _add_column(self, column: Any, material_idx: int) -> int:
        """Add a column mesh and return node index."""
        # Get column profile (SHS)
        profile = column.material
        
        # Create extruded profile geometry
        vertices, indices = self._create_shs_geometry(
            profile.width, profile.height, profile.thickness, column.length
        )
        
        mesh_idx = self._add_mesh(
            vertices, indices, material_idx, f"Column_{column.id}"
        )
        
        # Calculate center position
        center_x = (column.start_position.x + column.end_position.x) / 2
        center_y = (column.start_position.y + column.end_position.y) / 2
        center_z = (column.start_position.z + column.end_position.z) / 2
        
        node_idx = len(self.nodes)
        self.nodes.append({
            "name": f"Column_{column.id}",
            "mesh": mesh_idx,
            "translation": [center_x / 1000, center_z / 1000, center_y / 1000]
        })
        
        return node_idx
    
    def _add_rafter(self, rafter: Any, material_idx: int) -> int:
        """Add a rafter mesh and return node index."""
        # Get rafter profile (C-section)
        profile = rafter.material
        
        # Create C-section geometry
        vertices, indices = self._create_c_section_geometry(
            profile.depth, profile.flange, profile.thickness, 
            profile.lip, rafter.length
        )
        
        mesh_idx = self._add_mesh(
            vertices, indices, material_idx, f"Rafter_{rafter.id}"
        )
        
        # Calculate position and rotation
        center_x = (rafter.start_position.x + rafter.end_position.x) / 2
        center_y = (rafter.start_position.y + rafter.end_position.y) / 2  
        center_z = (rafter.start_position.z + rafter.end_position.z) / 2
        
        # Calculate rotation from direction
        dx = rafter.end_position.x - rafter.start_position.x
        dy = rafter.end_position.y - rafter.start_position.y
        dz = rafter.end_position.z - rafter.start_position.z
        
        node_idx = len(self.nodes)
        node = {
            "name": f"Rafter_{rafter.id}",
            "mesh": mesh_idx,
            "translation": [center_x / 1000, center_z / 1000, center_y / 1000]
        }
        
        # Add rotation if needed
        if abs(dz) > 0.001:  # Sloped rafter
            pitch_angle = np.arctan2(dz, dx)
            node["rotation"] = self._euler_to_quaternion(0, -pitch_angle, 0)
            
        self.nodes.append(node)
        return node_idx
    
    def _add_purlin(self, purlin: Any, material_idx: int) -> int:
        """Add a purlin mesh and return node index."""
        profile = purlin.material
        
        # Create geometry based on profile type
        if isinstance(profile, TopHat):
            vertices, indices = self._create_tophat_geometry(
                profile.base_width, profile.top_width, profile.depth,
                profile.thickness, purlin.length
            )
        else:
            # C-section
            vertices, indices = self._create_c_section_geometry(
                profile.depth, profile.flange, profile.thickness,
                profile.lip, purlin.length
            )
        
        mesh_idx = self._add_mesh(
            vertices, indices, material_idx, f"Purlin_{purlin.id}"
        )
        
        center_x = (purlin.start_position.x + purlin.end_position.x) / 2
        center_y = (purlin.start_position.y + purlin.end_position.y) / 2
        center_z = (purlin.start_position.z + purlin.end_position.z) / 2
        
        node_idx = len(self.nodes)
        self.nodes.append({
            "name": f"Purlin_{purlin.id}",
            "mesh": mesh_idx,
            "translation": [center_x / 1000, center_z / 1000, center_y / 1000]
        })
        
        return node_idx
    
    def _add_eave_purlin(self, eave_purlin: Any, material_idx: int) -> int:
        """Add an eave purlin mesh and return node index."""
        return self._add_purlin(eave_purlin, material_idx)
    
    def _create_box_geometry(self, width: float, length: float, height: float) -> Tuple[np.ndarray, np.ndarray]:
        """Create box geometry vertices and indices."""
        # Convert to half dimensions
        hw = width / 2000  # Convert to meters
        hl = length / 2000
        hh = height / 2000
        
        # Define vertices (8 corners of box)
        vertices = np.array([
            [-hw, -hh, -hl], [hw, -hh, -hl], [hw, hh, -hl], [-hw, hh, -hl],  # Back
            [-hw, -hh, hl], [hw, -hh, hl], [hw, hh, hl], [-hw, hh, hl]       # Front
        ], dtype=np.float32)
        
        # Define triangular faces (12 triangles, 2 per face)
        indices = np.array([
            0, 1, 2, 0, 2, 3,  # Back
            4, 7, 6, 4, 6, 5,  # Front
            0, 4, 5, 0, 5, 1,  # Bottom
            2, 6, 7, 2, 7, 3,  # Top
            0, 3, 7, 0, 7, 4,  # Left
            1, 5, 6, 1, 6, 2   # Right
        ], dtype=np.uint16)
        
        return vertices, indices
    
    def _create_cylinder_geometry(self, radius: float, height: float, segments: int) -> Tuple[np.ndarray, np.ndarray]:
        """Create cylinder geometry vertices and indices."""
        vertices = []
        indices = []
        
        r = radius / 1000  # Convert to meters
        h = height / 1000
        
        # Create vertices
        for i in range(segments):
            angle = 2 * np.pi * i / segments
            x = r * np.cos(angle)
            z = r * np.sin(angle)
            vertices.append([x, -h/2, z])  # Bottom
            vertices.append([x, h/2, z])    # Top
        
        # Add center vertices
        vertices.append([0, -h/2, 0])  # Bottom center
        vertices.append([0, h/2, 0])   # Top center
        
        bottom_center = len(vertices) - 2
        top_center = len(vertices) - 1
        
        # Create side faces
        for i in range(segments):
            next_i = (i + 1) % segments
            # Side quad as two triangles
            indices.extend([i*2, next_i*2, next_i*2+1])
            indices.extend([i*2, next_i*2+1, i*2+1])
            
            # Bottom triangle
            indices.extend([bottom_center, next_i*2, i*2])
            
            # Top triangle  
            indices.extend([top_center, i*2+1, next_i*2+1])
        
        return np.array(vertices, dtype=np.float32), np.array(indices, dtype=np.uint16)
    
    def _create_shs_geometry(self, width: float, height: float, thickness: float, length: float) -> Tuple[np.ndarray, np.ndarray]:
        """Create Square Hollow Section geometry."""
        # Convert to meters
        w = width / 1000
        h = height / 1000
        t = thickness / 1000
        l = length / 1000
        
        # Outer and inner dimensions
        wo = w / 2
        ho = h / 2
        wi = wo - t
        hi = ho - t
        
        # Define vertices for hollow box
        vertices = []
        
        # Outer vertices (front and back)
        for y in [-l/2, l/2]:
            vertices.extend([
                [-wo, -ho, y], [wo, -ho, y], [wo, ho, y], [-wo, ho, y]
            ])
        
        # Inner vertices (front and back)
        for y in [-l/2, l/2]:
            vertices.extend([
                [-wi, -hi, y], [wi, -hi, y], [wi, hi, y], [-wi, hi, y]
            ])
        
        vertices = np.array(vertices, dtype=np.float32)
        
        # Create faces
        indices = []
        
        # Outer faces
        indices.extend([0, 1, 5, 0, 5, 4])  # Bottom
        indices.extend([2, 3, 7, 2, 7, 6])  # Top
        indices.extend([0, 4, 7, 0, 7, 3])  # Left
        indices.extend([1, 2, 6, 1, 6, 5])  # Right
        
        # Inner faces
        indices.extend([8, 12, 13, 8, 13, 9])   # Bottom
        indices.extend([10, 14, 15, 10, 15, 11]) # Top
        indices.extend([8, 11, 15, 8, 15, 12])   # Left
        indices.extend([9, 13, 14, 9, 14, 10])   # Right
        
        # End caps
        for i in range(4):
            next_i = (i + 1) % 4
            # Front cap
            indices.extend([i, next_i, next_i+8])
            indices.extend([i, next_i+8, i+8])
            # Back cap
            indices.extend([i+4, i+12, next_i+12])
            indices.extend([i+4, next_i+12, next_i+4])
        
        return vertices, np.array(indices, dtype=np.uint16)
    
    def _create_c_section_geometry(self, depth: float, flange: float, thickness: float, 
                                   lip: float, length: float) -> Tuple[np.ndarray, np.ndarray]:
        """Create C-section geometry."""
        # Convert to meters
        d = depth / 1000
        f = flange / 1000
        t = thickness / 1000
        lip_size = lip / 1000
        l = length / 1000
        
        # Define profile points
        points = [
            [0, -d/2],           # Bottom web
            [0, d/2],            # Top web
            [f, d/2],            # Top flange end
            [f, d/2 - lip_size], # Top lip
            [t, d/2 - lip_size], # Top lip inner
            [t, -d/2 + lip_size],# Bottom lip inner
            [f, -d/2 + lip_size],# Bottom lip
            [f, -d/2],           # Bottom flange end
        ]
        
        # Create vertices by extruding profile
        vertices = []
        for y in [-l/2, l/2]:
            for x, z in points:
                vertices.append([x, z, y])
        
        vertices = np.array(vertices, dtype=np.float32)
        
        # Create faces
        indices = []
        num_points = len(points)
        
        # Side faces
        for i in range(num_points):
            next_i = (i + 1) % num_points
            indices.extend([i, next_i, next_i + num_points])
            indices.extend([i, next_i + num_points, i + num_points])
        
        # End caps
        # Front cap
        for i in range(2, num_points):
            indices.extend([0, i-1, i])
        # Back cap
        for i in range(2, num_points):
            indices.extend([num_points, num_points + i, num_points + i-1])
        
        return vertices, np.array(indices, dtype=np.uint16)
    
    def _create_tophat_geometry(self, base_width: float, top_width: float, depth: float,
                                thickness: float, length: float) -> Tuple[np.ndarray, np.ndarray]:
        """Create TopHat section geometry."""
        # Convert to meters
        bw = base_width / 1000
        tw = top_width / 1000
        d = depth / 1000
        t = thickness / 1000
        l = length / 1000
        
        # Calculate side slope
        side_offset = (bw - tw) / 2
        
        # Define profile points
        points = [
            [-bw/2, 0],             # Bottom left
            [-tw/2, d],             # Top left
            [tw/2, d],              # Top right
            [bw/2, 0],              # Bottom right
            [bw/2 - t, t],          # Inner bottom right
            [tw/2 - t, d - t],      # Inner top right
            [-tw/2 + t, d - t],     # Inner top left
            [-bw/2 + t, t],         # Inner bottom left
        ]
        
        # Create vertices
        vertices = []
        for y in [-l/2, l/2]:
            for x, z in points:
                vertices.append([x, z, y])
        
        vertices = np.array(vertices, dtype=np.float32)
        
        # Create faces
        indices = []
        num_points = len(points)
        
        # Side faces
        for i in range(num_points):
            next_i = (i + 1) % num_points
            if i < 4:  # Outer faces
                indices.extend([i, next_i, next_i + num_points])
                indices.extend([i, next_i + num_points, i + num_points])
            else:  # Inner faces
                indices.extend([i, i + num_points, next_i + num_points])
                indices.extend([i, next_i + num_points, next_i])
        
        # End caps
        # Front cap
        for i in range(4):
            next_i = (i + 1) % 4
            indices.extend([i, next_i, 4 + next_i])
            indices.extend([i, 4 + next_i, 4 + i])
        
        # Back cap  
        for i in range(4):
            next_i = (i + 1) % 4
            indices.extend([num_points + i, num_points + 4 + i, num_points + 4 + next_i])
            indices.extend([num_points + i, num_points + 4 + next_i, num_points + next_i])
        
        return vertices, np.array(indices, dtype=np.uint16)
    
    def _euler_to_quaternion(self, roll: float, pitch: float, yaw: float) -> List[float]:
        """Convert Euler angles to quaternion."""
        cy = np.cos(yaw * 0.5)
        sy = np.sin(yaw * 0.5)
        cp = np.cos(pitch * 0.5)
        sp = np.sin(pitch * 0.5)
        cr = np.cos(roll * 0.5)
        sr = np.sin(roll * 0.5)
        
        return [
            sr * cp * cy - cr * sp * sy,  # x
            cr * sp * cy + sr * cp * sy,  # y
            cr * cp * sy - sr * sp * cy,  # z
            cr * cp * cy + sr * sp * sy   # w
        ]
    
    def _add_mesh(self, vertices: np.ndarray, indices: np.ndarray, 
                  material_idx: int, name: str) -> int:
        """Add a mesh to the GLB structure."""
        # Add vertex data to buffer
        vertex_offset = len(self.buffer_data)
        vertex_bytes = vertices.tobytes()
        self.buffer_data.extend(vertex_bytes)
        
        # Add index data to buffer
        index_offset = len(self.buffer_data)
        index_bytes = indices.tobytes()
        self.buffer_data.extend(index_bytes)
        
        # Create buffer views
        vertex_buffer_view_idx = len(self.buffer_views)
        self.buffer_views.append({
            "buffer": 0,
            "byteOffset": vertex_offset,
            "byteLength": len(vertex_bytes),
            "target": 34962  # ARRAY_BUFFER
        })
        
        index_buffer_view_idx = len(self.buffer_views)
        self.buffer_views.append({
            "buffer": 0,
            "byteOffset": index_offset,
            "byteLength": len(index_bytes),
            "target": 34963  # ELEMENT_ARRAY_BUFFER
        })
        
        # Create accessors
        vertex_accessor_idx = len(self.accessors)
        self.accessors.append({
            "bufferView": vertex_buffer_view_idx,
            "byteOffset": 0,
            "componentType": 5126,  # FLOAT
            "count": len(vertices),
            "type": "VEC3",
            "min": vertices.min(axis=0).tolist(),
            "max": vertices.max(axis=0).tolist()
        })
        
        index_accessor_idx = len(self.accessors)
        self.accessors.append({
            "bufferView": index_buffer_view_idx,
            "byteOffset": 0,
            "componentType": 5123,  # UNSIGNED_SHORT
            "count": len(indices),
            "type": "SCALAR",
            "min": [int(indices.min())],
            "max": [int(indices.max())]
        })
        
        # Create mesh
        mesh_idx = len(self.meshes)
        self.meshes.append({
            "name": name,
            "primitives": [{
                "attributes": {
                    "POSITION": vertex_accessor_idx
                },
                "indices": index_accessor_idx,
                "material": material_idx,
                "mode": 4  # TRIANGLES
            }]
        })
        
        return mesh_idx
    
    def _build_gltf_json(self) -> Dict[str, Any]:
        """Build the complete GLTF JSON structure."""
        # Ensure buffer data is padded to 4-byte boundary
        padding = (4 - len(self.buffer_data) % 4) % 4
        self.buffer_data.extend(b'\x00' * padding)
        
        gltf = {
            "asset": {
                "version": self.GLTF_VERSION,
                "generator": "PyModel Carport GLB Generator"
            },
            "scene": 0,
            "scenes": self.scenes,
            "nodes": self.nodes,
            "meshes": self.meshes,
            "materials": self.materials,
            "accessors": self.accessors,
            "bufferViews": self.buffer_views,
            "buffers": [{
                "byteLength": len(self.buffer_data)
            }]
        }
        
        # Add metadata
        gltf["extras"] = {
            "generated": datetime.now().isoformat(),
            "source": "PyModel BIM Backend",
            "aligned_with": "C# ShedGltf"
        }
        
        return gltf
    
    def _write_glb(self, gltf: Dict[str, Any], output_path: Path):
        """Write GLB binary file."""
        # Convert GLTF to JSON bytes
        json_bytes = json.dumps(gltf, separators=(',', ':')).encode('utf-8')
        
        # Pad JSON to 4-byte boundary
        json_padding = (4 - len(json_bytes) % 4) % 4
        json_bytes += b' ' * json_padding
        
        # Calculate total file size
        total_size = 12 + 8 + len(json_bytes) + 8 + len(self.buffer_data)
        
        with open(output_path, 'wb') as f:
            # GLB header
            f.write(struct.pack('<I', self.GLTF_MAGIC))
            f.write(struct.pack('<I', self.GLTF_VERSION_NUMBER))
            f.write(struct.pack('<I', total_size))
            
            # JSON chunk
            f.write(struct.pack('<I', len(json_bytes)))
            f.write(struct.pack('<I', self.CHUNK_TYPE_JSON))
            f.write(json_bytes)
            
            # Binary chunk
            f.write(struct.pack('<I', len(self.buffer_data)))
            f.write(struct.pack('<I', self.CHUNK_TYPE_BIN))
            f.write(self.buffer_data)
    
    def generate_from_building_input(self, building_input: BuildingInput, 
                                   output_path: Optional[str] = None) -> Path:
        """Generate GLB from BuildingInput object."""
        return self.generate_carport_glb(
            span=building_input.span,
            length=building_input.length,
            height=building_input.height,
            roof_type=CarportRoofType[building_input.roof_type],
            pitch=building_input.pitch,
            bays=building_input.bays,
            overhang=building_input.overhang,
            slab=building_input.slab,
            slab_thickness=building_input.slab_thickness,
            output_path=output_path
        )