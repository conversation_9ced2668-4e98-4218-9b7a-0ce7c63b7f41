"""
Material Builder for GLB Generation

Creates PBR materials for carport components matching C# implementation.
"""

from typing import Dict, Any, Optional, Tuple


class GLBMaterialBuilder:
    """
    Builds PBR (Physically Based Rendering) materials for GLB export.
    
    Aligned with C# ShedGltf material definitions for consistent appearance.
    """
    
    # Standard material presets matching C# implementation
    MATERIAL_PRESETS = {
        "steel": {
            "name": "Steel",
            "baseColor": (0.7, 0.7, 0.75, 1.0),
            "metallic": 0.8,
            "roughness": 0.3,
            "emissive": (0.0, 0.0, 0.0)
        },
        "galvanized": {
            "name": "Galvanized Steel", 
            "baseColor": (0.8, 0.8, 0.85, 1.0),
            "metallic": 0.7,
            "roughness": 0.4,
            "emissive": (0.0, 0.0, 0.0)
        },
        "concrete": {
            "name": "Concrete",
            "baseColor": (0.6, 0.6, 0.6, 1.0),
            "metallic": 0.0,
            "roughness": 0.9,
            "emissive": (0.0, 0.0, 0.0)
        },
        "painted_steel": {
            "name": "Painted Steel",
            "baseColor": (0.2, 0.3, 0.7, 1.0),  # Blue paint
            "metallic": 0.2,
            "roughness": 0.6,
            "emissive": (0.0, 0.0, 0.0)
        },
        "aluminum": {
            "name": "Aluminum",
            "baseColor": (0.85, 0.85, 0.88, 1.0),
            "metallic": 0.9,
            "roughness": 0.2,
            "emissive": (0.0, 0.0, 0.0)
        }
    }
    
    @staticmethod
    def create_material(preset: str = "steel", 
                       custom_color: Optional[Tuple[float, float, float, float]] = None,
                       name: Optional[str] = None) -> Dict[str, Any]:
        """
        Create a GLTF PBR material definition.
        
        Args:
            preset: Material preset name
            custom_color: Optional custom base color (r, g, b, a)
            name: Optional custom material name
            
        Returns:
            GLTF material definition dictionary
        """
        # Get base material from preset
        if preset in GLBMaterialBuilder.MATERIAL_PRESETS:
            base_material = GLBMaterialBuilder.MATERIAL_PRESETS[preset].copy()
        else:
            # Default material
            base_material = {
                "name": "Default",
                "baseColor": (0.8, 0.8, 0.8, 1.0),
                "metallic": 0.3,
                "roughness": 0.7,
                "emissive": (0.0, 0.0, 0.0)
            }
        
        # Override with custom values
        if custom_color:
            base_material["baseColor"] = custom_color
            
        if name:
            base_material["name"] = name
        
        # Build GLTF material structure
        material = {
            "name": base_material["name"],
            "pbrMetallicRoughness": {
                "baseColorFactor": list(base_material["baseColor"]),
                "metallicFactor": base_material["metallic"],
                "roughnessFactor": base_material["roughness"]
            },
            "emissiveFactor": list(base_material["emissive"]),
            "doubleSided": True,
            "alphaMode": "OPAQUE"
        }
        
        # Add transparency if alpha < 1
        if base_material["baseColor"][3] < 1.0:
            material["alphaMode"] = "BLEND"
            
        return material
    
    @staticmethod
    def create_texture_material(name: str,
                               base_color_texture_idx: int,
                               normal_texture_idx: Optional[int] = None,
                               metallic_roughness_texture_idx: Optional[int] = None) -> Dict[str, Any]:
        """
        Create a material with texture references.
        
        Args:
            name: Material name
            base_color_texture_idx: Index of base color texture
            normal_texture_idx: Optional normal map texture index
            metallic_roughness_texture_idx: Optional metallic-roughness texture index
            
        Returns:
            GLTF material with texture references
        """
        material = {
            "name": name,
            "pbrMetallicRoughness": {
                "baseColorTexture": {
                    "index": base_color_texture_idx,
                    "texCoord": 0
                },
                "metallicFactor": 1.0,
                "roughnessFactor": 1.0
            },
            "doubleSided": True
        }
        
        if metallic_roughness_texture_idx is not None:
            material["pbrMetallicRoughness"]["metallicRoughnessTexture"] = {
                "index": metallic_roughness_texture_idx,
                "texCoord": 0
            }
            
        if normal_texture_idx is not None:
            material["normalTexture"] = {
                "index": normal_texture_idx,
                "texCoord": 0,
                "scale": 1.0
            }
            
        return material
    
    @staticmethod
    def get_material_for_component(component_type: str) -> str:
        """
        Get recommended material preset for a component type.
        
        Args:
            component_type: Type of component (column, rafter, etc.)
            
        Returns:
            Material preset name
        """
        material_mapping = {
            "column": "galvanized",
            "rafter": "galvanized",
            "purlin": "galvanized",
            "eave_purlin": "galvanized",
            "footing": "concrete",
            "slab": "concrete",
            "brace": "steel",
            "bracket": "steel",
            "fastener": "steel"
        }
        
        return material_mapping.get(component_type.lower(), "steel")
    
    @staticmethod
    def create_material_variants(base_preset: str = "steel") -> Dict[str, Dict[str, Any]]:
        """
        Create a set of material variants for different conditions.
        
        Args:
            base_preset: Base material preset
            
        Returns:
            Dictionary of material variants
        """
        variants = {}
        
        # Normal condition
        variants["normal"] = GLBMaterialBuilder.create_material(base_preset)
        
        # Weathered variant
        base_color = GLBMaterialBuilder.MATERIAL_PRESETS[base_preset]["baseColor"]
        weathered_color = (
            base_color[0] * 0.8,
            base_color[1] * 0.8, 
            base_color[2] * 0.7,
            base_color[3]
        )
        variants["weathered"] = GLBMaterialBuilder.create_material(
            base_preset,
            custom_color=weathered_color,
            name=f"Weathered {GLBMaterialBuilder.MATERIAL_PRESETS[base_preset]['name']}"
        )
        
        # Highlighted variant (for selection/emphasis)
        highlight_color = (
            min(1.0, base_color[0] * 1.2),
            min(1.0, base_color[1] * 1.2),
            min(1.0, base_color[2] * 1.3),
            base_color[3]
        )
        variants["highlighted"] = GLBMaterialBuilder.create_material(
            base_preset,
            custom_color=highlight_color,
            name=f"Highlighted {GLBMaterialBuilder.MATERIAL_PRESETS[base_preset]['name']}"
        )
        
        return variants