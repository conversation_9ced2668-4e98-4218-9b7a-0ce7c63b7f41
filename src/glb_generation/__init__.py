"""
GLB Generation Module for Carport 3D Visualization

This module provides specialized GLB/GLTF generation for carport structures,
aligned with the C# ShedGltf implementation.

Features:
- Binary GLB format for compact file size
- PBR materials with proper colors
- Optimized mesh generation
- Metadata embedding
- Unity-compatible output option
"""

from .carport_glb_generator import CarportGLBGenerator
from .mesh_converter import CarportMeshConverter
from .material_builder import GLBMaterialBuilder

__all__ = [
    'CarportGLBGenerator',
    'CarportMeshConverter', 
    'GLBMaterialBuilder'
]