#!/usr/bin/env python3
"""
Carport IFC Generator with C# alignment.

This module provides functions to generate IFC files for various carport configurations,
ensuring complete alignment with the C# BimCoreLibrary implementation.
"""

import sys
from pathlib import Path
from typing import Optional, List, Dict
from datetime import datetime

# Add PyModel to path for imports
pymodel_path = Path(__file__).parent.parent.parent / "PyModel"
sys.path.insert(0, str(pymodel_path))

from src.business.building_input import BuildingInput, CarportRoofType
from src.business.structure_builder import CarportBuilder
from src.business.engineering import EngData
from .ifc_brep_generator import IFCBREPGenerator


class CarportIFCGenerator:
    """High-level carport IFC generator with various configuration options."""
    
    def __init__(self, output_dir: Path = None):
        """Initialize the generator.
        
        Args:
            output_dir: Directory for output files. Defaults to 'output/ifc'
        """
        self.output_dir = output_dir or Path("output/ifc")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.generator = IFCBREPGenerator()
    
    def generate_carport(self, 
                        span: float = 6000,
                        length: float = 9000,
                        height: float = 2700,
                        roof_type: CarportRoofType = CarportRoofType.GABLE,
                        pitch: float = 15,
                        bays: int = 3,
                        overhang: float = 0,
                        slab: bool = True,
                        eng_data: Optional[EngData] = None,
                        output_name: Optional[str] = None) -> Path:
        """Generate a single carport IFC file.
        
        Args:
            span: Carport span in mm
            length: Carport length in mm
            height: Carport height in mm
            roof_type: Type of roof (GABLE, FLAT, AWNING, ATTACHED_AWNING)
            pitch: Roof pitch in degrees
            bays: Number of bays
            overhang: Overhang distance in mm
            slab: Whether to include a slab
            eng_data: Optional engineering data for material overrides
            output_name: Optional output filename
            
        Returns:
            Path to generated IFC file
        """
        # Create building input
        building_input = BuildingInput(
            span=span,
            length=length,
            height=height,
            roof_type=roof_type,
            pitch=pitch,
            bays=bays,
            slab=slab
        )
        
        # Set overhang as property (only valid for flat roofs)
        if overhang > 0:
            building_input.overhang = overhang
        
        # Create carport structure
        carport = CarportBuilder.create_carport(building_input, eng_data)
        
        # Generate output filename
        if not output_name:
            roof_type_str = roof_type.value.lower().replace('_', '-')
            output_name = f"carport_{roof_type_str}_{int(span)}x{int(length)}_b{bays}.ifc"
        
        output_path = self.output_dir / output_name
        
        # Generate IFC content
        ifc_content = self.generator.generate_ifc_content(carport, output_path)
        
        # Write file
        with open(output_path, 'w') as f:
            f.write(ifc_content)
        
        return output_path
    
    def generate_standard_carports(self) -> List[Path]:
        """Generate a set of standard carport configurations.
        
        Returns:
            List of paths to generated IFC files
        """
        configurations = [
            # Basic gable carports
            {"span": 6000, "length": 6000, "roof_type": CarportRoofType.GABLE, "bays": 2},
            {"span": 6000, "length": 9000, "roof_type": CarportRoofType.GABLE, "bays": 3},
            {"span": 6000, "length": 12000, "roof_type": CarportRoofType.GABLE, "bays": 4},
            {"span": 7000, "length": 9000, "roof_type": CarportRoofType.GABLE, "bays": 3},
            {"span": 8000, "length": 9000, "roof_type": CarportRoofType.GABLE, "bays": 3},
            
            # Basic flat carports
            {"span": 6000, "length": 6000, "roof_type": CarportRoofType.FLAT, "bays": 2},
            {"span": 6000, "length": 9000, "roof_type": CarportRoofType.FLAT, "bays": 3},
            {"span": 6000, "length": 12000, "roof_type": CarportRoofType.FLAT, "bays": 4},
            
            # Carports with overhang
            {"span": 6000, "length": 9000, "roof_type": CarportRoofType.GABLE, "bays": 3, "overhang": 300},
            {"span": 6000, "length": 9000, "roof_type": CarportRoofType.FLAT, "bays": 3, "overhang": 300},
            
            # Different pitches
            {"span": 6000, "length": 9000, "roof_type": CarportRoofType.GABLE, "bays": 3, "pitch": 10},
            {"span": 6000, "length": 9000, "roof_type": CarportRoofType.GABLE, "bays": 3, "pitch": 20},
            {"span": 6000, "length": 9000, "roof_type": CarportRoofType.GABLE, "bays": 3, "pitch": 25},
            
            # Awning types
            {"span": 6000, "length": 9000, "roof_type": CarportRoofType.AWNING, "bays": 3, "pitch": 5},
            {"span": 6000, "length": 9000, "roof_type": CarportRoofType.ATTACHED_AWNING, "bays": 3, "pitch": 5},
        ]
        
        generated_files = []
        
        for config in configurations:
            try:
                path = self.generate_carport(**config)
                generated_files.append(path)
                print(f"✅ Generated: {path.name}")
            except Exception as e:
                print(f"❌ Failed to generate carport with config {config}: {e}")
        
        return generated_files
    
    def generate_test_suite(self, count: int = 50) -> List[Path]:
        """Generate a comprehensive test suite of carport variations.
        
        Args:
            count: Number of carports to generate
            
        Returns:
            List of paths to generated IFC files
        """
        import random
        
        generated_files = []
        
        # Standard dimensions to choose from
        spans = [5000, 6000, 7000, 8000, 9000]
        lengths = [6000, 7500, 9000, 10500, 12000, 15000]
        heights = [2400, 2700, 3000, 3300]
        bay_counts = [2, 3, 4, 5, 6]
        pitches = [5, 10, 15, 20, 25, 30]
        overhangs = [0, 150, 300, 450, 600]
        
        # Ensure we generate at least one of each roof type
        roof_types = list(CarportRoofType)
        
        for i in range(count):
            # Cycle through roof types
            roof_type = roof_types[i % len(roof_types)]
            
            # Random configuration
            config = {
                "span": random.choice(spans),
                "length": random.choice(lengths),
                "height": random.choice(heights),
                "roof_type": roof_type,
                "pitch": random.choice(pitches) if roof_type == CarportRoofType.GABLE else random.choice([5, 10, 15]),
                "bays": random.choice(bay_counts),
                "overhang": random.choice(overhangs),
                "slab": random.choice([True, False]),
                "output_name": f"test_carport_{i+1:03d}.ifc"
            }
            
            # Adjust bays based on length
            max_bays = int(config["length"] / 3000)
            config["bays"] = min(config["bays"], max_bays) if max_bays > 0 else 2
            
            try:
                path = self.generate_carport(**config)
                generated_files.append(path)
                print(f"✅ [{i+1}/{count}] Generated: {path.name}")
            except Exception as e:
                print(f"❌ [{i+1}/{count}] Failed: {e}")
        
        return generated_files
    
    def generate_with_engineering_data(self, eng_data: EngData, 
                                     building_input: Optional[BuildingInput] = None) -> Path:
        """Generate carport with specific engineering data.
        
        Args:
            eng_data: Engineering data with material specifications
            building_input: Optional building input. If not provided, uses eng_data dimensions
            
        Returns:
            Path to generated IFC file
        """
        if not building_input:
            # Create building input from engineering data if available
            building_input = BuildingInput(
                span=eng_data.width if hasattr(eng_data, 'width') else 6000,
                length=eng_data.length if hasattr(eng_data, 'length') else 9000,
                height=eng_data.height if hasattr(eng_data, 'height') else 2700,
                roof_type=CarportRoofType.GABLE,
                pitch=eng_data.pitch if hasattr(eng_data, 'pitch') else 15,
                bays=eng_data.bays if hasattr(eng_data, 'bays') else 3
            )
        
        # Generate with engineering data
        return self.generate_carport(
            span=building_input.span,
            length=building_input.length,
            height=building_input.height,
            roof_type=building_input.roof_type,
            pitch=building_input.pitch,
            bays=building_input.bays,
            overhang=building_input.overhang,
            slab=building_input.slab,
            eng_data=eng_data,
            output_name=f"carport_engineered_{datetime.now().strftime('%Y%m%d_%H%M%S')}.ifc"
        )
    
    def generate_report(self, generated_files: List[Path]) -> str:
        """Generate a summary report of generated files.
        
        Args:
            generated_files: List of generated file paths
            
        Returns:
            Report string
        """
        report = f"""
IFC Generation Report
====================
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Total Files: {len(generated_files)}

Files Generated:
"""
        
        total_size = 0
        for i, path in enumerate(generated_files, 1):
            if path.exists():
                size = path.stat().st_size
                total_size += size
                report += f"{i:3d}. {path.name:<50} {size:>10,} bytes\n"
            else:
                report += f"{i:3d}. {path.name:<50} [MISSING]\n"
        
        report += f"\nTotal Size: {total_size:,} bytes ({total_size/1024/1024:.2f} MB)\n"
        
        return report


def main():
    """Example usage of the carport generator."""
    generator = CarportIFCGenerator()
    
    print("=" * 60)
    print("CARPORT IFC GENERATOR")
    print("=" * 60)
    
    # Generate a single carport
    print("\n1. Generating single carport...")
    path = generator.generate_carport(
        span=6000,
        length=9000,
        height=2700,
        roof_type=CarportRoofType.GABLE,
        pitch=15,
        bays=3,
        slab=True
    )
    print(f"   Generated: {path}")
    
    # Generate standard set
    print("\n2. Generating standard carport set...")
    standard_files = generator.generate_standard_carports()
    print(f"   Generated {len(standard_files)} standard carports")
    
    # Generate test suite
    print("\n3. Generating test suite...")
    test_files = generator.generate_test_suite(count=10)
    print(f"   Generated {len(test_files)} test carports")
    
    # Generate report
    all_files = [path] + standard_files + test_files
    report = generator.generate_report(all_files)
    
    report_path = generator.output_dir / "generation_report.txt"
    with open(report_path, 'w') as f:
        f.write(report)
    
    print(f"\n📄 Report saved to: {report_path}")
    print("\n✅ Generation complete!")


if __name__ == "__main__":
    main()