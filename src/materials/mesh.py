"""3D mesh representation for BIM materials.

This module corresponds to the Mesh3d class from the C# implementation:
- MeshHelper.cs: Lines 443-449

Provides 3D mesh data structure for material visualization.
"""

# Fix imports to use absolute paths
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from dataclasses import dataclass, field
from typing import List

# Import geometry types from our previously implemented modules
from src.geometry.primitives import Vec3
from src.geometry.boxes import Box3
from src.geometry.triangle import TriIndex


# C# Ref: Lines 443-449 - public class Mesh3d
@dataclass
class Mesh3d:
    """Represents a 3D mesh with vertices and triangle indices.
    
    C# Reference: MeshHelper.cs lines 443-449
    C# Definition: public class Mesh3d
    """
    # C# Ref: Line 445 - public List<Vec3> Vertexes { get; set; }
    vertexes: List[Vec3] = field(default_factory=list)
    
    # C# Ref: Line 446 - public List<TriIndex> Indexes { get; set; }
    indexes: List[TriIndex] = field(default_factory=list)
    
    # C# Ref: Line 447 - public Box3 Bounds { get; set; }
    bounds: Box3 = field(default_factory=lambda: Box3.empty())
    
    def calculate_bounds(self) -> None:
        """Calculate the bounding box of the mesh from its vertices."""
        if not self.vertexes:
            self.bounds = Box3.empty()
            return
            
        min_x = min_y = min_z = float('inf')
        max_x = max_y = max_z = float('-inf')
        
        for vertex in self.vertexes:
            min_x = min(min_x, vertex.x)
            min_y = min(min_y, vertex.y)
            min_z = min(min_z, vertex.z)
            max_x = max(max_x, vertex.x)
            max_y = max(max_y, vertex.y)
            max_z = max(max_z, vertex.z)
        
        self.bounds = Box3(
            Vec3(min_x, min_y, min_z),
            Vec3(max_x, max_y, max_z)
        )
    
    def get_triangle_count(self) -> int:
        """Get the number of triangles in the mesh."""
        return len(self.indexes)
    
    def get_vertex_count(self) -> int:
        """Get the number of vertices in the mesh."""
        return len(self.vertexes)
    
    def is_valid(self) -> bool:
        """Check if the mesh is valid (has vertices and indices)."""
        if not self.vertexes or not self.indexes:
            return False
            
        # Check that all indices reference valid vertices
        max_index = len(self.vertexes) - 1
        for tri in self.indexes:
            if tri.a < 0 or tri.a > max_index:
                return False
            if tri.b < 0 or tri.b > max_index:
                return False
            if tri.c < 0 or tri.c > max_index:
                return False
                
        return True
    
    def transform(self, transform_matrix: 'Mat4') -> 'Mesh3d':
        """Create a transformed copy of the mesh.
        
        Args:
            transform_matrix: The transformation matrix to apply
            
        Returns:
            A new Mesh3d with transformed vertices
        """
        from geometry import Mat4
        
        # Transform vertices
        transformed_vertices = [
            transform_matrix.transform_position(v) for v in self.vertexes
        ]
        
        # Create new mesh with transformed vertices (indices remain the same)
        new_mesh = Mesh3d(
            vertexes=transformed_vertices,
            indexes=self.indexes.copy()
        )
        
        # Calculate new bounds
        new_mesh.calculate_bounds()
        
        return new_mesh
    
    def merge(self, other: 'Mesh3d') -> 'Mesh3d':
        """Merge this mesh with another mesh.
        
        Args:
            other: The mesh to merge with
            
        Returns:
            A new Mesh3d containing vertices and triangles from both meshes
        """
        # Copy vertices from both meshes
        merged_vertices = self.vertexes.copy() + other.vertexes.copy()
        
        # Copy indices from first mesh
        merged_indices = self.indexes.copy()
        
        # Add indices from second mesh with offset
        vertex_offset = len(self.vertexes)
        for tri in other.indexes:
            merged_indices.append(
                TriIndex(
                    tri.a + vertex_offset,
                    tri.b + vertex_offset,
                    tri.c + vertex_offset
                )
            )
        
        # Create merged mesh
        merged_mesh = Mesh3d(
            vertexes=merged_vertices,
            indexes=merged_indices
        )
        
        # Calculate bounds for merged mesh
        merged_mesh.calculate_bounds()
        
        return merged_mesh
    
    @staticmethod
    def create_box(box: Box3) -> 'Mesh3d':
        """Create a mesh representing a 3D box.
        
        Args:
            box: The bounding box to create a mesh for
            
        Returns:
            A Mesh3d representing the box with 8 vertices and 12 triangles
        """
        min_pt = box.min
        max_pt = box.max
        
        # Create 8 vertices for the box corners
        vertices = [
            Vec3(min_pt.x, min_pt.y, min_pt.z),  # 0: bottom-back-left
            Vec3(max_pt.x, min_pt.y, min_pt.z),  # 1: bottom-back-right
            Vec3(max_pt.x, max_pt.y, min_pt.z),  # 2: bottom-front-right
            Vec3(min_pt.x, max_pt.y, min_pt.z),  # 3: bottom-front-left
            Vec3(min_pt.x, min_pt.y, max_pt.z),  # 4: top-back-left
            Vec3(max_pt.x, min_pt.y, max_pt.z),  # 5: top-back-right
            Vec3(max_pt.x, max_pt.y, max_pt.z),  # 6: top-front-right
            Vec3(min_pt.x, max_pt.y, max_pt.z),  # 7: top-front-left
        ]
        
        # Create 12 triangles (2 per face, 6 faces)
        indices = [
            # Bottom face
            TriIndex(0, 1, 2),
            TriIndex(0, 2, 3),
            # Top face
            TriIndex(4, 6, 5),
            TriIndex(4, 7, 6),
            # Front face
            TriIndex(3, 2, 6),
            TriIndex(3, 6, 7),
            # Back face
            TriIndex(0, 5, 1),
            TriIndex(0, 4, 5),
            # Left face
            TriIndex(0, 3, 7),
            TriIndex(0, 7, 4),
            # Right face
            TriIndex(1, 5, 6),
            TriIndex(1, 6, 2),
        ]
        
        return Mesh3d(
            vertexes=vertices,
            indexes=indices,
            bounds=box
        )