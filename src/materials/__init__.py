"""Material system for BIM Backend.

This module corresponds to the Materials.cs file from the C# implementation:
- Materials.cs: Lines 1-588

The material system provides definitions for various building materials including:
- Bracket materials and meshes
- Cladding materials with profiles
- Color materials with RGB/CMYK support
- Flashing materials
- Downpipe materials
- Fastener materials (bolts, etc.)
- Footing materials
- Frame materials (C, Z, TH, SHS sections)
- Punching specifications
- Strap materials
- Lining materials
"""

from .base import (
    BracketMaterial,
    BracketMesh,
    BracketAttachment,
    CladdingMaterial,
    ColorMaterial,
    FlashingMaterial,
    DownpipeMaterial,
    FastenerMaterial,
    FastenerMaterialType,
    FootingMaterial,
    FootingMaterialType,
    FrameMaterial,
    FrameMaterialType,
    Punching,
    PunchingWhere,
    StrapMaterial,
    LiningMaterial,
)

from .mesh import (
    Mesh3d,
)

from .visual import (
    MaterialFinish,
    TextureMapping,
    MaterialAppearance,
    ColorLibrary,
    MaterialProfile,
    MaterialVisualizer,
)

from .helpers import (
    FrameMaterialHelper,
    CladdingMaterialHelper,
    CladdingProfileHelper,
    FastenerMaterialHelper,
    FootingMaterialHelper,
    FlashingMaterialHelper,
    BracketMaterialHelper,
)

from .profiles import (
    MeshProfileItem,
    PunchingMap,
    MeshProfile,
    MeshProfileHelper,
)

from .segments import (
    MaterialSegmentData,
    MaterialSegment,
    ShedBimCladdingHole,
    ShedBimCladdingSegment,
    ShedBimLiningSegment,
    MaterialSegmentHelper,
)

__all__ = [
    # Base materials
    'BracketMaterial',
    'BracketMesh',
    'BracketAttachment',
    'CladdingMaterial',
    'ColorMaterial',
    'FlashingMaterial',
    'DownpipeMaterial',
    'FastenerMaterial',
    'FastenerMaterialType',
    'FootingMaterial',
    'FootingMaterialType',
    'FrameMaterial',
    'FrameMaterialType',
    'Punching',
    'PunchingWhere',
    'StrapMaterial',
    'LiningMaterial',
    # Mesh
    'Mesh3d',
    # Visual properties
    'MaterialFinish',
    'TextureMapping',
    'MaterialAppearance',
    'ColorLibrary',
    'MaterialProfile',
    'MaterialVisualizer',
    # Helpers
    'FrameMaterialHelper',
    'CladdingMaterialHelper',
    'CladdingProfileHelper',
    'FastenerMaterialHelper',
    'FootingMaterialHelper',
    'FlashingMaterialHelper',
    'BracketMaterialHelper',
    # Profiles
    'MeshProfileItem',
    'PunchingMap',
    'MeshProfile',
    'MeshProfileHelper',
    # Segments
    'MaterialSegmentData',
    'MaterialSegment',
    'ShedBimCladdingHole',
    'ShedBimCladdingSegment',
    'ShedBimLiningSegment',
    'MaterialSegmentHelper',
]