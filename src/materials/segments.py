"""Material segmentation for cladding and lining.

This module corresponds to MaterialSegmentHelper.cs from the C# implementation.
Handles the segmentation of cladding sheets for roofs and walls.
"""

# Fix imports to use absolute paths
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from dataclasses import dataclass, field
from typing import List, Dict, Optional, Tuple
from enum import Enum

from src.geometry.primitives import Vec2, Vec3
from src.geometry.boxes import Box3
from .base import CladdingMaterial, LiningMaterial


@dataclass
class MaterialSegmentData:
    """Data for a material segment.
    
    C# Reference: MaterialSegmentData struct
    """
    full_outline: List[Vec3]
    effective_outline: List[Vec3]
    sheet_width: float
    full_length: float
    full_width: float
    is_full_width: bool = True


@dataclass 
class MaterialSegment:
    """Basic material segment.
    
    C# Reference: MaterialSegment internal class
    """
    full_outline: List[Vec3] = field(default_factory=list)
    effective_outline: List[Vec3] = field(default_factory=list)
    sheet_width: float = 0.0
    full_length: float = 0.0
    full_width: float = 0.0


@dataclass
class ShedBimCladdingHole:
    """Hole in cladding segment.
    
    C# Reference: ShedBimCladdingHole
    """
    outline: List[Vec3] = field(default_factory=list)


@dataclass
class ShedBimCladdingSegment:
    """Cladding segment with holes.
    
    C# Reference: ShedBimCladdingSegment
    """
    full_outline: List[Vec3] = field(default_factory=list)
    effective_outline: List[Vec3] = field(default_factory=list)
    sheet_width: float = 0.0
    full_length: float = 0.0
    full_width: float = 0.0
    holes: List[ShedBimCladdingHole] = field(default_factory=list)


@dataclass
class ShedBimLiningSegment:
    """Lining segment.
    
    C# Reference: ShedBimLiningSegment
    """
    material: LiningMaterial
    full_outline: List[Vec3] = field(default_factory=list)
    effective_outline: List[Vec3] = field(default_factory=list)
    sheet_width: float = 0.0
    full_length: float = 0.0
    full_width: float = 0.0


class MaterialSegmentHelper:
    """Helper for segmenting materials.
    
    C# Reference: MaterialSegmentHelper.cs
    """
    
    @staticmethod
    def get_cladding_segments(shed_bim_cladding: 'ShedBimCladding') -> Optional[List[ShedBimCladdingSegment]]:
        """Get cladding segments for a cladding area.
        
        C# Ref: Lines 12-39
        """
        if shed_bim_cladding.cladding is None:
            return None
        
        segments = []
        
        segment_data = MaterialSegmentHelper._get_roof_segments_from_outline(
            shed_bim_cladding.points,
            shed_bim_cladding.cladding.cover_width,
            shed_bim_cladding.cladding.overlap / 2
        )
        
        for data in segment_data:
            segments.append(ShedBimCladdingSegment(
                full_outline=data.full_outline,
                effective_outline=data.effective_outline,
                sheet_width=data.sheet_width,
                full_length=data.full_length,
                full_width=data.full_width,
                holes=[]
            ))
        
        shed_bim_cladding.cladding_segments = segments
        return segments
    
    @staticmethod
    def get_nearest_full_segment_from_skylight(
        skylight_centroid: Vec3,
        centroid_segment_dictionary: Dict[Vec3, MaterialSegmentData]
    ) -> Optional[MaterialSegmentData]:
        """Find nearest full-width segment to skylight.
        
        C# Ref: Lines 41-59
        """
        min_distance = float('inf')
        nearest_segment = None
        
        for segment_centroid, segment_data in centroid_segment_dictionary.items():
            current_distance = (segment_centroid - skylight_centroid).length()
            
            if current_distance < min_distance and segment_data.is_full_width:
                min_distance = current_distance
                nearest_segment = segment_data
        
        return nearest_segment
    
    @staticmethod
    def get_roof_cladding_segments(shed_bim_cladding: 'ShedBimCladding') -> Optional[List[ShedBimCladdingSegment]]:
        """Get roof cladding segments.
        
        C# Ref: Lines 61-88
        """
        # Same as get_cladding_segments for roof
        return MaterialSegmentHelper.get_cladding_segments(shed_bim_cladding)
    
    @staticmethod
    def get_lining_segments(shed_bim_cladding: 'ShedBimCladding') -> Optional[List[ShedBimLiningSegment]]:
        """Get lining segments for cladding.
        
        C# Ref: Lines 89-124
        """
        if shed_bim_cladding.lining_materials is None:
            return None
        
        lining_segments = []
        
        for lining_material in shed_bim_cladding.lining_materials:
            lining_cover_width = lining_material.width_per_roll - 2 * lining_material.overlap
            
            roof_lining_outlines = MaterialSegmentHelper.get_roof_lining_outlines(shed_bim_cladding)
            
            for roof_lining_outline in roof_lining_outlines:
                segment_data = MaterialSegmentHelper._get_roof_segments_from_outline(
                    roof_lining_outline,
                    lining_cover_width,
                    lining_material.overlap
                )
                
                for data in segment_data:
                    lining_segments.append(ShedBimLiningSegment(
                        material=lining_material,
                        full_outline=data.full_outline,
                        effective_outline=data.effective_outline,
                        sheet_width=data.sheet_width,
                        full_length=data.full_length,
                        full_width=data.full_width
                    ))
        
        return lining_segments
    
    @staticmethod
    def get_roof_lining_outlines(roof_cladding: 'ShedBimCladding') -> Optional[List[List[Vec3]]]:
        """Get lining outlines from roof cladding.
        
        C# Ref: Lines 125-179
        """
        if roof_cladding.cladding_segments is None:
            return None
        
        # List of lining outlines
        roof_lining_outlines = []
        
        # Single lining outline
        roof_lining_outline = []
        
        # Get first cladding and add outline points
        current_cladding = roof_cladding.cladding_segments[0]
        roof_lining_outline.extend(current_cladding.full_outline)
        
        for i in range(len(roof_cladding.cladding_segments) - 1):
            next_cladding = roof_cladding.cladding_segments[i + 1]
            
            if not MaterialSegmentHelper._is_overlapping(roof_lining_outline, next_cladding.full_outline):
                roof_lining_outlines.append(roof_lining_outline)
                roof_lining_outline = []
                roof_lining_outline.extend(next_cladding.full_outline)
                current_cladding = next_cladding
                continue
            
            MaterialSegmentHelper._merge_outlines(roof_lining_outline, next_cladding.full_outline)
        
        if roof_lining_outline:
            roof_lining_outlines.append(roof_lining_outline)
        
        return roof_lining_outlines
    
    @staticmethod
    def _is_overlapping(target_outline: List[Vec3], test_outline: List[Vec3]) -> bool:
        """Check if two outlines overlap.
        
        C# Ref: Lines 163-169
        """
        box1 = Box3.from_list(target_outline)
        box2 = Box3.from_list(test_outline)
        return box1.intersects(box2)
    
    @staticmethod
    def _merge_outlines(target_outline: List[Vec3], to_merge_outline: List[Vec3]):
        """Merge two outlines.
        
        C# Ref: Lines 171-176
        """
        # HACK: This works on roof cladding since segments are always full outlines
        target_outline[2] = to_merge_outline[2]
        target_outline[3] = to_merge_outline[3]
    
    @staticmethod
    def _get_roof_segments_from_outline(
        outline: List[Vec3],
        cover_width: float,
        one_side_overlap: float
    ) -> List[MaterialSegment]:
        """Get roof segments from outline.
        
        C# Ref: Lines 181-287
        """
        # HACK: This works on roof since it always has 4 points
        width_vec = outline[2] - outline[1]
        length_vec = outline[1] - outline[0]
        full_width = width_vec.length()
        full_length = length_vec.length()
        width_vec_normal = Vec3.normal(width_vec)
        length_vec_normal = Vec3.normal(length_vec)
        one_side_overlap_vector = one_side_overlap * width_vec_normal
        
        material_segments = []
        
        # Full sheet/segment is cover + 2 overlaps
        full_sheet_width = cover_width + 2 * one_side_overlap
        
        first_segment_width = full_sheet_width
        last_segment_width = full_sheet_width  # Assume full width for now
        
        # First point for roof is eave point where Y = 0
        base_point = outline[0]
        
        # Save last basepoint
        previous_base_point = base_point
        covered_width = 0.0
        
        # Helper function to add segment
        def add_segment_and_get_new_basepoint(
            start_point: Vec3,
            first_segment: bool = False,
            last_segment: bool = False
        ) -> Vec3:
            # Full outline (topdown outline without overlaps)
            p1 = start_point
            p2 = p1 + length_vec_normal * full_length
            p3 = p2 + full_sheet_width * width_vec_normal
            p4 = p1 + full_sheet_width * width_vec_normal
            
            if first_segment:
                p3 = p2 + first_segment_width * width_vec_normal
                p4 = p1 + first_segment_width * width_vec_normal
            
            if last_segment:
                p3 = p2 + last_segment_width * width_vec_normal
                p4 = p1 + last_segment_width * width_vec_normal
            
            # Effective outline adjusts full outline by overlap
            x1 = p1 + one_side_overlap_vector
            x2 = p2 + one_side_overlap_vector
            x3 = p3 - one_side_overlap_vector
            x4 = p4 - one_side_overlap_vector
            
            if first_segment:
                x1 = p1
                x2 = p2
            
            if last_segment:
                x3 = p3
                x4 = p4
            
            material_segment = MaterialSegment(
                full_outline=[p1, p2, p3, p4],
                effective_outline=[x1, x2, x3, x4],
                sheet_width=last_segment_width if last_segment else full_sheet_width,
                full_length=full_length,
                full_width=full_sheet_width
            )
            
            material_segments.append(material_segment)
            
            # Return last point as starting point for next sheet
            return x4
        
        # Add first segment
        base_point = add_segment_and_get_new_basepoint(base_point, first_segment=True)
        covered_width += full_sheet_width
        
        # Add middle segments until not enough space
        while covered_width < full_width:
            previous_base_point = base_point
            base_point = add_segment_and_get_new_basepoint(base_point)
            covered_width += cover_width + one_side_overlap
        
        # Calculate excess material
        excess_material = covered_width - full_width
        
        # If there's excess material
        if excess_material > 0:
            last_segment_width -= excess_material
        
        # Remove last material and re-add with correct width
        material_segments.pop()
        add_segment_and_get_new_basepoint(previous_base_point, last_segment=True)
        
        return material_segments
    
    @staticmethod
    def get_wall_cladding_segments(
        piece: 'CladdingOutputPiece',
        material: CladdingMaterial,
        cladding_position: float,
        roof_shape: List[Vec2],
        is_side_wall: bool = False
    ) -> List[ShedBimCladdingSegment]:
        """Get wall cladding segments.
        
        C# Ref: Lines 288+
        """
        segments = []
        
        # Sheeting specific properties
        full_sheet_width = material.cover_width + material.overlap
        cover_width = material.cover_width
        wall_outline = piece.verts
        bottom_points = [p for p in wall_outline if p not in roof_shape]
        base_point = wall_outline[0]
        holes = piece.holes
        
        # TODO: Implement full wall segmentation logic
        # This is a complex algorithm that would need more of the C# code
        
        return segments