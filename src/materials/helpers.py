"""Material helper utilities for BIM Backend.

This module provides helper functions and predefined material catalogs.
Corresponds to various helper files in the C# implementation:
- FrameMaterialHelper.cs
- CladdingMaterialHelper.cs
- FastenerMaterialHelper.cs
- FootingMaterialHelper.cs
- FlashingMaterialHelper.cs
"""

# Fix imports to use absolute paths
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from typing import Dict, List, Optional, Set
from dataclasses import dataclass
from enum import Enum

from .base import (
    FrameMaterial, FrameMaterialType, 
    CladdingMaterial, FastenerMaterial,
    FootingMaterial, FootingMaterialType,
    FlashingMaterial, BracketMaterial
)
from src.geometry.primitives import Vec2


class FrameMaterialHelper:
    """Helper for frame materials with predefined catalog.
    
    C# Reference: FrameMaterialHelper.cs
    """
    
    # Web hole centers from Stramit book of answers
    # C# Ref: Lines 34-40
    WEB_HOLE_CENTERS_100 = 40
    WEB_HOLE_CENTERS_150 = 60  # Vic and Tas are 70
    WEB_HOLE_CENTERS_200 = 110
    WEB_HOLE_CENTERS_250 = 160
    WEB_HOLE_CENTERS_300 = 210
    WEB_HOLE_CENTERS_350 = 210
    WEB_HOLE_CENTERS_400 = 310
    
    # C-sections catalog (C# Ref: Lines 44-96)
    C_SECTIONS = {
        # Single C-sections
        "C10010": lambda: FrameMaterial.create_c("C10010", 100, False, 102, 51, 12.5, 1.0, FrameMaterialHelper.WEB_HOLE_CENTERS_100),
        "C10012": lambda: FrameMaterial.create_c("C10012", 100, False, 102, 51, 13.0, 1.2, FrameMaterialHelper.WEB_HOLE_CENTERS_100),
        "C10015": lambda: FrameMaterial.create_c("C10015", 100, False, 102, 51, 14.0, 1.5, FrameMaterialHelper.WEB_HOLE_CENTERS_100),
        "C10019": lambda: FrameMaterial.create_c("C10019", 100, False, 102, 51, 15.0, 1.9, FrameMaterialHelper.WEB_HOLE_CENTERS_100),
        
        "C15010": lambda: FrameMaterial.create_c("C15010", 150, False, 152, 64, 14.5, 1.0, FrameMaterialHelper.WEB_HOLE_CENTERS_150),
        "C15012": lambda: FrameMaterial.create_c("C15012", 150, False, 152, 64, 15.0, 1.2, FrameMaterialHelper.WEB_HOLE_CENTERS_150),
        "C15015": lambda: FrameMaterial.create_c("C15015", 150, False, 152, 64, 16.0, 1.5, FrameMaterialHelper.WEB_HOLE_CENTERS_150),
        "C15019": lambda: FrameMaterial.create_c("C15019", 150, False, 152, 64, 17.0, 1.9, FrameMaterialHelper.WEB_HOLE_CENTERS_150),
        "C15024": lambda: FrameMaterial.create_c("C15024", 150, False, 152, 64, 18.5, 2.4, FrameMaterialHelper.WEB_HOLE_CENTERS_150),
        
        "C20015": lambda: FrameMaterial.create_c("C20015", 200, False, 203, 76, 16.0, 1.5, FrameMaterialHelper.WEB_HOLE_CENTERS_200),
        "C20019": lambda: FrameMaterial.create_c("C20019", 200, False, 203, 76, 19.5, 1.9, FrameMaterialHelper.WEB_HOLE_CENTERS_200),
        "C20020": lambda: FrameMaterial.create_c("C20020", 200, False, 203, 76, 20.0, 2.0, FrameMaterialHelper.WEB_HOLE_CENTERS_200),
        "C20024": lambda: FrameMaterial.create_c("C20024", 200, False, 203, 76, 21.0, 2.4, FrameMaterialHelper.WEB_HOLE_CENTERS_200),
        
        "C25019": lambda: FrameMaterial.create_c("C25019", 250, False, 254, 76, 19.0, 1.9, FrameMaterialHelper.WEB_HOLE_CENTERS_250),
        "C25020": lambda: FrameMaterial.create_c("C25020", 250, False, 254, 76, 19.5, 2.0, FrameMaterialHelper.WEB_HOLE_CENTERS_250),
        "C25024": lambda: FrameMaterial.create_c("C25024", 250, False, 254, 76, 20.5, 2.4, FrameMaterialHelper.WEB_HOLE_CENTERS_250),
        "C25025": lambda: FrameMaterial.create_c("C25025", 250, False, 254, 76, 21.0, 2.5, FrameMaterialHelper.WEB_HOLE_CENTERS_250),
        
        "C30024": lambda: FrameMaterial.create_c("C30024", 300, False, 300, 96, 28.0, 2.4, FrameMaterialHelper.WEB_HOLE_CENTERS_300),
        "C30030": lambda: FrameMaterial.create_c("C30030", 300, False, 300, 96, 31.5, 3.0, FrameMaterialHelper.WEB_HOLE_CENTERS_300),
        
        "C35030": lambda: FrameMaterial.create_c("C35030", 350, False, 350, 125, 30.0, 3.0, FrameMaterialHelper.WEB_HOLE_CENTERS_350),
        
        "C40024": lambda: FrameMaterial.create_c("C40024", 400, False, 400, 96, 35.0, 2.4, FrameMaterialHelper.WEB_HOLE_CENTERS_400),
        "C40030": lambda: FrameMaterial.create_c("C40030", 400, False, 400, 96, 33.5, 3.0, FrameMaterialHelper.WEB_HOLE_CENTERS_400),
        
        # Back-to-back C-sections (C# Ref: Lines 71-95)
        "2C10010": lambda: FrameMaterial.create_c("2C10010", 100, True, 102, 102, 12.5, 1.0, FrameMaterialHelper.WEB_HOLE_CENTERS_100),
        "2C10012": lambda: FrameMaterial.create_c("2C10012", 100, True, 102, 102, 13.0, 1.2, FrameMaterialHelper.WEB_HOLE_CENTERS_100),
        "2C10015": lambda: FrameMaterial.create_c("2C10015", 100, True, 102, 102, 14.0, 1.5, FrameMaterialHelper.WEB_HOLE_CENTERS_100),
        "2C10019": lambda: FrameMaterial.create_c("2C10019", 100, True, 102, 102, 15.0, 1.9, FrameMaterialHelper.WEB_HOLE_CENTERS_100),
        
        "2C15010": lambda: FrameMaterial.create_c("2C15010", 150, True, 152, 128, 14.5, 1.0, FrameMaterialHelper.WEB_HOLE_CENTERS_150),
        "2C15012": lambda: FrameMaterial.create_c("2C15012", 150, True, 152, 128, 15.0, 1.2, FrameMaterialHelper.WEB_HOLE_CENTERS_150),
        "2C15015": lambda: FrameMaterial.create_c("2C15015", 150, True, 152, 128, 16.0, 1.5, FrameMaterialHelper.WEB_HOLE_CENTERS_150),
        "2C15019": lambda: FrameMaterial.create_c("2C15019", 150, True, 152, 128, 17.0, 1.9, FrameMaterialHelper.WEB_HOLE_CENTERS_150),
        "2C15024": lambda: FrameMaterial.create_c("2C15024", 150, True, 152, 128, 18.5, 2.4, FrameMaterialHelper.WEB_HOLE_CENTERS_150),
        
        "2C20015": lambda: FrameMaterial.create_c("2C20015", 200, True, 203, 152, 16.0, 1.5, FrameMaterialHelper.WEB_HOLE_CENTERS_200),
        "2C20019": lambda: FrameMaterial.create_c("2C20019", 200, True, 203, 152, 19.5, 1.9, FrameMaterialHelper.WEB_HOLE_CENTERS_200),
        "2C20024": lambda: FrameMaterial.create_c("2C20024", 200, True, 203, 152, 21.0, 2.4, FrameMaterialHelper.WEB_HOLE_CENTERS_200),
        
        "2C25019": lambda: FrameMaterial.create_c("2C25019", 250, True, 254, 152, 19.0, 1.9, FrameMaterialHelper.WEB_HOLE_CENTERS_250),
        "2C25024": lambda: FrameMaterial.create_c("2C25024", 250, True, 254, 152, 20.5, 2.4, FrameMaterialHelper.WEB_HOLE_CENTERS_250),
        
        "2C30024": lambda: FrameMaterial.create_c("2C30024", 300, True, 300, 192, 28.0, 2.4, FrameMaterialHelper.WEB_HOLE_CENTERS_300),
        "2C30030": lambda: FrameMaterial.create_c("2C30030", 300, True, 300, 192, 31.5, 3.0, FrameMaterialHelper.WEB_HOLE_CENTERS_300),
        
        "2C35030": lambda: FrameMaterial.create_c("2C35030", 350, True, 350, 250, 30.0, 3.0, FrameMaterialHelper.WEB_HOLE_CENTERS_350),
        
        "2C40024": lambda: FrameMaterial.create_c("2C40024", 400, True, 400, 192, 35.0, 2.4, FrameMaterialHelper.WEB_HOLE_CENTERS_400),
        "2C40030": lambda: FrameMaterial.create_c("2C40030", 400, True, 400, 192, 33.5, 3.0, FrameMaterialHelper.WEB_HOLE_CENTERS_400),
    }
    
    # Z-sections catalog (C# Ref: Lines 98-120)
    Z_SECTIONS = {
        "Z10010": lambda: FrameMaterial.create_z("Z10010", 100, 102, 53, 49, 12.5, 1.0, FrameMaterialHelper.WEB_HOLE_CENTERS_100),
        "Z10012": lambda: FrameMaterial.create_z("Z10012", 100, 102, 53, 49, 13.0, 1.2, FrameMaterialHelper.WEB_HOLE_CENTERS_100),
        "Z10015": lambda: FrameMaterial.create_z("Z10015", 100, 102, 53, 49, 14.0, 1.5, FrameMaterialHelper.WEB_HOLE_CENTERS_100),
        "Z10019": lambda: FrameMaterial.create_z("Z10019", 100, 102, 53, 49, 15.0, 1.9, FrameMaterialHelper.WEB_HOLE_CENTERS_100),
        
        "Z15010": lambda: FrameMaterial.create_z("Z15010", 150, 152, 65, 61, 14.5, 1.0, FrameMaterialHelper.WEB_HOLE_CENTERS_150),
        "Z15012": lambda: FrameMaterial.create_z("Z15012", 150, 152, 65, 61, 15.0, 1.2, FrameMaterialHelper.WEB_HOLE_CENTERS_150),
        "Z15015": lambda: FrameMaterial.create_z("Z15015", 150, 152, 65, 61, 16.0, 1.5, FrameMaterialHelper.WEB_HOLE_CENTERS_150),
        "Z15019": lambda: FrameMaterial.create_z("Z15019", 150, 152, 65, 61, 17.0, 1.9, FrameMaterialHelper.WEB_HOLE_CENTERS_150),
        "Z15024": lambda: FrameMaterial.create_z("Z15024", 150, 152, 66, 61, 18.5, 2.4, FrameMaterialHelper.WEB_HOLE_CENTERS_150),
        
        "Z20015": lambda: FrameMaterial.create_z("Z20015", 200, 203, 79, 74, 16.0, 1.5, FrameMaterialHelper.WEB_HOLE_CENTERS_200),
        "Z20019": lambda: FrameMaterial.create_z("Z20019", 200, 203, 79, 74, 19.5, 1.9, FrameMaterialHelper.WEB_HOLE_CENTERS_200),
        "Z20024": lambda: FrameMaterial.create_z("Z20024", 200, 203, 79, 74, 21.0, 2.4, FrameMaterialHelper.WEB_HOLE_CENTERS_200),
        
        "Z25019": lambda: FrameMaterial.create_z("Z25019", 250, 254, 79, 74, 19.0, 1.9, FrameMaterialHelper.WEB_HOLE_CENTERS_250),
        "Z25024": lambda: FrameMaterial.create_z("Z25024", 250, 254, 79, 74, 20.5, 2.4, FrameMaterialHelper.WEB_HOLE_CENTERS_250),
        
        "Z30024": lambda: FrameMaterial.create_z("Z30024", 300, 300, 100, 93, 28.0, 2.4, FrameMaterialHelper.WEB_HOLE_CENTERS_300),
        "Z30030": lambda: FrameMaterial.create_z("Z30030", 300, 300, 100, 93, 31.5, 3.0, FrameMaterialHelper.WEB_HOLE_CENTERS_300),
        
        "Z35024": lambda: FrameMaterial.create_z("Z35024", 350, 350, 129, 122, 29.5, 2.4, FrameMaterialHelper.WEB_HOLE_CENTERS_350),
        "Z35030": lambda: FrameMaterial.create_z("Z35030", 350, 350, 129, 121, 30.0, 3.0, FrameMaterialHelper.WEB_HOLE_CENTERS_350),
        
        "Z40024": lambda: FrameMaterial.create_z("Z40024", 400, 400, 100, 93, 35.0, 2.4, FrameMaterialHelper.WEB_HOLE_CENTERS_400),
        "Z40030": lambda: FrameMaterial.create_z("Z40030", 400, 400, 100, 93, 33.5, 3.0, FrameMaterialHelper.WEB_HOLE_CENTERS_400),
    }
    
    # TopHat sections catalog (C# Ref: Lines 122-137)
    TH_SECTIONS = {
        "TH064075": lambda: FrameMaterial.create_th("TH064075", 64, 97, 64, 0.75),
        "TH064100": lambda: FrameMaterial.create_th("TH064100", 64, 97, 64, 1.00),
        "TH064120": lambda: FrameMaterial.create_th("TH064120", 64, 97, 64, 1.20),
        
        "TH096075": lambda: FrameMaterial.create_th("TH096075", 96, 109, 96, 0.75),
        "TH096100": lambda: FrameMaterial.create_th("TH096100", 96, 109, 96, 1.00),
        "TH096120": lambda: FrameMaterial.create_th("TH096120", 96, 109, 96, 1.20),
        
        "TH120070": lambda: FrameMaterial.create_th("TH120070", 120, 154, 120, 0.7),
        "TH120090": lambda: FrameMaterial.create_th("TH120090", 120, 154, 120, 0.9),
        
        "RB22": lambda: FrameMaterial.create_th("RB22", 22, 64, 22, 0.42),
        "RB40": lambda: FrameMaterial.create_th("RB40", 40, 90, 40, 0.75),
    }
    
    # SHS sections catalog (C# Ref: Lines 139-145)
    SHS_SECTIONS = {
        "SHS07507525": lambda: FrameMaterial.create_shs("SHS07507525", 75, 75, 2.5),
        "SHS07507540": lambda: FrameMaterial.create_shs("SHS07507540", 75, 75, 4.0),
        "SHS10010040": lambda: FrameMaterial.create_shs("SHS10010040", 100, 100, 4.0),
    }
    
    # PAD sections catalog (C# Ref: Lines 147-163)
    PAD_SECTIONS = {
        "NRDJ064": lambda: FrameMaterial.create_pad_stile("NRDJ064", 64, 66, 25, 1.6, 0, 0, 0),
        "NRDJ100": lambda: FrameMaterial.create_pad_stile("NRDJ100", 100, 104, 25, 1.6, 0, 0, 0),
        "NRDJ120": lambda: FrameMaterial.create_pad_stile("NRDJ120", 120, 122, 25, 1.6, 0, 0, 0),
        "NRDJ150": lambda: FrameMaterial.create_pad_stile("NRDJ150", 150, 154, 25, 1.6, 0, 0, 0),
        
        "PADJ06427": lambda: FrameMaterial.create_pad_stile("PADJ06427", 64, 66, 57, 1.6, 27, 27, 24),
        "PADJ10027": lambda: FrameMaterial.create_pad_stile("PADJ10027", 100, 104, 57, 1.6, 27, 27, 24),
        "PADJ12027": lambda: FrameMaterial.create_pad_stile("PADJ12027", 120, 122, 57, 1.6, 27, 27, 24),
        "PADJ15027": lambda: FrameMaterial.create_pad_stile("PADJ15027", 150, 154, 57, 1.6, 27, 27, 24),
        
        "PADJ06427T": lambda: FrameMaterial.create_pad_stile("PADJ06427T", 64, 66, 70, 1.6, 40, 27, 37),
        "PADJ10027T": lambda: FrameMaterial.create_pad_stile("PADJ10027T", 100, 104, 70, 1.6, 40, 27, 37),
        "PADJ12027T": lambda: FrameMaterial.create_pad_stile("PADJ12027T", 120, 122, 70, 1.6, 40, 27, 37),
        "PADJ15027T": lambda: FrameMaterial.create_pad_stile("PADJ15027T", 150, 154, 70, 1.6, 40, 27, 37),
    }
    
    # Floor joist sections (C# Ref: Lines 165-196)
    FLOOR_SECTIONS = {
        "J11510": lambda: FrameMaterial.create_c("J11510", 115, False, 115, 45, 11.8, 1.0, FrameMaterialHelper.WEB_HOLE_CENTERS_100),
        "J11512": lambda: FrameMaterial.create_c("J11512", 115, False, 115, 45, 12.3, 1.2, FrameMaterialHelper.WEB_HOLE_CENTERS_100),
        "J11515": lambda: FrameMaterial.create_c("J11515", 115, False, 115, 45, 13.1, 1.5, FrameMaterialHelper.WEB_HOLE_CENTERS_100),
        "J11519": lambda: FrameMaterial.create_c("J11519", 115, False, 115, 45, 14.1, 1.9, FrameMaterialHelper.WEB_HOLE_CENTERS_100),
        
        "J18210": lambda: FrameMaterial.create_c("J18210", 182, False, 182, 51, 12.3, 1.0, FrameMaterialHelper.WEB_HOLE_CENTERS_150),
        "J18212": lambda: FrameMaterial.create_c("J18212", 182, False, 182, 51, 12.8, 1.2, FrameMaterialHelper.WEB_HOLE_CENTERS_150),
        "J18215": lambda: FrameMaterial.create_c("J18215", 182, False, 182, 51, 13.6, 1.5, FrameMaterialHelper.WEB_HOLE_CENTERS_150),
        "J18219": lambda: FrameMaterial.create_c("J18219", 182, False, 182, 51, 14.7, 1.9, FrameMaterialHelper.WEB_HOLE_CENTERS_150),
        "J18224": lambda: FrameMaterial.create_c("J18224", 182, False, 182, 51, 16.0, 2.4, FrameMaterialHelper.WEB_HOLE_CENTERS_150),
        
        "J23512": lambda: FrameMaterial.create_c("J23512", 235, False, 235, 64, 10.8, 1.2, FrameMaterialHelper.WEB_HOLE_CENTERS_200),
        "J23515": lambda: FrameMaterial.create_c("J23515", 235, False, 235, 64, 11.6, 1.5, FrameMaterialHelper.WEB_HOLE_CENTERS_200),
        "J23519": lambda: FrameMaterial.create_c("J23519", 235, False, 235, 64, 15.2, 1.9, FrameMaterialHelper.WEB_HOLE_CENTERS_200),
        "J23524": lambda: FrameMaterial.create_c("J23524", 235, False, 235, 64, 16.5, 2.4, FrameMaterialHelper.WEB_HOLE_CENTERS_200),
        
        "J28319": lambda: FrameMaterial.create_c("J28319", 283, False, 283, 64, 16.2, 1.9, FrameMaterialHelper.WEB_HOLE_CENTERS_250),
        "J28324": lambda: FrameMaterial.create_c("J28324", 283, False, 283, 64, 17.5, 2.4, FrameMaterialHelper.WEB_HOLE_CENTERS_250),
        
        # Bearer sections
        "B11519": lambda: FrameMaterial.create_c("B11519", 115, False, 116, 52, 0, 1.9, FrameMaterialHelper.WEB_HOLE_CENTERS_100),
        "B18219": lambda: FrameMaterial.create_c("B18219", 182, False, 183, 58, 0, 1.9, FrameMaterialHelper.WEB_HOLE_CENTERS_150),
        "B18224": lambda: FrameMaterial.create_c("B18224", 182, False, 183, 58, 0, 2.4, FrameMaterialHelper.WEB_HOLE_CENTERS_150),
        "B23519": lambda: FrameMaterial.create_c("B23519", 235, False, 236, 72, 0, 1.9, FrameMaterialHelper.WEB_HOLE_CENTERS_200),
        "B23524": lambda: FrameMaterial.create_c("B23524", 235, False, 236, 72, 0, 2.4, FrameMaterialHelper.WEB_HOLE_CENTERS_200),
        "B28319": lambda: FrameMaterial.create_c("B28319", 283, False, 283, 72, 0, 1.9, FrameMaterialHelper.WEB_HOLE_CENTERS_250),
        "B28324": lambda: FrameMaterial.create_c("B28324", 283, False, 283, 72, 0, 2.4, FrameMaterialHelper.WEB_HOLE_CENTERS_250),
    }
    
    # SRDJ sections (C# Ref: Lines 198-204)
    SRDJ_SECTIONS = {
        "SRDTS64": lambda: FrameMaterial.create_side_roller_door_jamb("SRDTS64", 64, 69.8, 102, 35, 1.9),
        "SRDTS120": lambda: FrameMaterial.create_side_roller_door_jamb("SRDTS120", 120, 125.2, 135, 43, 2.4),
        "SRDZ100": lambda: FrameMaterial.create_side_roller_door_jamb("SRDZ100", 100, 107.2, 151, 45, 1.9),
        "SRDZ150": lambda: FrameMaterial.create_side_roller_door_jamb("SRDZ150", 150, 157.2, 106, 40, 2.4),
    }
    
    # Combined material map
    _material_cache: Dict[str, FrameMaterial] = {}
    
    @classmethod
    def get_frame_material(cls, name: str) -> FrameMaterial:
        """Get frame material by name.
        
        C# Ref: Lines 11-19
        """
        # Check cache first
        if name in cls._material_cache:
            return cls._material_cache[name]
        
        # Check all catalogs
        name_upper = name.upper()
        
        # Try all catalogs
        for catalog in [cls.C_SECTIONS, cls.Z_SECTIONS, cls.TH_SECTIONS, 
                       cls.SHS_SECTIONS, cls.PAD_SECTIONS, cls.FLOOR_SECTIONS,
                       cls.SRDJ_SECTIONS]:
            if name_upper in catalog:
                material = catalog[name_upper]()
                cls._material_cache[name] = material
                return material
        
        # Try aliases (C# Ref: Lines 237-296)
        aliases = {
            "C100": "C10010", "C150": "C15024", "C200": "C20019",
            "C250": "C25024", "C300": "C30030", "C350": "C35030", "C400": "C40030",
            "2C100": "2C10010", "2C150": "2C15024", "2C200": "2C20019",
            "2C250": "2C25024", "2C300": "2C30030", "2C350": "2C35030", "2C400": "2C40030",
            "Z100": "Z10010", "Z150": "Z15024", "Z200": "Z20019",
            "Z250": "Z25024", "Z300": "Z30030", "Z350": "Z35030", "Z400": "Z40030",
            "TH96": "TH096100", "TH12070": "TH120070", "TH12090": "TH120090",
            "TH64100": "TH064100", "TH64120": "TH064120",
            "SHS75": "SHS07507525", "SHS100": "SHS10010040",
            "RHS7525": "SHS07507525", "RHS7540": "SHS07507540", "RHS1040": "SHS10010040",
            "NPAD64": "NRDJ064", "NPAD100": "NRDJ100", "NPAD120": "NRDJ120", "NPAD150": "NRDJ150",
            "PAD64": "PADJ06427", "PAD100": "PADJ10027", "PAD120": "PADJ12027", "PAD150": "PADJ15027",
            "J115": "J11510", "J182": "J18210", "J235": "J23512", "J283": "J28319",
            "B115": "B11519", "B182": "B18219", "B235": "B23519", "B283": "B28319",
            "SRDTS": "SRDTS64", "SRDZ": "SRDZ100",
        }
        
        if name_upper in aliases:
            return cls.get_frame_material(aliases[name_upper])
        
        raise ValueError(f"Frame material not found: {name}")
    
    @classmethod
    def get_all_materials_by_type(cls, material_type: FrameMaterialType) -> List[FrameMaterial]:
        """Get all materials of a specific type.
        
        Returns a list of all materials with the specified type.
        """
        materials = []
        
        if material_type == FrameMaterialType.C:
            for name, factory in cls.C_SECTIONS.items():
                materials.append(factory())
        elif material_type == FrameMaterialType.Z:
            for name, factory in cls.Z_SECTIONS.items():
                materials.append(factory())
        elif material_type == FrameMaterialType.TH:
            for name, factory in cls.TH_SECTIONS.items():
                materials.append(factory())
        elif material_type == FrameMaterialType.SHS:
            for name, factory in cls.SHS_SECTIONS.items():
                materials.append(factory())
        elif material_type == FrameMaterialType.PAD:
            for name, factory in cls.PAD_SECTIONS.items():
                materials.append(factory())
        elif material_type == FrameMaterialType.SRDJ:
            for name, factory in cls.SRDJ_SECTIONS.items():
                materials.append(factory())
        
        return materials
    
    @classmethod
    def get_frame_material_single(cls, name: str) -> FrameMaterial:
        """Get single frame material (not B2B).
        
        C# Ref: Lines 21-31
        """
        mat = cls.get_frame_material(name)
        if not mat.is_b2b:
            return mat
        
        # Get single version
        single_name = name[1:] if name.startswith("2") else name
        return cls.get_frame_material(single_name)


class CladdingProfileHelper:
    """Helper for creating cladding profiles.
    
    C# Reference: CladdingProfileHelper.cs
    """
    
    @staticmethod
    def create_sample() -> List[Vec2]:
        """Create sample profile.
        
        C# Ref: Lines 12-22
        """
        return [
            Vec2(0, 0),
            Vec2(150, 0),
            Vec2(160, 30),
            Vec2(190, 30),
            Vec2(200, 0)
        ]
    
    @staticmethod
    def create_corrugated(profile_height: float = 19, num_segments: int = 8) -> List[Vec2]:
        """Create corrugated profile.
        
        C# Ref: Lines 24-41
        """
        import math
        
        num_pts = num_segments + 1
        profile = []
        
        step = math.pi * 2 / (num_pts - 1)
        
        for i in range(num_pts):
            a = step * i
            x = a * profile_height
            y = (1 + math.sin(a)) * profile_height / 2
            profile.append(Vec2(x, y))
        
        return profile
    
    @staticmethod
    def create_monoclad() -> List[Vec2]:
        """Create Monoclad profile.
        
        C# Ref: Lines 43-59
        """
        return [
            Vec2(0, 0),
            Vec2(7.3, 0),
            Vec2(19.7, 29),
            Vec2(51, 29),
            Vec2(63.4, 0),
            Vec2(106.3, 0),
            Vec2(109.2, 3.2),
            Vec2(130.7, 3.2),
            Vec2(152.2, 3.2),
            Vec2(155.1, 0),
            Vec2(187.9, 0)
        ]
    
    @staticmethod
    def create_monopanel() -> List[Vec2]:
        """Create Monopanel profile.
        
        C# Ref: Lines 61-71
        """
        return [
            Vec2(0, 0),
            Vec2(25, 0),
            Vec2(28, 12),
            Vec2(118, 12),
            Vec2(121, 0)
        ]
    
    @staticmethod
    def create_k_panel() -> List[Vec2]:
        """Create K-Panel profile.
        
        C# Ref: Lines 73-91
        """
        return [
            Vec2(0, 0),
            Vec2(40, 0),
            Vec2(43, 2),
            Vec2(73, 2),
            Vec2(76, 0),
            Vec2(116, 0),
            Vec2(119, 2),
            Vec2(149, 2),
            Vec2(152, 0),
            Vec2(192, 0),
            Vec2(200, 12),
            Vec2(212, 12),
            Vec2(220, 0)
        ]
    
    @staticmethod
    def create_sharpline() -> List[Vec2]:
        """Create Sharpline profile.
        
        C# Ref: Lines 93-103
        """
        return [
            Vec2(0, 0),
            Vec2(270, 0),
            Vec2(272, 25),
            Vec2(280, 25),
            Vec2(282, 0)
        ]
    
    @staticmethod
    def create_t_rib() -> List[Vec2]:
        """Create T-Rib profile.
        
        C# Ref: Lines 105-119
        """
        return [
            Vec2(0, 0),
            Vec2(42, 0),
            Vec2(45, 2),
            Vec2(85, 2),
            Vec2(88, 0),
            Vec2(130, 0),
            Vec2(147, 29),
            Vec2(173, 29),
            Vec2(190, 0)
        ]
    
    @staticmethod
    def create_metrib() -> List[Vec2]:
        """Create Metrib profile.
        
        C# Ref: Lines 121-139
        """
        return [
            Vec2(0, 0),
            Vec2(19, 0),
            Vec2(22, 2),
            Vec2(41, 2),
            Vec2(44, 0),
            Vec2(63, 0),
            Vec2(66, 2),
            Vec2(85, 2),
            Vec2(88, 0),
            Vec2(107, 0),
            Vec2(119, 22),
            Vec2(138, 22),
            Vec2(150, 0),
        ]
    
    @staticmethod
    def create_metclad() -> List[Vec2]:
        """Create Metclad profile.
        
        C# Ref: Lines 141-159
        """
        return [
            Vec2(0, 0),
            Vec2(26, 0),
            Vec2(29, 2),
            Vec2(55, 2),
            Vec2(58, 0),
            Vec2(84, 0),
            Vec2(87, 2),
            Vec2(113, 2),
            Vec2(116, 0),
            Vec2(142, 0),
            Vec2(151, 12),
            Vec2(161, 12),
            Vec2(170, 0),
        ]


class CladdingMaterialHelper:
    """Helper for cladding materials.
    
    C# Reference: CladdingMaterialHelper.cs
    """
    
    # Stramit cladding materials (C# Ref: Lines 12-89)
    CORRUGATED = CladdingMaterial(
        name="Corrugated",
        design="Corrugated",
        cover_width=762,
        rib_height=16,
        overlap=93,
        bmt=0.42,
        tct=0.48,
        profile=CladdingProfileHelper.create_corrugated(16),
        is_profile_rotated=False
    )
    
    MONOCLAD = CladdingMaterial(
        name="Monoclad",
        design="Monoclad",
        cover_width=762,
        rib_height=29,
        overlap=68,
        bmt=0.42,
        tct=0.48,
        profile=CladdingProfileHelper.create_monoclad(),
        is_profile_rotated=False
    )
    
    MONOPANEL = CladdingMaterial(
        name="M-Panel",
        design="M-Panel",
        cover_width=762,
        rib_height=12,
        overlap=93,
        bmt=0.48,
        tct=0.53,
        profile=CladdingProfileHelper.create_monopanel(),
        is_profile_rotated=True
    )
    
    K_PANEL = CladdingMaterial(
        name="K-Panel",
        design="K-Panel",
        cover_width=35,
        rib_height=40,
        overlap=31,
        bmt=0.35,
        tct=0.40,
        profile=CladdingProfileHelper.create_k_panel(),
        is_profile_rotated=False
    )
    
    HORIZONTAL_CORRUGATED = CladdingMaterial(
        name="Horizontal Corrugated",
        design="Corrugated",
        cover_width=762,
        rib_height=16,
        overlap=93,
        bmt=0.42,
        tct=0.47,
        profile=CladdingProfileHelper.create_corrugated(16),
        is_profile_rotated=True
    )
    
    SHARPLINE = CladdingMaterial(
        name="Sharpline",
        design="Sharpline",
        cover_width=290,
        rib_height=25,
        overlap=25,
        bmt=0.55,
        tct=0.55,
        profile=CladdingProfileHelper.create_sharpline(),
        is_profile_rotated=False
    )
    
    # Material catalog
    CLADDING_MATERIALS = {
        "CORRUGATED": CORRUGATED,
        "MONOCLAD": MONOCLAD,
        "M-PANEL": MONOPANEL,
        "K-PANEL": K_PANEL,
        "HORIZONTAL CORRUGATED": HORIZONTAL_CORRUGATED,
        "SHARPLINE": SHARPLINE,
    }
    
    @classmethod
    def get_cladding_material(cls, name: str) -> CladdingMaterial:
        """Get cladding material by name."""
        name_upper = name.upper()
        if name_upper in cls.CLADDING_MATERIALS:
            return cls.CLADDING_MATERIALS[name_upper]
        raise ValueError(f"Cladding material not found: {name}")


class FastenerMaterialHelper:
    """Helper for fastener materials.
    
    C# Reference: FastenerMaterialHelper.cs
    """
    
    # Bolt catalog (C# Ref: Lines 21-27)
    BOLTS = {
        "M12x30": lambda: FastenerMaterial.create_bolt("M12x30", 50, 25, 12),
        "M16x33": lambda: FastenerMaterial.create_bolt("M16x33", 50, 29, 16),
        "M16x45": lambda: FastenerMaterial.create_bolt("M16x45", 50, 29, 16),
    }
    
    @classmethod
    def get_fastener_material(cls, name: str) -> FastenerMaterial:
        """Get fastener material by name.
        
        C# Ref: Lines 11-14
        """
        name_upper = name.upper()
        if name_upper in cls.BOLTS:
            return cls.BOLTS[name_upper]()
        raise ValueError(f"Fastener material not found: {name}")


class FootingMaterialHelper:
    """Helper for creating footing materials.
    
    C# Reference: FootingMaterialHelper.cs
    """
    
    @staticmethod
    def create_footing(footing_type: FootingMaterialType, width: float, 
                      length: float, depth: float) -> FootingMaterial:
        """Create footing material.
        
        C# Ref: Lines 15-24
        """
        return FootingMaterial(
            footing_type=footing_type,
            width=width,
            length=length,
            depth=depth
        )
    
    @staticmethod
    def create_block(width: float, length: float, depth: float) -> FootingMaterial:
        """Create block footing.
        
        C# Ref: Lines 29-30
        """
        return FootingMaterialHelper.create_footing(
            FootingMaterialType.BLOCK, width, length, depth
        )
    
    @staticmethod
    def create_bored(diameter: float, depth: float) -> FootingMaterial:
        """Create bored footing.
        
        C# Ref: Lines 32-33
        """
        return FootingMaterialHelper.create_footing(
            FootingMaterialType.BORED, diameter, diameter, depth
        )


# Flashing product definitions (placeholder)
# Note: The actual flashing products would need to be defined based on
# FdsFlashingProducts.cs which contains the specific profile geometries
class FlashingMaterialHelper:
    """Helper for flashing materials.
    
    C# Reference: FlashingMaterialHelper.cs
    This is a simplified version - full implementation would require
    porting all flashing product definitions from FdsFlashingProducts.cs
    """
    
    @staticmethod
    def create_flashing(name: str, description: str = "", 
                       thickness: float = 0.55) -> FlashingMaterial:
        """Create a flashing material.
        
        C# Ref: Lines 657-665
        """
        return FlashingMaterial(
            name=name,
            description=description,
            thickness=thickness,
            profile=[],
            front_faces=[],
            cap_outline=[]
        )


class BracketMaterialHelper:
    """Helper for bracket materials.
    
    C# Reference: BracketMaterialHelper.cs
    Note: This is a simplified version. Full implementation would require
    loading STL meshes from resources.
    """
    
    # Bracket catalog (simplified - full implementation would load from STL files)
    # C# Ref: BracketMaterialHelper.Brackets.cs
    
    @staticmethod
    def get_bracket_material(mesh_name: str, name: str) -> BracketMaterial:
        """Get bracket material by mesh name.
        
        C# Ref: GetBracketMaterial method
        """
        return BracketMaterial(
            name=name,
            mesh_name=mesh_name
        )
    
    # Apex brackets
    class ApexBrackets:
        """Apex bracket definitions."""
        APB11100_UF = lambda: BracketMaterialHelper.get_bracket_material("Apex_Brackets.APB11100-UF", "APB11100-UF")
        APB15100_UF = lambda: BracketMaterialHelper.get_bracket_material("Apex_Brackets.APB15100-UF", "APB15100-UF")
        APB22100_UF = lambda: BracketMaterialHelper.get_bracket_material("Apex_Brackets.APB22100-UF", "APB22100-UF")
        # ... more would be defined in full implementation
    
    # Base cleat brackets  
    class BaseCleatBrackets:
        """Base cleat bracket definitions."""
        DB501H2 = lambda: BracketMaterialHelper.get_bracket_material("Base_Cleat_Brackets.DB501H2", "DB501H2")
        DB1002H = lambda: BracketMaterialHelper.get_bracket_material("Base_Cleat_Brackets.DB1002H", "DB1002H")
        # ... more would be defined in full implementation
    
    # Eave purlin brackets
    class EavePurlinBrackets:
        """Eave purlin bracket definitions."""
        EPB64S51_PH = lambda: BracketMaterialHelper.get_bracket_material("Eave_Purlin_Brackets.EPB64S51-PH", "EPB64S51-PH")
        EPB100S51_PH = lambda: BracketMaterialHelper.get_bracket_material("Eave_Purlin_Brackets.EPB100S51-PH", "EPB100S51-PH")
        # ... more would be defined in full implementation