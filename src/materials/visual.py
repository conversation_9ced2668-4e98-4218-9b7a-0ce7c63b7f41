"""Visual properties and texture mapping for materials.

This module provides visual property management, color utilities,
and texture mapping functionality for the material system.
"""

from dataclasses import dataclass, field
from typing import Optional, Tuple, Dict, List
from enum import Enum

from .base import ColorMaterial


class MaterialFinish(Enum):
    """Material finish types."""
    COLORBOND = "CB"
    ZINCALUME = "ZA"
    POWDER_COAT = "PC"
    ANODIZED = "AN"
    MILL = "ML"
    CUSTOM = ""


@dataclass
class TextureMapping:
    """Texture mapping coordinates for materials."""
    u_scale: float = 1.0
    v_scale: float = 1.0
    u_offset: float = 0.0
    v_offset: float = 0.0
    rotation: float = 0.0  # In radians
    
    def apply_to_uv(self, u: float, v: float) -> Tuple[float, float]:
        """Apply texture mapping transformation to UV coordinates."""
        # Apply scale
        u_scaled = u * self.u_scale
        v_scaled = v * self.v_scale
        
        # Apply rotation if needed
        if self.rotation != 0:
            import math
            cos_r = math.cos(self.rotation)
            sin_r = math.sin(self.rotation)
            u_rotated = u_scaled * cos_r - v_scaled * sin_r
            v_rotated = u_scaled * sin_r + v_scaled * cos_r
            u_scaled, v_scaled = u_rotated, v_rotated
        
        # Apply offset
        u_final = u_scaled + self.u_offset
        v_final = v_scaled + self.v_offset
        
        return u_final, v_final


@dataclass
class MaterialAppearance:
    """Visual appearance properties for materials."""
    base_color: Optional[ColorMaterial] = None
    metallic: float = 0.0  # 0.0 = non-metallic, 1.0 = metallic
    roughness: float = 0.5  # 0.0 = smooth, 1.0 = rough
    opacity: float = 1.0   # 0.0 = transparent, 1.0 = opaque
    emissive_color: Optional[ColorMaterial] = None
    emissive_intensity: float = 0.0
    texture_mapping: Optional[TextureMapping] = None
    
    def is_transparent(self) -> bool:
        """Check if material is transparent."""
        return self.opacity < 1.0
    
    def is_emissive(self) -> bool:
        """Check if material emits light."""
        return self.emissive_intensity > 0 and self.emissive_color is not None


class ColorLibrary:
    """Library of predefined colors for materials."""
    
    # Australian Colorbond colors
    COLORBOND_COLORS = {
        "ZINCALUME": ColorMaterial.from_hex("Zincalume", "ZA", "999999"),
        "BASALT": ColorMaterial.from_hex("Basalt", "CB", "6D6C6E"),
        "BLUEGUM": ColorMaterial.from_hex("Bluegum", "CB", "969799"),
        "BUSHLAND": ColorMaterial.from_hex("Bushland", "CB", "8A8A7F"),
        "CLASSIC_CREAM": ColorMaterial.from_hex("Classic Cream", "CB", "E9DCB8"),
        "COTTAGE_GREEN": ColorMaterial.from_hex("Cottage Green", "CB", "304C3C"),
        "COVE": ColorMaterial.from_hex("Cove", "CB", "A59F8A"),
        "DOVER_WHITE": ColorMaterial.from_hex("Dover White", "CB", "F9FBF1"),
        "DEEP_OCEAN": ColorMaterial.from_hex("Deep Ocean", "CB", "364152"),
        "DUNE": ColorMaterial.from_hex("Dune", "CB", "B1ADA3"),
        "EVENING_HAZE": ColorMaterial.from_hex("Evening Haze", "CB", "C5C2AA"),
        "GULLY": ColorMaterial.from_hex("Gully", "CB", "857E73"),
        "HEADLAND": ColorMaterial.from_hex("Headland", "CB", "975540"),
        "IRONSTONE": ColorMaterial.from_hex("Ironstone", "CB", "3E434C"),
        "JASPER": ColorMaterial.from_hex("Jasper", "CB", "6C6153"),
        "LOFT": ColorMaterial.from_hex("Loft", "CB", "44393D"),
        "MANGROVE": ColorMaterial.from_hex("Mangrove", "CB", "737562"),
        "MANOR_RED": ColorMaterial.from_hex("Manor Red", "CB", "5E1D0E"),
        "MONUMENT": ColorMaterial.from_hex("Monument", "CB", "323233"),
        "NIGHT_SKY": ColorMaterial.from_hex("Night Sky", "CB", "000000"),
        "PALE_EUCALYPT": ColorMaterial.from_hex("Pale Eucalypt", "CB", "7C846A"),
        "PAPERBARK": ColorMaterial.from_hex("Paperbark", "CB", "CABFA4"),
        "SANDBANK": ColorMaterial.from_hex("Sandbank", "CB", "D1B988"),
        "SHALE_GREY": ColorMaterial.from_hex("Shale Grey", "CB", "BDBFBA"),
        "SURFMIST": ColorMaterial.from_hex("Surfmist", "CB", "E4E2D5"),
        "TERRAIN": ColorMaterial.from_hex("Terrain", "CB", "67432E"),
        "WALLABY": ColorMaterial.from_hex("Wallaby", "CB", "7F7C78"),
        "WILDERNESS": ColorMaterial.from_hex("Wilderness", "CB", "65796D"),
        "WINDSPRAY": ColorMaterial.from_hex("Windspray", "CB", "888B8A"),
        "WOODLAND_GREY": ColorMaterial.from_hex("Woodland Grey", "CB", "4B4C46"),
    }
    
    # New Zealand Colorsteel colors
    COLORSTEEL_COLORS = {
        "ZINCALUME": ColorMaterial.from_hex("Zincalume", "ZA", "999999"),
        "AZURE": ColorMaterial.from_rgb("Azure", "CB", 77, 109, 125),
        "BONE_WHITE": ColorMaterial.from_rgb("Bone White", "CB", 182, 182, 168),
        "CLOUD": ColorMaterial.from_rgb("Cloud", "CB", 218, 217, 204),
        "DESERT_SAND": ColorMaterial.from_rgb("Desert Sand", "CB", 184, 168, 144),
        "EBONY": ColorMaterial.from_rgb("Ebony", "CB", 43, 43, 44),
        "GREY_FRIARS": ColorMaterial.from_rgb("Grey Friars", "CB", 71, 74, 77),
        "GULL_GREY": ColorMaterial.from_rgb("Gull Grey", "CB", 172, 176, 173),
        "IRONSAND": ColorMaterial.from_rgb("Ironsand", "CB", 65, 63, 61),
        "KARAKA": ColorMaterial.from_rgb("Karaka", "CB", 59, 61, 54),
        "LICHEN": ColorMaterial.from_rgb("Lichen", "CB", 136, 128, 107),
        "LIGNITE": ColorMaterial.from_rgb("Lignite", "CB", 77, 69, 64),
        "MIST_GREEN": ColorMaterial.from_rgb("Mist Green", "CB", 116, 126, 106),
        "NEW_DENIM_BLUE": ColorMaterial.from_rgb("New Denim Blue", "CB", 72, 78, 87),
        "PACIFIC_BLUE": ColorMaterial.from_rgb("Pacific Blue", "CB", 72, 91, 104),
        "PERMANENT_GREEN": ColorMaterial.from_rgb("Permanent Green", "CB", 55, 80, 70),
        "PIONEER_RED": ColorMaterial.from_rgb("Pioneer Red", "CB", 129, 63, 57),
        "RIVERGUM": ColorMaterial.from_rgb("Rivergum", "CB", 216, 202, 184),
        "SANDSTONE_GREY": ColorMaterial.from_rgb("Sandstone Grey", "CB", 125, 125, 123),
        "SCORIA": ColorMaterial.from_rgb("Scoria", "CB", 103, 59, 54),
        "SMOOTH_CREAM": ColorMaterial.from_rgb("Smooth Cream", "CB", 229, 209, 170),
        "STONE": ColorMaterial.from_rgb("Stone", "CB", 152, 137, 121),
        "STORM_BLUE": ColorMaterial.from_rgb("Storm Blue", "CB", 62, 73, 84),
        "TERRACOTTA": ColorMaterial.from_rgb("Terracotta", "CB", 164, 102, 75),
        "TITANIA": ColorMaterial.from_rgb("Titania", "CB", 215, 212, 200),
    }
    
    # Skylight colors
    SKYLIGHT_COLORS = {
        "CLEAR": ColorMaterial.from_rgb("Clear Skylight", "", 124, 221, 255, 200),
        "OPAL": ColorMaterial.from_rgb("Opal Skylight", "", 64, 64, 64, 200),
    }
    
    @classmethod
    def get_color(cls, name: str, library: str = "COLORBOND") -> Optional[ColorMaterial]:
        """Get a predefined color by name from specified library.
        
        Args:
            name: Color name (case insensitive, spaces converted to underscores)
            library: Color library ("COLORBOND", "COLORSTEEL", "SKYLIGHT")
            
        Returns:
            ColorMaterial if found, None otherwise
        """
        # Normalize name
        normalized_name = name.upper().replace(" ", "_")
        
        # Select library
        if library.upper() == "COLORBOND":
            colors = cls.COLORBOND_COLORS
        elif library.upper() == "COLORSTEEL":
            colors = cls.COLORSTEEL_COLORS
        elif library.upper() == "SKYLIGHT":
            colors = cls.SKYLIGHT_COLORS
        else:
            return None
            
        return colors.get(normalized_name)
    
    @classmethod
    def get_or_create_color(cls, color_string: str, 
                           default_name: str = "Monument",
                           default_library: str = "COLORBOND") -> ColorMaterial:
        """Get or create a color material from a string specification.
        
        Args:
            color_string: Color specification (name or "rgb(r,g,b[,a])")
            default_name: Default color name if not found
            default_library: Default library if not found
            
        Returns:
            ColorMaterial (existing or newly created)
        """
        if not color_string:
            return cls.get_color(default_name, default_library)
        
        # Check for RGB format
        if color_string.lower().startswith("rgb"):
            # Parse RGB values
            try:
                rgb_part = color_string[4:].split(')')[0]
                values = [float(v) for v in rgb_part.split(',')]
                
                # Convert from 0-1 range to 0-255 if needed
                if all(v <= 1.0 for v in values[:3]):
                    r = int(values[0] * 255)
                    g = int(values[1] * 255)
                    b = int(values[2] * 255)
                else:
                    r = int(values[0])
                    g = int(values[1])
                    b = int(values[2])
                
                a = int(values[3]) if len(values) > 3 else 255
                
                return ColorMaterial(
                    name="Custom Color",
                    finish="CB",
                    r=r, g=g, b=b, a=a
                )
            except (ValueError, IndexError):
                pass
        
        # Try to find in libraries
        for library in ["COLORBOND", "COLORSTEEL", "SKYLIGHT"]:
            color = cls.get_color(color_string, library)
            if color:
                return color
        
        # Return default
        return cls.get_color(default_name, default_library)


@dataclass
class MaterialProfile:
    """Profile definition for materials like cladding."""
    points: List[Tuple[float, float]] = field(default_factory=list)
    is_closed: bool = True
    thickness: float = 0.0
    
    def get_bounding_box(self) -> Tuple[float, float, float, float]:
        """Get the bounding box of the profile (min_x, min_y, max_x, max_y)."""
        if not self.points:
            return 0, 0, 0, 0
            
        xs = [p[0] for p in self.points]
        ys = [p[1] for p in self.points]
        
        return min(xs), min(ys), max(xs), max(ys)
    
    def get_width(self) -> float:
        """Get the width of the profile."""
        min_x, _, max_x, _ = self.get_bounding_box()
        return max_x - min_x
    
    def get_height(self) -> float:
        """Get the height of the profile."""
        _, min_y, _, max_y = self.get_bounding_box()
        return max_y - min_y


class MaterialVisualizer:
    """Helper class for material visualization."""
    
    @staticmethod
    def get_material_appearance(material_type: str, 
                               color: Optional[ColorMaterial] = None) -> MaterialAppearance:
        """Get default appearance for a material type.
        
        Args:
            material_type: Type of material ("metal", "glass", "concrete", etc.)
            color: Optional color override
            
        Returns:
            MaterialAppearance with appropriate settings
        """
        appearances = {
            "metal": MaterialAppearance(
                base_color=color or ColorLibrary.COLORBOND_COLORS["MONUMENT"],
                metallic=0.8,
                roughness=0.3
            ),
            "steel": MaterialAppearance(
                base_color=color or ColorLibrary.COLORBOND_COLORS["ZINCALUME"],
                metallic=0.9,
                roughness=0.2
            ),
            "glass": MaterialAppearance(
                base_color=color or ColorLibrary.SKYLIGHT_COLORS["CLEAR"],
                metallic=0.0,
                roughness=0.1,
                opacity=0.8
            ),
            "concrete": MaterialAppearance(
                base_color=color or ColorMaterial.from_rgb("Concrete", "", 128, 128, 128),
                metallic=0.0,
                roughness=0.8
            ),
            "wood": MaterialAppearance(
                base_color=color or ColorMaterial.from_rgb("Wood", "", 139, 69, 19),
                metallic=0.0,
                roughness=0.6
            ),
        }
        
        return appearances.get(material_type.lower(), MaterialAppearance(base_color=color))
    
    @staticmethod
    def interpolate_color(color1: ColorMaterial, color2: ColorMaterial, 
                         t: float) -> ColorMaterial:
        """Interpolate between two colors.
        
        Args:
            color1: First color
            color2: Second color
            t: Interpolation factor (0.0 = color1, 1.0 = color2)
            
        Returns:
            Interpolated color
        """
        t = max(0.0, min(1.0, t))  # Clamp to [0, 1]
        
        r = int(color1.r * (1 - t) + color2.r * t)
        g = int(color1.g * (1 - t) + color2.g * t)
        b = int(color1.b * (1 - t) + color2.b * t)
        a = int(color1.a * (1 - t) + color2.a * t)
        
        return ColorMaterial(
            name=f"Interpolated({color1.name}, {color2.name})",
            finish=color1.finish,
            r=r, g=g, b=b, a=a
        )