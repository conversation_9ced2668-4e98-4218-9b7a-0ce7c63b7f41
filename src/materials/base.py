"""Base material classes for BIM Backend.

This module corresponds to the Materials.cs file from the C# implementation:
- Materials.cs: Lines 1-588

Provides material definitions for building components.
"""

from dataclasses import dataclass, field
from enum import Enum
from typing import List, Dict, Optional, Tuple
import math

# Import geometry types from our previously implemented modules
# Using absolute imports to avoid relative import issues
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.geometry.primitives import Vec2, Vec3
from src.geometry.boxes import Box3
from src.geometry.basis import Basis3


# C# Ref: Lines 225-236 - public enum FastenerMaterialType
class FastenerMaterialType(Enum):
    """Type of fastener material.
    
    C# Reference: Materials.cs lines 225-236
    """
    # C# Ref: Line 230 - Unknown
    UNKNOWN = 0
    
    # C# Ref: Line 235 - Bolt
    BOLT = 1


# C# Ref: Lines 269-273 - public enum FootingMaterialType
class FootingMaterialType(Enum):
    """Type of footing material.
    
    C# Reference: Materials.cs lines 269-273
    """
    # C# Ref: Line 271 - Block
    BLOCK = 0
    
    # C# Ref: Line 272 - Bored
    BORED = 1


# C# Ref: Lines 465-503 - public enum FrameMaterialType
class FrameMaterialType(Enum):
    """Type of frame material.
    
    C# Reference: Materials.cs lines 465-503
    """
    # C# Ref: Line 470 - Unknown
    UNKNOWN = 0
    
    # C# Ref: Line 475 - C
    C = 1
    
    # C# Ref: Line 480 - TH
    TH = 2
    
    # C# Ref: Line 485 - Z
    Z = 3
    
    # C# Ref: Line 490 - SHS
    SHS = 4
    
    # C# Ref: Line 495 - PAD
    PAD = 5
    
    # C# Ref: Line 500 - SRDJ
    SRDJ = 6


# C# Ref: Lines 562-569 - public enum PunchingWhere
class PunchingWhere(Enum):
    """Location where punching occurs.
    
    C# Reference: Materials.cs lines 562-569
    """
    # C# Ref: Line 564 - Web
    WEB = 0
    
    # C# Ref: Line 565 - Flange
    FLANGE = 1
    
    # C# Ref: Line 566 - Center
    CENTER = 2
    
    # C# Ref: Line 567 - WebLeft
    WEB_LEFT = 3
    
    # C# Ref: Line 568 - WebRight
    WEB_RIGHT = 4


# C# Ref: Lines 52-66 - public struct BracketAttachment
@dataclass
class BracketAttachment:
    """Represents a bracket attachment point.
    
    C# Reference: Materials.cs lines 52-66
    C# Definition: public struct BracketAttachment
    """
    # C# Ref: Line 54 - public Vec3 Position { get; set; }
    position: Vec3
    
    # C# Ref: Line 55 - public Basis3 Basis { get; set; }
    basis: Basis3 = field(default_factory=lambda: Basis3.unit_xyz())
    
    # C# Ref: Lines 57-59 - Constructor with position only
    @staticmethod
    def from_position(position: Vec3) -> 'BracketAttachment':
        """Create bracket attachment with default basis.
        
        C# Ref: Lines 57-59 - public BracketAttachment(Vec3 position)
        """
        return BracketAttachment(position, Basis3.unit_xyz())


# C# Ref: Lines 45-50 - public class BracketMesh
@dataclass
class BracketMesh:
    """Represents a bracket mesh with attachments.
    
    C# Reference: Materials.cs lines 45-50
    C# Definition: public class BracketMesh
    """
    # C# Ref: Line 47 - public string Id { get; set; }
    id: str
    
    # C# Ref: Line 48 - public Mesh3d Mesh { get; set; }
    mesh: Optional['Mesh3d'] = None  # Forward reference, will be defined in mesh.py
    
    # C# Ref: Line 49 - public Dictionary<string, BracketAttachment> Attachments { get; set; }
    attachments: Dict[str, BracketAttachment] = field(default_factory=dict)


# C# Ref: Lines 10-43 - public class BracketMaterial
@dataclass
class BracketMaterial:
    """Represents a bracket material.
    
    C# Reference: Materials.cs lines 10-43
    C# Definition: public class BracketMaterial
    """
    # C# Ref: Line 12 - public string Name { get; set; }
    name: str = ""
    
    # C# Ref: Line 14 - public string MeshName { get; set; }
    mesh_name: str = ""
    
    # C# Ref: Line 16 - private BracketMesh _mesh = null;
    _mesh: Optional[BracketMesh] = field(default=None, init=False, repr=False)
    
    # C# Ref: Lines 18-22 - internal void InternalSetMesh(BracketMesh mesh)
    def internal_set_mesh(self, mesh: BracketMesh) -> None:
        """Set the bracket mesh internally.
        
        C# Ref: Lines 18-22 - InternalSetMesh
        """
        self.mesh_name = mesh.id
        self._mesh = mesh
    
    # C# Ref: Lines 24-30 - private void EnsureMeshCreated()
    def _ensure_mesh_created(self) -> None:
        """Ensure mesh is created.
        
        C# Ref: Lines 24-30 - EnsureMeshCreated
        """
        if self._mesh is None:
            # Note: BracketMaterialHelper.GetBracketMesh would be implemented separately
            raise NotImplementedError("BracketMaterialHelper not yet implemented")
    
    # C# Ref: Lines 32-36 - public BracketMesh GetBracketMesh()
    def get_bracket_mesh(self) -> BracketMesh:
        """Get the bracket mesh.
        
        C# Ref: Lines 32-36 - GetBracketMesh
        """
        self._ensure_mesh_created()
        return self._mesh
    
    # C# Ref: Line 38 - public Mesh3d GetMesh() => GetBracketMesh().Mesh;
    def get_mesh(self) -> 'Mesh3d':
        """Get the 3D mesh.
        
        C# Ref: Line 38 - GetMesh
        """
        return self.get_bracket_mesh().mesh
    
    # C# Ref: Line 40 - public Box3 GetBounds() => GetMesh().Bounds;
    def get_bounds(self) -> Box3:
        """Get the mesh bounds.
        
        C# Ref: Line 40 - GetBounds
        """
        return self.get_mesh().bounds
    
    # C# Ref: Line 42 - public Dictionary<string, BracketAttachment> GetAttachments()
    def get_attachments(self) -> Dict[str, BracketAttachment]:
        """Get the bracket attachments.
        
        C# Ref: Line 42 - GetAttachments
        """
        return self.get_bracket_mesh().attachments


# C# Ref: Lines 68-95 - public class CladdingMaterial
@dataclass
class CladdingMaterial:
    """Represents cladding material properties.
    
    C# Reference: Materials.cs lines 68-95
    C# Definition: public class CladdingMaterial
    """
    # C# Ref: Line 70 - public string Name { get; set; }
    name: str = ""
    
    # C# Ref: Line 72 - public string Design { get; set; }
    design: str = ""
    
    # C# Ref: Line 74 - public double CoverWidth { get; set; }
    cover_width: float = 0.0
    
    # C# Ref: Line 76 - public double RibHeight { get; set; }
    rib_height: float = 0.0
    
    # C# Ref: Line 78 - public double Overlap { get; set; }
    overlap: float = 0.0
    
    # C# Ref: Lines 79-82 - public double Bmt { get; set; } (Base metal thickness)
    bmt: float = 0.0
    
    # C# Ref: Lines 84-87 - public double Tct { get; set; } (Total coated thickness)
    tct: float = 0.0
    
    # C# Ref: Line 89 - public Vec2[] Profile { get; set; }
    profile: List[Vec2] = field(default_factory=list)
    
    # C# Ref: Lines 91-94 - public bool IsProfileRotated { get; set; }
    is_profile_rotated: bool = False


# C# Ref: Lines 97-168 - public class ColorMaterial
@dataclass
class ColorMaterial:
    """Represents color material properties.
    
    C# Reference: Materials.cs lines 97-168
    C# Definition: public class ColorMaterial
    """
    # C# Ref: Line 99 - public string Name { get; set; }
    name: str = ""
    
    # C# Ref: Lines 101-104 - public string Finish { get; set; }
    finish: str = ""
    
    # C# Ref: Line 106 - public byte R { get; set; }
    r: int = 0
    
    # C# Ref: Line 107 - public byte G { get; set; }
    g: int = 0
    
    # C# Ref: Line 108 - public byte B { get; set; }
    b: int = 0
    
    # C# Ref: Line 109 - public byte A { get; set; }
    a: int = 255
    
    # C# Ref: Lines 111-136 - public static ColorMaterial FromHex
    @staticmethod
    def from_hex(name: str, finish: str, hex_color: str) -> 'ColorMaterial':
        """Create color material from hex string.
        
        C# Ref: Lines 111-136 - FromHex
        """
        if len(hex_color) != 6:
            raise ValueError("Expected hex format: FF00FF")
        
        # C# Ref: Lines 118-124 - convert function
        def convert(index: int) -> int:
            start_index = (index - 1) * 2
            value = hex_color[start_index:start_index + 2]
            return int(value, 16)
        
        # C# Ref: Lines 126-135
        return ColorMaterial(
            name=name,
            finish=finish,
            r=convert(1),
            g=convert(2),
            b=convert(3),
            a=255
        )
    
    # C# Ref: Lines 137-148 - public static ColorMaterial FromRgb
    @staticmethod
    def from_rgb(name: str, finish: str, r: int, g: int, b: int, a: int = 255) -> 'ColorMaterial':
        """Create color material from RGB values.
        
        C# Ref: Lines 137-148 - FromRgb
        """
        return ColorMaterial(
            name=name,
            finish=finish,
            r=r,
            g=g,
            b=b,
            a=a
        )
    
    # C# Ref: Lines 150-167 - public static ColorMaterial FromCMYK
    @staticmethod
    def from_cmyk(name: str, finish: str, cyan: float, magenta: float, 
                  yellow: float, black: float, alpha: float = 1.0) -> 'ColorMaterial':
        """Create color material from CMYK values.
        
        C# Ref: Lines 150-167 - FromCMYK
        """
        # C# Ref: Lines 152-155 - CMYK to RGB conversion
        r = int(255 * (1 - cyan) * (1 - black))
        g = int(255 * (1 - magenta) * (1 - black))
        b = int(255 * (1 - yellow) * (1 - black))
        a = int(255 * alpha)
        
        return ColorMaterial(
            name=name,
            finish=finish,
            r=r,
            g=g,
            b=b,
            a=a
        )


# C# Ref: Lines 170-183 - public class FlashingMaterial
@dataclass
class FlashingMaterial:
    """Represents flashing material properties.
    
    C# Reference: Materials.cs lines 170-183
    C# Definition: public class FlashingMaterial
    """
    # C# Ref: Line 172 - public string Name { get; set; }
    name: str = ""
    
    # C# Ref: Line 174 - public string Description { get; set; }
    description: str = ""
    
    # C# Ref: Line 176 - public double Thickness { get; set; }
    thickness: float = 0.0
    
    # C# Ref: Line 178 - public List<Vec2> Profile { get; set; }
    profile: List[Vec2] = field(default_factory=list)
    
    # C# Ref: Line 180 - public List<int> FrontFaces { get; set; }
    front_faces: List[int] = field(default_factory=list)
    
    # C# Ref: Line 182 - public List<Vec2> CapOutline { get; set; }
    cap_outline: List[Vec2] = field(default_factory=list)


# C# Ref: Lines 186-199 - public class DownpipeMaterial
@dataclass
class DownpipeMaterial:
    """Represents downpipe material properties.
    
    C# Reference: Materials.cs lines 186-199
    C# Definition: public class DownpipeMaterial
    """
    # C# Ref: Line 188 - public string Name { get; set; }
    name: str = ""
    
    # C# Ref: Line 190 - public string Description { get; set; }
    description: str = ""
    
    # C# Ref: Line 192 - public double Thickness { get; set; }
    thickness: float = 0.0
    
    # C# Ref: Line 194 - public List<Vec2> Profile { get; set; }
    profile: List[Vec2] = field(default_factory=list)
    
    # C# Ref: Line 196 - public List<int> FrontFaces { get; set; }
    front_faces: List[int] = field(default_factory=list)
    
    # C# Ref: Line 198 - public List<Vec2> CapOutline { get; set; }
    cap_outline: List[Vec2] = field(default_factory=list)


# C# Ref: Lines 201-224 - public class FastenerMaterial
@dataclass
class FastenerMaterial:
    """Represents fastener material properties.
    
    C# Reference: Materials.cs lines 201-224
    C# Definition: public class FastenerMaterial
    """
    # C# Ref: Line 203 - public string Name { get; set; }
    name: str = ""
    
    # C# Ref: Line 205 - public string Description { get; set; }
    description: str = ""
    
    # C# Ref: Line 207 - public double Length { get; set; }
    length: float = 0.0
    
    # C# Ref: Line 208 - public double HeadDiameter { get; set; }
    head_diameter: float = 0.0
    
    # C# Ref: Line 209 - public double ClearanceHoleDiameter { get; set; }
    clearance_hole_diameter: float = 0.0
    
    # C# Ref: Line 210 - public FastenerMaterialType MaterialType { get; set; }
    material_type: FastenerMaterialType = FastenerMaterialType.UNKNOWN
    
    # C# Ref: Lines 212-223 - public static FastenerMaterial CreateBolt
    @staticmethod
    def create_bolt(name: str, length: int, head_diameter: float, 
                   clearance_hole_diameter: float) -> 'FastenerMaterial':
        """Create a bolt fastener material.
        
        C# Ref: Lines 212-223 - CreateBolt
        """
        return FastenerMaterial(
            name=name,
            material_type=FastenerMaterialType.BOLT,
            length=float(length),
            head_diameter=head_diameter,
            clearance_hole_diameter=clearance_hole_diameter
        )


# C# Ref: Lines 238-267 - public class FootingMaterial : IEquatable<FootingMaterial>
@dataclass
class FootingMaterial:
    """Represents footing material properties.
    
    C# Reference: Materials.cs lines 238-267
    C# Definition: public class FootingMaterial : IEquatable<FootingMaterial>
    """
    # C# Ref: Line 240 - public FootingMaterialType FootingType { get; set; }
    footing_type: FootingMaterialType = FootingMaterialType.BLOCK
    
    # C# Ref: Line 242 - public double Width { get; set; }
    width: float = 0.0
    
    # C# Ref: Line 244 - public double Length { get; set; }
    length: float = 0.0
    
    # C# Ref: Line 246 - public double Depth { get; set; }
    depth: float = 0.0
    
    # C# Ref: Lines 263-266 - public bool Equals(FootingMaterial other)
    def __eq__(self, other: object) -> bool:
        """Check equality with another FootingMaterial.
        
        C# Ref: Lines 248-256, 263-266 - Equals methods
        """
        if not isinstance(other, FootingMaterial):
            return False
        return (self.footing_type == other.footing_type and
                self.width == other.width and
                self.length == other.length and
                self.depth == other.depth)
    
    # C# Ref: Lines 258-261 - public override int GetHashCode()
    def __hash__(self) -> int:
        """Get hash code.
        
        C# Ref: Lines 258-261 - GetHashCode
        """
        return hash((self.footing_type, self.width, self.length, self.depth))


# C# Ref: Lines 275-463 - public class FrameMaterial
@dataclass
class FrameMaterial:
    """Represents frame material properties.
    
    C# Reference: Materials.cs lines 275-463
    C# Definition: public class FrameMaterial
    """
    # C# Ref: Line 279 - public string Name { get; set; }
    name: str = ""
    
    # C# Ref: Line 281 - public FrameMaterialType MaterialType { get; set; }
    material_type: FrameMaterialType = FrameMaterialType.UNKNOWN
    
    # C# Ref: Line 283 - public bool Flipped { get; set; }
    flipped: bool = False
    
    # C# Ref: Line 285 - public int Section { get; set; }
    section: int = 0
    
    # C# Ref: Line 287 - public double Width { get; set; }
    width: float = 0.0
    
    # C# Ref: Line 288 - public double Height { get; set; }
    height: float = 0.0
    
    # C# Ref: Line 289 - public double Thickness { get; set; }
    thickness: float = 0.0
    
    # C# Ref: Line 292 - public bool IsB2B { get; set; }
    is_b2b: bool = False
    
    # C# Ref: Line 295 - public double Lip { get; set; }
    lip: float = 0.0
    
    # C# Ref: Lines 299-302 - public double Z_FlangeF { get; set; }
    z_flange_f: float = 0.0
    
    # C# Ref: Lines 304-307 - public double Z_FlangeE { get; set; }
    z_flange_e: float = 0.0
    
    # C# Ref: Lines 309-315 - public double WebHoleCenters { get; set; }
    web_hole_centers: float = 0.0
    
    # C# Ref: Lines 322-325 - public double PAD_RebateWidth { get; set; }
    pad_rebate_width: float = 0.0
    
    # C# Ref: Lines 327-330 - public double PAD_RebateHeight { get; set; }
    pad_rebate_height: float = 0.0
    
    # C# Ref: Lines 332-335 - public double PAD_RebateTail { get; set; }
    pad_rebate_tail: float = 0.0
    
    # C# Ref: Lines 337-340 - public double SRDJ_Tail { get; set; }
    srdj_tail: float = 0.0
    
    def __post_init__(self):
        """Validate frame material properties based on C# rules."""
        # Basic dimension validation
        # Skip thickness validation for PAD materials (they're solid)
        if self.material_type != FrameMaterialType.PAD and self.thickness <= 0:
            raise ValueError(f"Thickness must be greater than 0, got {self.thickness}")
        
        if self.width < 0:
            raise ValueError(f"Width cannot be negative, got {self.width}")
            
        if self.height < 0:
            raise ValueError(f"Height cannot be negative, got {self.height}")
        
        # Material type specific validation
        if self.material_type == FrameMaterialType.C:
            # C-section minimum width validation (40mm based on C# examples)
            if self.width < 40:
                raise ValueError(f"C-section width must be at least 40mm, got {self.width}")
            
            # Thickness range validation
            if self.thickness < 0.75 or self.thickness > 3.0:
                raise ValueError(f"C-section thickness must be between 0.75mm and 3.0mm, got {self.thickness}")
        
        elif self.material_type == FrameMaterialType.SHS:
            # SHS must be square
            if self.width != self.height:
                raise ValueError(f"SHS sections must be square (width == height), got width={self.width}, height={self.height}")
            
            # SHS thickness range
            if self.thickness < 2.5 or self.thickness > 4.0:
                raise ValueError(f"SHS thickness must be between 2.5mm and 4.0mm, got {self.thickness}")
        
        elif self.material_type == FrameMaterialType.TH:
            # TopHat thickness range
            if self.thickness < 0.42 or self.thickness > 1.2:
                raise ValueError(f"TopHat thickness must be between 0.42mm and 1.2mm, got {self.thickness}")
        
        elif self.material_type == FrameMaterialType.Z:
            # Z-section thickness range
            if self.thickness < 1.0 or self.thickness > 3.0:
                raise ValueError(f"Z-section thickness must be between 1.0mm and 3.0mm, got {self.thickness}")
        
        # PAD and SRDJ types don't have specific validation rules
        # UNKNOWN type is allowed for future expansion
    
    # C# Ref: Line 277 - public string Id => Flipped ? $"{Name} (flipped)" : Name;
    @property
    def id(self) -> str:
        """Get the material ID.
        
        C# Ref: Line 277 - Id property
        """
        return f"{self.name} (flipped)" if self.flipped else self.name
    
    # C# Ref: Line 293 - public double Web => Height;
    @property
    def web(self) -> float:
        """Get the web dimension.
        
        C# Ref: Line 293 - Web property
        """
        return self.height
    
    # C# Ref: Line 294 - public double Flange => Width;
    @property
    def flange(self) -> float:
        """Get the flange dimension.
        
        C# Ref: Line 294 - Flange property
        """
        return self.width
    
    # C# Ref: Line 297 - public double FlangeSingle => IsB2B ? Flange / 2 : Flange;
    @property
    def flange_single(self) -> float:
        """Get the single flange dimension.
        
        C# Ref: Line 297 - FlangeSingle property
        """
        return self.flange / 2 if self.is_b2b else self.flange
    
    # C# Ref: Lines 317-320 - public double Depth => Height;
    @property
    def depth(self) -> float:
        """Get the depth dimension.
        
        C# Ref: Lines 317-320 - Depth property
        """
        return self.height
    
    # C# Ref: Lines 342-345 - public override string ToString()
    def __str__(self) -> str:
        """String representation.
        
        C# Ref: Lines 342-345 - ToString
        """
        return self.id
    
    # C# Ref: Lines 347-350 - public override int GetHashCode()
    def __hash__(self) -> int:
        """Get hash code.
        
        C# Ref: Lines 347-350 - GetHashCode
        """
        return hash(self.id)
    
    # C# Ref: Lines 352-363 - private FrameMaterial ToOrientationInternal(bool flipped)
    def _to_orientation_internal(self, flipped: bool) -> 'FrameMaterial':
        """Create a frame material with specified orientation.
        
        C# Ref: Lines 352-363 - ToOrientationInternal
        """
        if self.flipped == flipped:
            return self
        
        # Create a copy with new flipped state
        from copy import copy
        new_material = copy(self)
        new_material.flipped = flipped
        return new_material
    
    # C# Ref: Line 365 - public FrameMaterial ToOrientationNormal()
    def to_orientation_normal(self) -> 'FrameMaterial':
        """Get normal orientation.
        
        C# Ref: Line 365 - ToOrientationNormal
        """
        return self._to_orientation_internal(flipped=False)
    
    # C# Ref: Line 366 - public FrameMaterial ToOrientationFlipped()
    def to_orientation_flipped(self) -> 'FrameMaterial':
        """Get flipped orientation.
        
        C# Ref: Line 366 - ToOrientationFlipped
        """
        return self._to_orientation_internal(flipped=True)
    
    # C# Ref: Line 367 - public FrameMaterial ToOrientationToggle()
    def to_orientation_toggle(self) -> 'FrameMaterial':
        """Toggle orientation.
        
        C# Ref: Line 367 - ToOrientationToggle
        """
        return self._to_orientation_internal(flipped=not self.flipped)
    
    # C# Ref: Lines 369-384 - public static FrameMaterial CreateC
    @staticmethod
    def create_c(name: str, section: int, is_b2b: bool, web: float, 
                 flange: float, lip: float, thickness: float, 
                 web_hole_centers: float) -> 'FrameMaterial':
        """Create a C-section frame material.
        
        C# Ref: Lines 369-384 - CreateC
        """
        return FrameMaterial(
            name=name,
            material_type=FrameMaterialType.C,
            section=section,
            width=flange,
            height=web,
            thickness=thickness,
            is_b2b=is_b2b,
            lip=lip,
            web_hole_centers=web_hole_centers
        )
    
    # C# Ref: Lines 386-402 - public static FrameMaterial CreateZ
    @staticmethod
    def create_z(name: str, section: int, web: float, flange_f: float,
                 flange_e: float, lip: float, thickness: float,
                 web_hole_centers: float) -> 'FrameMaterial':
        """Create a Z-section frame material.
        
        C# Ref: Lines 386-402 - CreateZ
        """
        return FrameMaterial(
            name=name,
            material_type=FrameMaterialType.Z,
            section=section,
            width=round(flange_f + flange_e - thickness, 1),
            height=web,
            thickness=thickness,
            z_flange_f=flange_f,
            z_flange_e=flange_e,
            lip=lip,
            web_hole_centers=web_hole_centers
        )
    
    # C# Ref: Lines 404-416 - public static FrameMaterial CreateTH
    @staticmethod
    def create_th(name: str, section: int, width: float, depth: float,
                  thickness: float) -> 'FrameMaterial':
        """Create a TopHat frame material.
        
        C# Ref: Lines 404-416 - CreateTH
        """
        return FrameMaterial(
            name=name,
            material_type=FrameMaterialType.TH,
            section=section,
            width=width,
            height=depth,
            thickness=thickness
        )
    
    # C# Ref: Lines 418-430 - public static FrameMaterial CreateSHS
    @staticmethod
    def create_shs(name: str, section: int, size: float,
                   thickness: float) -> 'FrameMaterial':
        """Create a Square Hollow Section frame material.
        
        C# Ref: Lines 418-430 - CreateSHS
        """
        return FrameMaterial(
            name=name,
            material_type=FrameMaterialType.SHS,
            section=section,
            width=size,
            height=size,
            thickness=thickness
        )
    
    # C# Ref: Lines 432-447 - public static FrameMaterial CreatePadStile
    @staticmethod
    def create_pad_stile(name: str, section: int, internal_width: float,
                        height: float, thickness: float, rebate_width: float = 0,
                        rebate_height: float = 0, rebate_tail: float = 0) -> 'FrameMaterial':
        """Create a Personal Access Door stile frame material.
        
        C# Ref: Lines 432-447 - CreatePadStile
        """
        return FrameMaterial(
            name=name,
            material_type=FrameMaterialType.PAD,
            section=section,
            width=internal_width + thickness * 2,
            height=height,
            thickness=thickness,
            pad_rebate_width=rebate_width,
            pad_rebate_height=rebate_height,
            pad_rebate_tail=rebate_tail
        )
    
    # C# Ref: Lines 449-462 - public static FrameMaterial CreateSideRollerDoorJamb
    @staticmethod
    def create_side_roller_door_jamb(name: str, section: int, width: float,
                                    height: float, tail: float,
                                    thickness: float) -> 'FrameMaterial':
        """Create a Side Roller Door Jamb frame material.
        
        C# Ref: Lines 449-462 - CreateSideRollerDoorJamb
        """
        return FrameMaterial(
            name=name,
            material_type=FrameMaterialType.SRDJ,
            section=section,
            width=width,
            height=height,
            thickness=thickness,
            srdj_tail=tail
        )
    
    def get_profile_points(self) -> List[Vec2]:
        """
        Get 2D profile points for the frame cross-section.
        
        Returns a list of points defining the frame profile in the XY plane.
        The profile is centered at the origin with proper thickness handling.
        
        C# Reference: Method needed for mesh generation
        
        Returns:
            List of Vec2 points defining the closed profile shape
        """
        if self.material_type == FrameMaterialType.C:
            # C-channel profile - simplified to match C# 8-point structure
            # Create outer and inner contours as per C# implementation
            points = []
            
            if self.lip > 0:
                # With lip - 12 points total as per C# (but simplified here)
                # For now, keep the existing complex profile
                # Bottom flange - outer edge
                points.append(Vec2(0, 0))
                points.append(Vec2(self.width, 0))
                
                # Right lip (bottom) if present
                points.append(Vec2(self.width, self.lip))
                points.append(Vec2(self.width - self.thickness, self.lip))
                
                # Right side going up
                points.append(Vec2(self.width - self.thickness, self.thickness))
                
                # Web - moving up
                points.append(Vec2(self.width - self.thickness, self.height - self.thickness))
                
                # Top flange - inner edge
                points.append(Vec2(self.width, self.height - self.thickness))
                
                # Right lip (top) if present  
                points.append(Vec2(self.width, self.height - self.lip))
                points.append(Vec2(self.width, self.height))
                
                # Top flange - outer edge
                points.append(Vec2(0, self.height))
                
                # Left side going down
                points.append(Vec2(0, self.thickness))
                
                # Close the profile
                points.append(Vec2(self.thickness, self.thickness))
            else:
                # Without lip - 8 points as per C# (4 outer + 4 inner)
                # Outer profile (clockwise)
                points.append(Vec2(0, 0))                   # Bottom-left outer
                points.append(Vec2(self.width, 0))          # Bottom-right outer
                points.append(Vec2(self.width, self.height)) # Top-right outer
                points.append(Vec2(0, self.height))         # Top-left outer
                
                # Inner profile (defines the thickness)
                points.append(Vec2(self.thickness, self.thickness))                          # Bottom-left inner
                points.append(Vec2(self.width - self.thickness, self.thickness))            # Bottom-right inner
                points.append(Vec2(self.width - self.thickness, self.height - self.thickness)) # Top-right inner
                points.append(Vec2(self.thickness, self.height - self.thickness))           # Top-left inner
            
            # If we need to center the profile
            if self.section == 0:  # Default centering
                center_x = self.width / 2
                center_y = self.height / 2
                points = [Vec2(p.x - center_x, p.y - center_y) for p in points]
                
        elif self.material_type == FrameMaterialType.SHS:
            # Square hollow section - 8 points as per C# (4 outer + 4 inner)
            points = []
            
            # Outer profile (clockwise)
            points.append(Vec2(0, 0))                          # Bottom-left
            points.append(Vec2(self.width, 0))                 # Bottom-right
            points.append(Vec2(self.width, self.height))       # Top-right
            points.append(Vec2(0, self.height))                # Top-left
            
            # Inner profile (clockwise for hole)
            points.append(Vec2(self.thickness, self.thickness))                            # Bottom-left inner
            points.append(Vec2(self.width - self.thickness, self.thickness))              # Bottom-right inner
            points.append(Vec2(self.width - self.thickness, self.height - self.thickness)) # Top-right inner
            points.append(Vec2(self.thickness, self.height - self.thickness))             # Top-left inner
            
            # Center the profile if needed
            if self.section == 0:
                center_x = self.width / 2
                center_y = self.height / 2
                points = [Vec2(p.x - center_x, p.y - center_y) for p in points]
            
        elif self.material_type == FrameMaterialType.TH:
            # Top hat profile - like a C but with flanges on both sides
            points = []
            
            # Base/bottom
            points.append(Vec2(0, 0))
            points.append(Vec2(self.width, 0))
            
            # Right side up
            points.append(Vec2(self.width, self.height))
            
            # Top right flange
            points.append(Vec2(self.width - self.lip, self.height))
            
            # Inner right down
            points.append(Vec2(self.width - self.lip, self.thickness))
            
            # Inner bottom
            points.append(Vec2(self.lip, self.thickness))
            
            # Inner left up
            points.append(Vec2(self.lip, self.height))
            
            # Top left flange
            points.append(Vec2(0, self.height))
            
            # Close the shape
            points.append(Vec2(0, 0))
            
            # Center if needed
            if self.section == 0:
                center_x = self.width / 2
                center_y = self.height / 2
                points = [Vec2(p.x - center_x, p.y - center_y) for p in points]
            
        elif self.material_type == FrameMaterialType.Z:
            # Z-section profile - flanges on opposite sides
            points = []
            
            # Bottom flange (left side)
            points.append(Vec2(0, 0))
            points.append(Vec2(self.width, 0))
            points.append(Vec2(self.width, self.thickness))
            
            # Web diagonal
            points.append(Vec2(self.thickness, self.thickness))
            points.append(Vec2(self.thickness, self.height - self.thickness))
            
            # Top flange (right side)  
            points.append(Vec2(0, self.height - self.thickness))
            points.append(Vec2(0, self.height))
            points.append(Vec2(self.width, self.height))
            points.append(Vec2(self.width, self.height - self.thickness))
            
            # Back down the web
            points.append(Vec2(self.width - self.thickness, self.height - self.thickness))
            points.append(Vec2(self.width - self.thickness, self.thickness))
            points.append(Vec2(self.width, self.thickness))
            
            # Close at start
            points.append(Vec2(self.width, 0))
            
            # Center if needed
            if self.section == 0:
                center_x = self.width / 2
                center_y = self.height / 2
                points = [Vec2(p.x - center_x, p.y - center_y) for p in points]
            
        else:
            # Default to simple rectangle for unknown types
            half_width = self.width / 2
            half_height = self.height / 2
            
            points = [
                Vec2(-half_width, -half_height),
                Vec2(half_width, -half_height),
                Vec2(half_width, half_height),
                Vec2(-half_width, half_height),
            ]
            
        return points


# C# Ref: Lines 505-560 - public struct Punching : IEquatable<Punching>
@dataclass
class Punching:
    """Represents a punching specification.
    
    C# Reference: Materials.cs lines 505-560
    C# Definition: public struct Punching : IEquatable<Punching>
    """
    # C# Ref: Line 507 - public double Position { get; set; }
    position: float
    
    # C# Ref: Line 509 - public PunchingWhere Where { get; set; }
    where: PunchingWhere
    
    # C# Ref: Lines 517-520 - public Punching Negate()
    def negate(self) -> 'Punching':
        """Negate the position.
        
        C# Ref: Lines 517-520 - Negate
        """
        return Punching(-self.position, self.where)
    
    # C# Ref: Lines 522-529 - public Punching Abs(double sectionLength)
    def abs(self, section_length: float) -> 'Punching':
        """Convert to absolute position.
        
        C# Ref: Lines 522-529 - Abs
        """
        if self.position < 0:
            return Punching(section_length - self.position, self.where)
        return self
    
    # C# Ref: Lines 531-534 - public Punching Add(double position)
    def add(self, position: float) -> 'Punching':
        """Add to position.
        
        C# Ref: Lines 531-534 - Add
        """
        return Punching(self.position + position, self.where)
    
    # C# Ref: Lines 536-539 - public override string ToString()
    def __str__(self) -> str:
        """String representation.
        
        C# Ref: Lines 536-539 - ToString
        """
        return f"{self.position} @ {self.where.name}"
    
    # C# Ref: Lines 541-544 - public bool Equals(Punching other)
    def __eq__(self, other: object) -> bool:
        """Check equality.
        
        C# Ref: Lines 541-544 - Equals
        """
        if not isinstance(other, Punching):
            return False
        return self.position == other.position and self.where == other.where
    
    # C# Ref: Lines 546-549 - public static Punching CreateWeb(double position)
    @staticmethod
    def create_web(position: float) -> 'Punching':
        """Create web punching.
        
        C# Ref: Lines 546-549 - CreateWeb
        """
        return Punching(position, PunchingWhere.WEB)
    
    # C# Ref: Lines 551-554 - public static Punching CreateFlange(double position)
    @staticmethod
    def create_flange(position: float) -> 'Punching':
        """Create flange punching.
        
        C# Ref: Lines 551-554 - CreateFlange
        """
        return Punching(position, PunchingWhere.FLANGE)
    
    # C# Ref: Lines 556-559 - public static Punching CreateCenter(double position)
    @staticmethod
    def create_center(position: float) -> 'Punching':
        """Create center punching.
        
        C# Ref: Lines 556-559 - CreateCenter
        """
        return Punching(position, PunchingWhere.CENTER)


# C# Ref: Lines 571-576 - public class StrapMaterial
@dataclass
class StrapMaterial:
    """Represents strap material properties.
    
    C# Reference: Materials.cs lines 571-576
    C# Definition: public class StrapMaterial
    """
    # C# Ref: Line 573 - public string Name { get; set; }
    name: str = ""
    
    # C# Ref: Line 574 - public double Width { get; set; }
    width: float = 0.0
    
    # C# Ref: Line 575 - public double Thickness { get; set; }
    thickness: float = 0.0


# C# Ref: Lines 578-587 - public class LiningMaterial
@dataclass
class LiningMaterial:
    """Represents lining material properties.
    
    C# Reference: Materials.cs lines 578-587
    C# Definition: public class LiningMaterial
    """
    # C# Ref: Line 580 - public string Name { get; set; }
    name: str = ""
    
    # C# Ref: Line 581 - public string ProductCode { get; set; }
    product_code: str = ""
    
    # C# Ref: Line 582 - public double Thickness { get; set; }
    thickness: float = 0.0
    
    # C# Ref: Line 583 - public double WidthPerRoll { get; set; }
    width_per_roll: float = 0.0
    
    # C# Ref: Line 584 - public double LengthPerRoll { get; set; }
    length_per_roll: float = 0.0
    
    # C# Ref: Line 585 - public double Overlap { get; set; }
    overlap: float = 0.0
    
    # C# Ref: Line 586 - public double AreaSqmPerRoll { get; set; }
    area_sqm_per_roll: float = 0.0