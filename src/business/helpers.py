"""Business logic helper functions and configurations.

This module contains helper functions and configuration constants
used throughout the business logic layer.
"""

# Fix imports to use absolute paths
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from dataclasses import dataclass
from typing import Optional, List, Tuple, Dict
from src.geometry.primitives import Vec3
from src.geometry.matrix import Mat4
from src.materials.base import ColorMaterial, FrameMaterial
import math


@dataclass
class CarportConfig:
    """Configuration constants for carport construction.
    
    These values represent standard construction parameters and limits.
    """
    # Standard dimensions in mm
    SLAB_THICKNESS: float = 100.0
    MIN_BAY_SIZE: float = 1500.0
    MAX_BAY_SIZE: float = 6000.0
    
    # Standard clearances in mm
    RAFTER_CLEARANCE: float = 50.0
    PURLIN_CLEARANCE: float = 25.0
    
    # Standard spacings in mm
    DEFAULT_PURLIN_SPACING: float = 1200.0
    MAX_PURLIN_SPACING: float = 1500.0
    
    # Minimum dimensions
    MIN_SPAN: float = 2400.0
    MIN_LENGTH: float = 2400.0
    MIN_HEIGHT: float = 2100.0
    
    # Maximum dimensions
    MAX_SPAN: float = 12000.0
    MAX_LENGTH: float = 30000.0
    MAX_HEIGHT: float = 6000.0
    
    # Engineering limits
    MAX_WIND_SPEED: int = 69  # Region C cyclonic
    
    # Standard angles
    MIN_ROOF_PITCH: float = 1.0  # degrees
    MAX_ROOF_PITCH: float = 30.0  # degrees


class CarportHelpers:
    """Helper functions for carport business logic.
    
    Contains utility methods for calculations, validations, and
    common operations used in carport construction.
    """
    
    @staticmethod
    def calculate_bay_positions(length: float, bays: int) -> List[float]:
        """Calculate the Y positions of each bay.
        
        Args:
            length: Total length of the structure
            bays: Number of bays
            
        Returns:
            List of Y positions for each frame
        """
        bay_size = length / bays
        positions = []
        
        for i in range(bays + 1):
            positions.append(i * bay_size)
        
        return positions
    
    @staticmethod
    def calculate_purlin_positions(span: float, rows: int, roof_type: str) -> List[float]:
        """Calculate purlin positions along the roof.
        
        Args:
            span: Roof span
            rows: Number of purlin rows
            roof_type: Type of roof (affects spacing)
            
        Returns:
            List of positions along the roof slope
        """
        if roof_type == "Gable":
            # For gable roofs, purlins on each side
            half_span = span / 2
            spacing = half_span / (rows + 1)
            positions = []
            
            for i in range(1, rows + 1):
                positions.append(i * spacing)
            
            return positions
        else:
            # For flat/skillion roofs
            spacing = span / (rows + 1)
            positions = []
            
            for i in range(1, rows + 1):
                positions.append(i * spacing)
            
            return positions
    
    @staticmethod
    def calculate_roof_slope(span: float, pitch: float) -> float:
        """Calculate vertical rise of roof based on span and pitch.
        
        Args:
            span: Horizontal span
            pitch: Pitch angle in degrees
            
        Returns:
            Vertical rise in mm
        """
        return span * math.tan(math.radians(pitch))
    
    @staticmethod
    def get_color_material(color_name: str) -> Optional[ColorMaterial]:
        """Get color material by name.
        
        Args:
            color_name: Name of the color
            
        Returns:
            ColorMaterial if found, None otherwise
        """
        # This would typically look up from a color database
        # For now, return a default color
        if not color_name:
            return None
        
        # Map common color names to RGB values
        color_map = {
            "CLASSIC_CREAM": (233, 224, 199),
            "WOODLAND_GREY": (138, 135, 133),
            "MONUMENT": (79, 78, 78),
            "SURFMIST": (227, 227, 227),
            "EVENING_HAZE": (163, 174, 183),
            "BASALT": (83, 86, 90),
            "COVE": (189, 191, 183),
            "MANOR_RED": (140, 52, 45),
            "PALE_EUCALYPT": (107, 119, 103),
            "JASPER": (105, 89, 80),
            "DEEP_OCEAN": (39, 67, 91),
            "COTTAGE_GREEN": (51, 66, 56),
            "SHALE_GREY": (169, 165, 161),
            "DUNE": (171, 158, 135),
            "WINDSPRAY": (185, 185, 172),
        }
        
        color_key = color_name.upper().replace(" ", "_")
        if color_key in color_map:
            r, g, b = color_map[color_key]
            return ColorMaterial(name=color_name, r=r, g=g, b=b)
        
        # Default grey if color not found
        return ColorMaterial(name=color_name, r=128, g=128, b=128)
    
    @staticmethod
    def validate_frame_spacing(positions: List[float], min_spacing: float = 1500.0) -> bool:
        """Validate that frame positions have adequate spacing.
        
        Args:
            positions: List of frame positions
            min_spacing: Minimum allowed spacing
            
        Returns:
            True if spacing is valid
        """
        if len(positions) < 2:
            return True
        
        for i in range(1, len(positions)):
            spacing = positions[i] - positions[i-1]
            if spacing < min_spacing:
                return False
        
        return True
    
    @staticmethod
    def calculate_bracket_positions(
        column_material: FrameMaterial,
        rafter_material: FrameMaterial,
        connection_type: str
    ) -> Tuple[Vec3, Vec3]:
        """Calculate bracket attachment positions.
        
        Args:
            column_material: Column frame material
            rafter_material: Rafter frame material
            connection_type: Type of connection (haunch, apex, etc.)
            
        Returns:
            Tuple of (column_attachment_pos, rafter_attachment_pos)
        """
        if connection_type == "haunch":
            # Haunch bracket positions
            col_attach = Vec3(0, column_material.web - 50, 0)
            raf_attach = Vec3(rafter_material.flange + 50, 0, 0)
        elif connection_type == "apex":
            # Apex bracket positions
            col_attach = Vec3(0, 0, 0)  # Not used for apex
            raf_attach = Vec3(0, rafter_material.web / 2, 0)
        else:
            # Default positions
            col_attach = Vec3(0, 0, 0)
            raf_attach = Vec3(0, 0, 0)
        
        return col_attach, raf_attach
    
    @staticmethod
    def calculate_punching_points(
        material: FrameMaterial,
        length: float,
        connection_count: int
    ) -> List[int]:
        """Calculate punching hole positions along a member.
        
        Args:
            material: Frame material
            length: Length of member
            connection_count: Number of connections needed
            
        Returns:
            List of punching positions from member start
        """
        if connection_count <= 0:
            return []
        
        if connection_count == 1:
            # Single connection at center
            return [int(length / 2)]
        
        # Multiple connections evenly spaced
        spacing = length / (connection_count + 1)
        positions = []
        
        for i in range(1, connection_count + 1):
            positions.append(int(i * spacing))
        
        return positions
    
    @staticmethod
    def get_downpipe_positions(
        building_length: float,
        downpipe_spacing: float = 12000.0
    ) -> List[float]:
        """Calculate downpipe positions along building.
        
        Args:
            building_length: Total building length
            downpipe_spacing: Maximum spacing between downpipes
            
        Returns:
            List of Y positions for downpipes
        """
        # Minimum 2 downpipes for proper drainage
        if building_length <= 6000:
            # Two downpipes at ends
            return [0.0, building_length]
        
        # Calculate number of downpipes needed
        count = max(2, int(math.ceil(building_length / downpipe_spacing)) + 1)
        
        # Space evenly
        positions = []
        spacing = building_length / (count - 1)
        
        for i in range(count):
            positions.append(i * spacing)
        
        return positions
    
    @staticmethod
    def validate_engineering_override(
        building_input: 'BuildingInput',
        eng_data: Optional['EngData']
    ) -> bool:
        """Validate that engineering overrides are safe.
        
        Args:
            building_input: Building input with potential overrides
            eng_data: Engineering validation data
            
        Returns:
            True if overrides are valid or no engineering data
        """
        if not eng_data or not building_input.frame_override:
            return True
        
        # TODO: Implement validation logic comparing override materials
        # with engineering requirements
        
        return True
    
    @staticmethod
    def interpolate_roof_height(
        x_position: float,
        span: float,
        base_height: float,
        pitch: float,
        roof_type: str
    ) -> float:
        """Calculate roof height at a given X position.
        
        Args:
            x_position: X coordinate along span
            span: Total roof span
            base_height: Eave height
            pitch: Roof pitch in degrees
            roof_type: Type of roof
            
        Returns:
            Z height at the given position
        """
        if roof_type == "Flat":
            # Flat roof with slight slope
            return base_height + x_position * math.tan(math.radians(pitch))
        elif roof_type == "Gable":
            # Gable roof peaks at center
            center = span / 2
            if x_position <= center:
                return base_height + x_position * math.tan(math.radians(pitch))
            else:
                return base_height + (span - x_position) * math.tan(math.radians(pitch))
        else:
            # Skillion roof
            return base_height + x_position * math.tan(math.radians(pitch))