"""Engineering service integration module.

This module handles external engineering validation and data structures.
"""

from dataclasses import dataclass
from typing import Optional, Dict, Any
import httpx
import asyncio
from .building_input import BuildingInput


@dataclass
class EngData:
    """Engineering calculation results data structure.
    
    Contains validated structural member sizes and specifications
    from the external engineering service.
    
    C# Ref: Used throughout StructureBuilderBase.cs and CarportBuilder.cs
    """
    # Structural member specifications
    ENG_RAFTER: str  # Rafter material specification (e.g., "C15024")
    ENG_PURLINSIZE: str  # Purlin material specification
    ENG_PURLINROW: int  # Number of purlin rows
    ENG_COLUMN: str  # Column material specification
    
    # Footing specifications
    ENG_FOOTINGTYPE: str  # "bored" or "block"
    ENG_FOOTINGDIA: str  # Diameter/width in mm
    ENG_FOOTINGDEPTH: str  # Depth in mm
    
    # Optional fields
    ENG_APEXBRACE: Optional[str] = None  # Apex brace material (optional)
    
    def __post_init__(self):
        """Validate engineering data after initialization."""
        # Ensure numeric strings can be parsed
        try:
            float(self.ENG_FOOTINGDIA)
            float(self.ENG_FOOTINGDEPTH)
        except ValueError as e:
            raise ValueError(f"Invalid footing dimensions: {e}")
        
        # Validate footing type
        if self.ENG_FOOTINGTYPE.lower() not in ["bored", "block"]:
            raise ValueError(f"Invalid footing type: {self.ENG_FOOTINGTYPE}")


# EngineeringService now available with httpx dependency
class EngineeringService:
    """Service for external engineering validation.
    
    This service communicates with an external engineering API to validate
    building designs and obtain proper structural member sizing.
    """
    
    def __init__(self, base_url: str, api_key: str, timeout: float = 30.0):
        """Initialize the engineering service.
        
        Args:
            base_url: Base URL of the engineering API
            api_key: API key for authentication
            timeout: Request timeout in seconds
        """
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.timeout = timeout
        self._client: Optional[httpx.AsyncClient] = None
    
    async def __aenter__(self):
        """Async context manager entry."""
        self._client = httpx.AsyncClient(timeout=self.timeout)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self._client:
            await self._client.aclose()
    
    async def validate_design(self, building_input: BuildingInput) -> Optional[EngData]:
        """Validate building design with engineering service.
        
        Args:
            building_input: Building input parameters to validate
            
        Returns:
            EngData if validation successful, None if failed or not required
        """
        if not building_input.validate_engineering:
            return None
        
        if not self._client:
            self._client = httpx.AsyncClient(timeout=self.timeout)
        
        try:
            # Prepare request data
            request_data = self._prepare_request_data(building_input)
            
            # Make API request
            response = await self._client.post(
                f"{self.base_url}/api/engineering/validate",
                json=request_data,
                headers={
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                }
            )
            
            response.raise_for_status()
            
            # Parse response
            data = response.json()
            return self._parse_response(data)
            
        except httpx.HTTPStatusError as e:
            print(f"Engineering validation HTTP error: {e.response.status_code}")
            return None
        except Exception as e:
            print(f"Engineering validation failed: {e}")
            return None
    
    def _prepare_request_data(self, building_input: BuildingInput) -> Dict[str, Any]:
        """Prepare request data for engineering API.
        
        Args:
            building_input: Building input parameters
            
        Returns:
            Dictionary of request data
        """
        return {
            "buildingType": building_input.building_type.value,
            "roofType": building_input.roof_type.value,
            "span": building_input.span,
            "length": building_input.length,
            "height": building_input.height,
            "bays": building_input.bays,
            "windSpeed": building_input.wind_speed,
            "pitch": building_input.pitch,
            "soil": building_input.soil,
            "overhang": building_input.overhang
        }
    
    def _parse_response(self, data: Dict[str, Any]) -> Optional[EngData]:
        """Parse engineering API response.
        
        Args:
            data: Response data from API
            
        Returns:
            EngData if parsing successful, None otherwise
        """
        try:
            return EngData(
                ENG_RAFTER=data.get("rafter", ""),
                ENG_PURLINSIZE=data.get("purlinSize", ""),
                ENG_PURLINROW=int(data.get("purlinRows", 3)),
                ENG_COLUMN=data.get("column", ""),
                ENG_APEXBRACE=data.get("apexBrace"),
                ENG_FOOTINGTYPE=data.get("footingType", "block"),
                ENG_FOOTINGDIA=str(data.get("footingDiameter", "300")),
                ENG_FOOTINGDEPTH=str(data.get("footingDepth", "300"))
            )
        except (KeyError, ValueError) as e:
            print(f"Failed to parse engineering response: {e}")
            return None
    
    def validate_design_sync(self, building_input: BuildingInput) -> Optional[EngData]:
        """Synchronous wrapper for validate_design.
        
        Args:
            building_input: Building input parameters to validate
            
        Returns:
            EngData if validation successful, None if failed
        """
        loop = asyncio.get_event_loop()
        return loop.run_until_complete(self.validate_design(building_input))


class MockEngineeringService(EngineeringService):
    """Mock engineering service for testing.
    
    Returns predetermined engineering data without external API calls.
    """
    
    def __init__(self):
        """Initialize mock service without API credentials."""
        super().__init__("http://mock", "mock-key")
    
    async def validate_design(self, building_input: BuildingInput) -> Optional[EngData]:
        """Return mock engineering data based on building parameters."""
        if not building_input.validate_engineering:
            return None
        
        # Simple logic for mock data based on span
        if building_input.span <= 3000:
            return EngData(
                ENG_RAFTER="C15015",
                ENG_PURLINSIZE="TH064100",
                ENG_PURLINROW=3,
                ENG_COLUMN="SHS07507525",
                ENG_APEXBRACE="C10010" if building_input.roof_type.value == "Gable" else None,
                ENG_FOOTINGTYPE="block",
                ENG_FOOTINGDIA="300",
                ENG_FOOTINGDEPTH="300"
            )
        elif building_input.span <= 6000:
            return EngData(
                ENG_RAFTER="C15024",
                ENG_PURLINSIZE="C15015",
                ENG_PURLINROW=5,
                ENG_COLUMN="SHS10010030",
                ENG_APEXBRACE="C15015" if building_input.roof_type.value == "Gable" else None,
                ENG_FOOTINGTYPE="bored",
                ENG_FOOTINGDIA="450",
                ENG_FOOTINGDEPTH="600"
            )
        else:
            return EngData(
                ENG_RAFTER="C20030",
                ENG_PURLINSIZE="C15024",
                ENG_PURLINROW=7,
                ENG_COLUMN="SHS15015040",
                ENG_APEXBRACE="C15024" if building_input.roof_type.value == "Gable" else None,
                ENG_FOOTINGTYPE="bored",
                ENG_FOOTINGDIA="600",
                ENG_FOOTINGDEPTH="900"
            )