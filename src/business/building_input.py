"""Building Input module - C# BuildingInput.cs conversion.

This module corresponds to BuildingInput.cs (lines 1-105).
Implements the main input data structure for building configurations.
"""

from dataclasses import dataclass, field
from enum import Enum
from typing import Optional


class BuildingType(Enum):
    """Building type enumeration."""
    CARPORT = "Carport"
    SHED = "Shed"


class CarportRoofType(Enum):
    """Carport roof type enumeration.
    
    C# Ref: BuildingInput.cs lines 97-103
    """
    FLAT = "Flat"
    GABLE = "Gable"  
    AWNING = "Awning"
    ATTACHED_AWNING = "AttachedAwning"


@dataclass
class BuildingInput:
    """Main input data structure for building configurations.
    
    This class handles validation and constraints for building inputs,
    including special handling for overhang values based on span.
    
    C# Ref: BuildingInput.cs lines 3-95
    """
    
    # Core building properties
    building_type: BuildingType = BuildingType.CARPORT
    name: str = ""
    roof_type: CarportRoofType = CarportRoofType.FLAT
    validate_engineering: bool = False
    
    # Dimensions
    bays: int = 1
    span: float = 0.0
    length: float = 0.0
    height: float = 0.0
    wind_speed: int = 0
    pitch: float = 0.0
    
    # Slab configuration
    slab: bool = False
    slab_thickness: float = 0.0
    soil: str = ""
    
    # Cladding
    cladding_type: str = ""
    
    # Internal override flag
    internal_override: bool = False
    
    # Color configuration
    roof_color: str = ""
    post_color: str = ""
    rafter_color: str = ""
    flashing_color: str = ""
    downpipe_color: str = ""
    gutter_color: str = ""
    
    # Frame overrides
    frame_override: bool = False
    max_purlin_space: int = -1
    post_override: str = ""
    rafter_override: str = ""
    eave_purlin_override: str = ""
    purlin_override: str = ""
    extra_purlins: int = 0
    end_bays: int = 1
    
    # Gutter and downpipe
    gutter_selection: str = "FGMP"
    downpipe_size: str = "large"
    footing_type_override: str = ""
    
    # Private overhang value
    _overhang: float = field(default=0.0, init=False)
    
    def __post_init__(self):
        """Validate inputs after initialization."""
        # Validate dimensions
        if self.span <= 0:
            raise ValueError(f"Span must be positive, got {self.span}")
        if self.length <= 0:
            raise ValueError(f"Length must be positive, got {self.length}")
        if self.height <= 0:
            raise ValueError(f"Height must be positive, got {self.height}")
        if self.bays <= 0:
            raise ValueError(f"Bays must be positive, got {self.bays}")
        if self.pitch < 0:
            raise ValueError(f"Pitch cannot be negative, got {self.pitch}")
        
        # Apply overhang validation
        if hasattr(self, 'overhang') and self.overhang > 0:
            temp_overhang = self.overhang
            self._overhang = 0  # Reset to trigger setter
            self.overhang = temp_overhang
    
    @property
    def overhang(self) -> float:
        """Get overhang value.
        
        C# Ref: BuildingInput.cs lines 20-24
        """
        return self._overhang
    
    @overhang.setter
    def overhang(self, value: float) -> None:
        """Set overhang value with validation based on roof type and span.
        
        Overhang is only applicable to flat roofs and has maximum values
        based on the span:
        - Span <= 3600: max 600mm
        - Span <= 5000: max 900mm  
        - Span > 5000: max 1200mm
        
        C# Ref: BuildingInput.cs lines 26-68
        """
        # Only flat roofs can have overhang
        if self.roof_type != CarportRoofType.FLAT:
            self._overhang = 0.0
            return
        
        # Apply span-based limits
        if self.span <= 3600:
            self._overhang = min(value, 600.0)
        elif self.span <= 5000:
            self._overhang = min(value, 900.0)
        else:
            self._overhang = min(value, 1200.0)
    
    def validate(self) -> bool:
        """Validate the building input parameters.
        
        Returns:
            bool: True if all inputs are valid, False otherwise.
        """
        # Basic dimension validation
        if self.span <= 0 or self.length <= 0 or self.height <= 0:
            return False
        
        # Bay validation
        if self.bays < 1:
            return False
        
        # Pitch validation for gable roofs
        if self.roof_type == CarportRoofType.GABLE and self.pitch <= 0:
            return False
        
        # Wind speed validation
        if self.wind_speed < 0:
            return False
        
        return True
    
    def to_dict(self) -> dict:
        """Convert to dictionary for serialization."""
        return {
            'building_type': self.building_type.value,
            'name': self.name,
            'roof_type': self.roof_type.value,
            'validate_engineering': self.validate_engineering,
            'bays': self.bays,
            'span': self.span,
            'length': self.length,
            'height': self.height,
            'wind_speed': self.wind_speed,
            'pitch': self.pitch,
            'overhang': self.overhang,
            'slab': self.slab,
            'slab_thickness': self.slab_thickness,
            'soil': self.soil,
            'cladding_type': self.cladding_type,
            'internal_override': self.internal_override,
            'roof_color': self.roof_color,
            'post_color': self.post_color,
            'rafter_color': self.rafter_color,
            'flashing_color': self.flashing_color,
            'downpipe_color': self.downpipe_color,
            'gutter_color': self.gutter_color,
            'frame_override': self.frame_override,
            'max_purlin_space': self.max_purlin_space,
            'post_override': self.post_override,
            'rafter_override': self.rafter_override,
            'eave_purlin_override': self.eave_purlin_override,
            'purlin_override': self.purlin_override,
            'extra_purlins': self.extra_purlins,
            'end_bays': self.end_bays,
            'gutter_selection': self.gutter_selection,
            'downpipe_size': self.downpipe_size,
            'footing_type_override': self.footing_type_override
        }


@dataclass
class ShedInput:
    """Input data structure for shed configurations.
    
    This is a placeholder for shed-specific inputs which differ from carports.
    Full implementation would include shed-specific parameters.
    """
    # Main shed parameters
    length: float = 0.0
    main: Optional['ShedMainInput'] = None
    
    # Additional shed-specific parameters would go here
    # This is a simplified version for the carport-focused implementation


@dataclass  
class ShedMainInput:
    """Main shed input parameters."""
    span: float = 0.0
    roof_pitch: float = 0.0
    overhang_left: float = 0.0
    overhang_right: float = 0.0
    endbay_num: int = 1