"""Business Logic Layer for BIM Backend.

This module implements the business logic for building construction including:
- Building input validation and constraints
- Structure builder base class with construction pipeline
- Carport builder factory implementation
- Engineering service integration
"""

from .building_input import (
    BuildingInput,
    ShedInput,
    BuildingType,
    CarportRoofType
)

from .structure_builder import (
    StructureBuilderBase,
    CarportBuilder,
    CarportProduct
)

from .engineering import (
    EngData
    # EngineeringService  # Commented out due to httpx dependency
)

from .helpers import (
    CarportHelpers,
    CarportConfig
)

__all__ = [
    # Input models
    'BuildingInput',
    'ShedInput',
    'BuildingType',
    'CarportRoofType',
    
    # Builders
    'StructureBuilderBase',
    'CarportBuilder',
    'CarportProduct',
    
    # Engineering
    'EngData',
    # 'EngineeringService',  # Commented out due to httpx dependency
    
    # Helpers
    'CarportHelpers',
    'CarportConfig'
]