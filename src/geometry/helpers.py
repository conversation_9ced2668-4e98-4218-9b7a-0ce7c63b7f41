"""Geometry helper functions and constants.

This module corresponds to the static Geo class and helper functions from C# Geo.cs.
C# Reference: Geo.cs lines 10-581
"""

import math
from typing import List, Tuple, Optional, TypeVar, Iterable, Union
from .primitives import Vec2, Vec3
from .lines import Line1, Line2, Line3
from .boxes import Box2


T = TypeVar('T')


class Geo:
    """Static class containing geometry helper functions and constants.
    
    C# Reference: Geo.cs lines 10-581
    C# Definition: public static class Geo
    """
    
    # Angle constants
    # C# Ref: Lines 13-18
    DEG0 = 0  # C#: Deg0
    DEG45 = math.pi / 4  # C#: Deg45
    DEG90 = math.pi / 2  # C#: Deg90
    DEG180 = math.pi  # C#: Deg180
    DEG270 = math.pi + math.pi / 2  # C#: Deg270
    DEG360 = math.pi * 2  # C#: Deg360
    
    # Conversion constants
    # C# Ref: Lines 20-21
    TO_DEG = 180 / math.pi  # C#: ToDeg
    TO_RAD = math.pi / 180  # C#: ToRad
    
    @staticmethod
    def normalize_angle(angle: float) -> float:
        """Normalize an angle to the range [0, 2π).
        
        C# Ref: Lines 23-36 - public static double NormalizeAngle(double angle)
        """
        while angle < 0:
            angle += Geo.DEG360
        while angle >= Geo.DEG360:
            angle -= Geo.DEG360
        return angle
    
    @staticmethod
    def wrap_index(collection: List[T], index: int) -> int:
        """Wrap an index to be within the bounds of a collection."""
        count = len(collection)
        while index < 0:
            index += count
        while index >= count:
            index -= count
        return index
    
    @staticmethod
    def trig_soh(angle: float, o: float) -> float:
        """SOH: sin = opposite/hypotenuse => hypotenuse = opposite/sin."""
        return o / math.sin(angle)
    
    @staticmethod
    def trig_sho(angle: float, h: float) -> float:
        """SHO: sin = opposite/hypotenuse => opposite = hypotenuse * sin."""
        return h * math.sin(angle)
    
    @staticmethod
    def trig_cah(angle: float, a: float) -> float:
        """CAH: cos = adjacent/hypotenuse => hypotenuse = adjacent/cos."""
        return a / math.cos(angle)
    
    @staticmethod
    def trig_cha(angle: float, h: float) -> float:
        """CHA: cos = adjacent/hypotenuse => adjacent = hypotenuse * cos."""
        return h * math.cos(angle)
    
    @staticmethod
    def trig_toa(angle: float, o: float) -> float:
        """TOA: tan = opposite/adjacent => adjacent = opposite/tan."""
        return o / math.tan(angle)
    
    @staticmethod
    def trig_tao(angle: float, a: float) -> float:
        """TAO: tan = opposite/adjacent => opposite = adjacent * tan."""
        return a * math.tan(angle)
    
    @staticmethod
    def mirror_angle(angle: float) -> float:
        """Mirror an angle by adding 180 degrees."""
        return Geo.normalize_angle(angle + Geo.DEG180)
    
    @staticmethod
    def mirror_vec_x(base_x: float, v: Vec2) -> Vec2:
        """Mirror a 2D vector across a vertical line at base_x."""
        return Vec2(base_x + (base_x - v.x), v.y)
    
    @staticmethod
    def get_extents(verts: Iterable[Vec2], extend: float = 0.0) -> Tuple[float, float, float, float]:
        """Get the bounding extents of a collection of 2D points."""
        left = float('inf')
        right = -float('inf')
        bottom = float('inf')
        top = -float('inf')
        
        for vert in verts:
            left = min(left, vert.x)
            right = max(right, vert.x)
            bottom = min(bottom, vert.y)
            top = max(top, vert.y)
        
        left -= extend
        right += extend
        bottom -= extend
        top += extend
        
        return (left, right, bottom, top)
    
    @staticmethod
    def get_offsets(bays: Iterable[float], start_pos: float = 0.0, dir: int = 1) -> List[float]:
        """Calculate cumulative offsets from bay sizes."""
        offsets = [start_pos]
        curr = start_pos
        
        for bay in bays:
            curr += dir * bay
            offsets.append(curr)
        
        return offsets
    
    @staticmethod
    def get_bay_sizes(length: float, num_bays: int, precision: float = 10) -> List[float]:
        """
        Calculate bay sizes that sum to the total length.
        
        Rounds to the nearest precision value (default 10mm).
        For example, 10000 with 3 bays returns [3340, 3330, 3330].
        
        Args:
            length: Total length to divide
            num_bays: Number of bays
            precision: Rounding precision (1, 10, 100, or 1000)
            
        Returns:
            List of bay sizes
            
        Raises:
            ValueError: If parameters are invalid
        """
        if length <= 0:
            raise ValueError(f"length must be a non-zero positive value, got {length}")
        
        if num_bays <= 0:
            raise ValueError(f"num_bays must be 1 or more, got {num_bays}")
        
        if precision not in [1, 10, 100, 1000]:
            raise ValueError(f"precision must be one of [1, 10, 100, 1000], got {precision}")
        
        if num_bays == 1:
            return [length]
        
        sizes = []
        bay_width = math.floor(length / num_bays / precision) * precision
        
        # First bay gets the remainder to ensure exact total
        first_bay_width = bay_width + (length - bay_width * num_bays)
        
        sizes.append(first_bay_width)
        for _ in range(1, num_bays):
            sizes.append(bay_width)
        
        return sizes
    
    @staticmethod
    def v2(x: float, y: float) -> Vec2:
        """Create a Vec2."""
        return Vec2(x, y)
    
    @staticmethod
    def v2xy(a: Vec3) -> Vec2:
        """Extract XY components from Vec3."""
        return Vec2(a.x, a.y)
    
    @staticmethod
    def v2xz(a: Vec3) -> Vec2:
        """Extract XZ components from Vec3."""
        return Vec2(a.x, a.z)
    
    @staticmethod
    def v2yz(a: Vec3) -> Vec2:
        """Extract YZ components from Vec3."""
        return Vec2(a.y, a.z)
    
    @staticmethod
    def v3(x: float, y: float, z: float) -> Vec3:
        """Create a Vec3."""
        return Vec3(x, y, z)
    
    @staticmethod
    def vx(x: float) -> Vec3:
        """Create a Vec3 with only X component."""
        return Vec3(x, 0, 0)
    
    @staticmethod
    def vy(y: float) -> Vec3:
        """Create a Vec3 with only Y component."""
        return Vec3(0, y, 0)
    
    @staticmethod
    def vz(z: float) -> Vec3:
        """Create a Vec3 with only Z component."""
        return Vec3(0, 0, z)
    
    @staticmethod
    def ln2xy(line: Line3) -> Line2:
        """Project 3D line to XY plane."""
        return Line2(Geo.v2xy(line.start), Geo.v2xy(line.end))
    
    @staticmethod
    def ln2xz(line: Line3) -> Line2:
        """Project 3D line to XZ plane."""
        return Line2(Geo.v2xz(line.start), Geo.v2xz(line.end))
    
    @staticmethod
    def ln2yz(line: Line3) -> Line2:
        """Project 3D line to YZ plane."""
        return Line2(Geo.v2yz(line.start), Geo.v2yz(line.end))
    
    @staticmethod
    def ln1(start: float, end: float) -> Line1:
        """Create a Line1."""
        return Line1(start, end)
    
    @staticmethod
    def ln2(start_x: float, start_y: float, end_x: float, end_y: float) -> Line2:
        """Create a Line2 from coordinates."""
        return Line2(Vec2(start_x, start_y), Vec2(end_x, end_y))
    
    @staticmethod
    def ln2_v(start: Vec2, end: Vec2) -> Line2:
        """Create a Line2 from vectors."""
        return Line2(start, end)
    
    @staticmethod
    def ln3(x1: float, y1: float, z1: float, x2: float, y2: float, z2: float) -> Line3:
        """Create a Line3 from coordinates."""
        return Line3(Vec3(x1, y1, z1), Vec3(x2, y2, z2))
    
    @staticmethod
    def ln3_v(start: Vec3, end: Vec3) -> Line3:
        """Create a Line3 from vectors."""
        return Line3(start, end)
    
    @staticmethod
    def polar(ang: float, dist: float) -> Vec2:
        """Create a 2D vector from polar coordinates."""
        return Vec2(dist * math.cos(ang), dist * math.sin(ang))
    
    @staticmethod
    def rotate(ang: float, v: Vec2) -> Vec2:
        """Rotate a 2D vector by an angle."""
        return Geo.polar(ang + v.angle(), v.length())
    
    @staticmethod
    def mid(a: Union[float, Vec2, Vec3, Line1, Line2, Line3], 
            b: Optional[Union[float, Vec2, Vec3]] = None) -> Union[float, Vec2, Vec3]:
        """Calculate midpoint of two values or the midpoint of a line."""
        if b is not None:
            # Two values provided
            if isinstance(a, float) and isinstance(b, float):
                return a + (b - a) / 2
            elif isinstance(a, Vec2) and isinstance(b, Vec2):
                return a + (b - a) / 2
            elif isinstance(a, Vec3) and isinstance(b, Vec3):
                return a + (b - a) / 2
        else:
            # Single line provided
            if isinstance(a, Line1):
                return a.start + a.length() / 2
            elif isinstance(a, Line2):
                return a.start + a.length() / 2
            elif isinstance(a, Line3):
                return a.start + a.length() / 2
        
        raise TypeError(f"Invalid types for mid: {type(a)}, {type(b)}")
    
    @staticmethod
    def interpolate(line: Union[Line1, Line2, Line3], scale: float) -> Union[float, Vec2, Vec3]:
        """Interpolate along a line by a scale factor (0 to 1)."""
        if isinstance(line, Line1):
            return line.start + line.length() * scale
        elif isinstance(line, Line2):
            return line.start + line.length() * scale
        elif isinstance(line, Line3):
            return line.start + line.length() * scale
        else:
            raise TypeError(f"Invalid type for interpolate: {type(line)}")
    
    @staticmethod
    def round(value: Union[Vec2, Vec3, Line2, Line3], decimals: int = 0) -> Union[Vec2, Vec3, Line2, Line3]:
        """Round vector or line components to specified decimal places."""
        if isinstance(value, Vec2):
            return Vec2(round(value.x, decimals), round(value.y, decimals))
        elif isinstance(value, Vec3):
            return Vec3(round(value.x, decimals), round(value.y, decimals), round(value.z, decimals))
        elif isinstance(value, Line2):
            return Line2(Geo.round(value.start, decimals), Geo.round(value.end, decimals))
        elif isinstance(value, Line3):
            return Line3(Geo.round(value.start, decimals), Geo.round(value.end, decimals))
        else:
            raise TypeError(f"Invalid type for round: {type(value)}")
    
    @staticmethod
    def ln_swap(line: Union[Line1, Line2, Line3]) -> Union[Line1, Line2, Line3]:
        """Swap start and end points of a line."""
        if isinstance(line, Line1):
            return Line1(line.end, line.start)
        elif isinstance(line, Line2):
            return Line2(line.end, line.start)
        elif isinstance(line, Line3):
            return Line3(line.end, line.start)
        else:
            raise TypeError(f"Invalid type for ln_swap: {type(line)}")
    
    @staticmethod
    def ln_extend_v(start: Union[Vec2, Vec3], end: Union[Vec2, Vec3], 
                    start_extend: float, end_extend: float) -> Union[Line2, Line3]:
        """Extend a line by specified amounts at each end."""
        if isinstance(start, Vec2) and isinstance(end, Vec2):
            angle = (end - start).angle()
            start = start - Geo.polar(angle, start_extend)
            end = end + Geo.polar(angle, end_extend)
            return Line2(start, end)
        elif isinstance(start, Vec3) and isinstance(end, Vec3):
            dir = Vec3.normal(end - start)
            start = start - dir * start_extend
            end = end + dir * end_extend
            return Line3(start, end)
        else:
            raise TypeError(f"Invalid types for ln_extend_v: {type(start)}, {type(end)}")
    
    @staticmethod
    def ln_extend(line: Union[Line2, Line3], start_extend: float, end_extend: float) -> Union[Line2, Line3]:
        """Extend a line by specified amounts at each end."""
        return Geo.ln_extend_v(line.start, line.end, start_extend, end_extend)
    
    @staticmethod
    def ln_offset(start: Vec2, end: Vec2, distance: float) -> Line2:
        """Offset a 2D line perpendicular to its direction."""
        perp = (end - start).angle() + Geo.DEG90
        offset = Geo.polar(perp, distance)
        return Line2(start + offset, end + offset)
    
    @staticmethod
    def ln_offset_line(line: Line2, distance: float) -> Line2:
        """Offset a 2D line perpendicular to its direction."""
        return Geo.ln_offset(line.start, line.end, distance)
    
    @staticmethod
    def ln_up(start: Vec2, end: Vec2, distance: float) -> Line2:
        """Offset a line upward (positive Y direction)."""
        offset_line = Geo.ln_offset(start, end, distance)
        if offset_line.start.y >= start.y:
            return offset_line
        return Geo.ln_offset(start, end, -distance)
    
    @staticmethod
    def ln_up_line(line: Line2, distance: float) -> Line2:
        """Offset a line upward (positive Y direction)."""
        return Geo.ln_up(line.start, line.end, distance)
    
    @staticmethod
    def ln_down(start: Vec2, end: Vec2, distance: float) -> Line2:
        """Offset a line downward (negative Y direction)."""
        offset_line = Geo.ln_offset(start, end, distance)
        if offset_line.start.y <= start.y:
            return offset_line
        return Geo.ln_offset(start, end, -distance)
    
    @staticmethod
    def ln_down_line(line: Line2, distance: float) -> Line2:
        """Offset a line downward (negative Y direction)."""
        return Geo.ln_down(line.start, line.end, distance)
    
    @staticmethod
    def ln_offset_polyline(polyline: List[Vec2], distance: float) -> List[Vec2]:
        """Offset a polyline (list of connected points) by a perpendicular distance."""
        if len(polyline) < 2:
            return polyline.copy()
        
        offsets = []
        
        # First point
        offset_start = Geo.ln_offset(polyline[0], polyline[1], distance).start
        offsets.append(offset_start)
        
        # Middle points
        for i in range(1, len(polyline) - 1):
            offset1 = Geo.ln_offset(polyline[i - 1], polyline[i], distance)
            offset2 = Geo.ln_offset(polyline[i], polyline[i + 1], distance)
            inters = Line2.inters_must(offset1, offset2, infinite=True)
            offsets.append(inters)
        
        # Last point
        offset_end = Geo.ln_offset(polyline[-2], polyline[-1], distance).end
        offsets.append(offset_end)
        
        return offsets
    
    @staticmethod
    def poly_offset(poly: List[Vec2], distance: float) -> List[Vec2]:
        """Offset a closed polygon by a perpendicular distance."""
        if len(poly) < 3:
            return poly.copy()
        
        def get_vert(i: int) -> Vec2:
            """Get vertex with wrapping."""
            if i < 0:
                i += len(poly)
            if i >= len(poly):
                i -= len(poly)
            return poly[i]
        
        new_poly = []
        
        for i in range(len(poly)):
            a = get_vert(i - 1)
            b = get_vert(i)
            c = get_vert(i + 1)
            
            l1 = Geo.ln_offset(a, b, distance)
            l2 = Geo.ln_offset(b, c, distance)
            inters = Line2.inters_must(l1, l2, infinite=True)
            new_poly.append(inters)
        
        return new_poly
    
    @staticmethod
    def ln_low_high(line: Line2) -> Line2:
        """
        Return line with lower point as start and higher point as end.
        If Y coordinates are equal, returns left-most point as start.
        """
        sx = line.start.x
        sy = line.start.y
        ex = line.end.x
        ey = line.end.y
        
        if sy < ey or (sy == ey and sx < ex):
            return line
        
        return Line2(line.end, line.start)
    
    @staticmethod
    def ln_left_right(line: Line2) -> Line2:
        """
        Return line with left-most point as start and right-most point as end.
        If X coordinates are equal, returns lowest point as start.
        """
        sx = line.start.x
        sy = line.start.y
        ex = line.end.x
        ey = line.end.y
        
        if sx < ex or (sx == ex and sy < ey):
            return line
        
        return Line2(line.end, line.start)
    
    @staticmethod
    def ln_range(middle: float, size: float) -> Line1:
        """Create a Line1 from center and size."""
        return Line1(middle - size / 2, middle + size / 2)
    
    @staticmethod
    def ln_extend_to_line(line: Line2, test: Line2) -> Line2:
        """Extend line to intersection with test line."""
        hit = Line2.inters(line, test, infinite=True)
        if hit is None:
            return line
        
        # Extend the closer endpoint to the intersection
        if (hit - line.start).length_squared() <= (hit - line.end).length_squared():
            return Line2(hit, line.end)
        else:
            return Line2(line.start, hit)
    
    @staticmethod
    def box_line_inters(box: Box2, line: Line2) -> Optional[Line2]:
        """Calculate intersection of a line with a box."""
        contains_start = box.contains(line.start)
        contains_end = box.contains(line.end)
        
        if contains_start and contains_end:
            return line
        
        # Check intersections with box edges
        intersl = Line2.inters(line, Geo.ln2(box.left(), box.bottom(), box.left(), box.top()))
        intersr = Line2.inters(line, Geo.ln2(box.right(), box.bottom(), box.right(), box.top()))
        intersb = Line2.inters(line, Geo.ln2(box.left(), box.bottom(), box.right(), box.bottom()))
        interst = Line2.inters(line, Geo.ln2(box.left(), box.top(), box.right(), box.top()))
        
        inters = [i for i in [intersl, intersr, intersb, interst] if i is not None]
        n = len(inters)
        
        if n == 0:
            return None
        elif n == 1:
            if contains_start:
                return Geo.ln2_v(line.start, inters[0])
            else:
                return Geo.ln2_v(inters[0], line.end)
        else:
            # Two intersections
            inters1 = inters[0]
            inters2 = inters[1]
            
            if (line.start - inters1).length() <= (line.end - inters1).length():
                return Geo.ln2_v(inters1, inters2)
            else:
                return Geo.ln2_v(inters2, inters1)
    
    @staticmethod
    def get_near_far(base_pt: Vec3, pt1: Vec3, pt2: Vec3) -> Tuple[Vec3, Vec3]:
        """Return the nearer and farther points from a base point."""
        dist1 = (pt1 - base_pt).length()
        dist2 = (pt2 - base_pt).length()
        
        if dist2 < dist1:
            return (pt2, pt1)
        else:
            return (pt1, pt2)