"""Triangle index for mesh operations.

This module corresponds to TriIndex structure from C# Geo.cs.
C# Reference: Geo.cs lines 1560-1604
"""

from __future__ import annotations
from dataclasses import dataclass


@dataclass
class TriIndex:
    """Represents triangle indices for mesh operations.
    
    C# Reference: Geo.cs lines 1560-1604
    C# Definition: public struct TriIndex : IEquatable<TriIndex>
    """
    
    # C# Ref: Lines 1562-1564 - public int A/B/C { get; set; }
    a: int
    b: int
    c: int
    
    def flipped(self) -> TriIndex:
        """Return a triangle with flipped winding order."""
        return TriIndex(self.a, self.c, self.b)
    
    def contains(self, vertex: int) -> bool:
        """Check if the triangle contains a vertex index."""
        return vertex == self.a or vertex == self.b or vertex == self.c
    
    def to_array(self) -> list[int]:
        """Convert to array representation."""
        return [self.a, self.b, self.c]
    
    def __str__(self) -> str:
        """String representation."""
        return f"({self.a}, {self.b}, {self.c})"
    
    def __repr__(self) -> str:
        """Detailed string representation."""
        return f"TriIndex(a={self.a}, b={self.b}, c={self.c})"