"""Vector primitives for 2D and 3D geometry operations.

This module corresponds to Vec2 and Vec3 structures from the C# Geo.cs file:
- Vec2: Lines 583-765 in Geo.cs
- Vec3: Lines 767-916 in Geo.cs
"""

from __future__ import annotations
import math
from dataclasses import dataclass
from typing import Optional, Tuple


@dataclass
class Vec2:
    """Represents a 2D vector with x and y components.
    
    C# Reference: Geo.cs lines 583-765
    C# Definition: public struct Vec2 : IEquatable<Vec2>
    """
    
    # C# Ref: Lines 586-587 - public double X { get; set; }
    x: float = 0.0
    y: float = 0.0
    
    def length(self) -> float:
        """Calculate the length (magnitude) of the vector."""
        return math.sqrt(self.x ** 2 + self.y ** 2)
    
    def length_squared(self) -> float:
        """Calculate the squared length of the vector (avoids sqrt calculation)."""
        return self.x ** 2 + self.y ** 2
    
    def angle(self) -> float:
        """Calculate the angle of the vector in radians."""
        return math.atan2(self.y, self.x)
    
    def normalized(self) -> Vec2:
        """Return a normalized version of this vector."""
        return Vec2.normal(self)
    
    @staticmethod
    def origin() -> Vec2:
        """Return the origin point (0, 0)."""
        return Vec2(0.0, 0.0)
    
    @staticmethod
    def unit_x() -> Vec2:
        """Return the unit vector in X direction."""
        return Vec2(1.0, 0.0)
    
    @staticmethod
    def unit_y() -> Vec2:
        """Return the unit vector in Y direction."""
        return Vec2(0.0, 1.0)
    
    @staticmethod
    def min_value() -> Vec2:
        """Return a vector with minimum float values."""
        return Vec2(-float('inf'), -float('inf'))
    
    @staticmethod
    def max_value() -> Vec2:
        """Return a vector with maximum float values."""
        return Vec2(float('inf'), float('inf'))
    
    @staticmethod
    def dot(a: Vec2, b: Vec2) -> float:
        """Calculate the dot product of two vectors."""
        return a.x * b.x + a.y * b.y
    
    @staticmethod
    def normal(a: Vec2) -> Vec2:
        """Return the normalized (unit) vector."""
        length = a.length()
        if length == 0:
            return Vec2(0.0, 0.0)
        return Vec2(a.x / length, a.y / length)
    
    @staticmethod
    def cross(a: Vec2, b: Vec2) -> float:
        """Calculate the 2D cross product (returns scalar)."""
        return a.x * b.y - a.y * b.x
    
    @staticmethod
    def distance(a: Vec2, b: Vec2) -> float:
        """Calculate the distance between two points."""
        dx = b.x - a.x
        dy = b.y - a.y
        return math.sqrt(dx * dx + dy * dy)
    
    @staticmethod
    def inters(a1: Vec2, a2: Vec2, b1: Vec2, b2: Vec2, infinite: bool = False) -> Optional[Vec2]:
        """
        Calculate intersection point of two line segments.
        
        Args:
            a1, a2: Start and end points of first line segment
            b1, b2: Start and end points of second line segment
            infinite: If True, treat lines as infinite
            
        Returns:
            Intersection point if exists, None otherwise
        """
        x21 = a2.x - a1.x
        y21 = a2.y - a1.y
        x31 = b1.x - a1.x
        y31 = b1.y - a1.y
        x43 = b2.x - b1.x
        y43 = b2.y - b1.y
        
        denom = x43 * y21 - x21 * y43
        
        if denom == 0:
            return None
        
        s = (x43 * y31 - x31 * y43) / denom
        t = (x21 * y31 - x31 * y21) / denom
        
        if not infinite:
            if s < 0 or s > 1 or t < 0 or t > 1:
                return None
        
        return Vec2(a1.x + (a2.x - a1.x) * s, a1.y + (a2.y - a1.y) * s)
    
    @staticmethod
    def inters_must(a1: Vec2, a2: Vec2, b1: Vec2, b2: Vec2, infinite: bool = False) -> Vec2:
        """
        Calculate intersection point of two line segments (must exist).
        
        Raises:
            ValueError: If no intersection found
        """
        result = Vec2.inters(a1, a2, b1, b2, infinite)
        if result is None:
            raise ValueError(f"No intersection found for: a1={a1}, a2={a2}, b1={b1}, b2={b2}, infinite={infinite}")
        return result
    
    @staticmethod
    def inters_list(a1: Vec2, a2: Vec2, bx: list[Vec2], infinite: bool = False) -> Optional[Vec2]:
        """Find first intersection with a list of connected line segments."""
        for i in range(1, len(bx)):
            inters = Vec2.inters(a1, a2, bx[i-1], bx[i], infinite)
            if inters is not None:
                return inters
        return None
    
    @staticmethod
    def inters_list_must(a1: Vec2, a2: Vec2, bx: list[Vec2], infinite: bool = False) -> Vec2:
        """
        Find first intersection with a list of connected line segments (must exist).
        
        Raises:
            ValueError: If no intersection found
        """
        result = Vec2.inters_list(a1, a2, bx, infinite)
        if result is None:
            raise ValueError("No intersection found.")
        return result
    
    
    @staticmethod
    def min(a: Vec2, b: Vec2) -> Vec2:
        """Return component-wise minimum of two vectors."""
        return Vec2(min(a.x, b.x), min(a.y, b.y))
    
    @staticmethod
    def max(a: Vec2, b: Vec2) -> Vec2:
        """Return component-wise maximum of two vectors."""
        return Vec2(max(a.x, b.x), max(a.y, b.y))
    
    def __pos__(self) -> Vec2:
        """Unary positive operator."""
        return self
    
    def __add__(self, other: Vec2) -> Vec2:
        """Vector addition."""
        return Vec2(self.x + other.x, self.y + other.y)
    
    def __neg__(self) -> Vec2:
        """Unary negative operator."""
        return Vec2(-self.x, -self.y)
    
    def __sub__(self, other: Vec2) -> Vec2:
        """Vector subtraction."""
        return Vec2(self.x - other.x, self.y - other.y)
    
    def __mul__(self, scalar: float) -> Vec2:
        """Scalar multiplication (vector * scalar)."""
        return Vec2(self.x * scalar, self.y * scalar)
    
    def __rmul__(self, scalar: float) -> Vec2:
        """Scalar multiplication (scalar * vector)."""
        return Vec2(scalar * self.x, scalar * self.y)
    
    def __truediv__(self, scalar: float) -> Vec2:
        """Scalar division."""
        return Vec2(self.x / scalar, self.y / scalar)
    
    def add(self, other: Vec2) -> Vec2:
        """Add another vector to this one (instance method).
        
        C# Reference: Method needed for compatibility
        
        Args:
            other: The vector to add
            
        Returns:
            A new vector representing the sum
        """
        return Vec2(self.x + other.x, self.y + other.y)
    
    def dot(self, other: Vec2) -> float:
        """Calculate the dot product with another vector (instance method).
        
        C# Reference: Instance method for compatibility
        
        Args:
            other: The vector to calculate dot product with
            
        Returns:
            The dot product as a scalar
        """
        return self.x * other.x + self.y * other.y
    
    def __str__(self) -> str:
        """String representation."""
        return f"({self.x},{self.y})"
    
    def __repr__(self) -> str:
        """Detailed string representation."""
        return f"Vec2(x={self.x}, y={self.y})"


@dataclass
class Vec3:
    """Represents a 3D vector with x, y, and z components.
    
    C# Reference: Geo.cs lines 767-916
    C# Definition: public struct Vec3 : IEquatable<Vec3>
    """
    
    # C# Ref: Lines 770-772 - public double X/Y/Z { get; set; }
    x: float = 0.0
    y: float = 0.0
    z: float = 0.0
    
    def distance_squared(self, other: Vec3) -> float:
        """Calculate squared distance to another vector."""
        diff = self - other
        return diff.length_squared()
    
    def to_array(self) -> list[float]:
        """Convert to array representation."""
        return [self.x, self.y, self.z]
    
    @staticmethod
    def midpoint(a: Vec3, b: Vec3) -> Vec3:
        """Calculate midpoint between two vectors."""
        return Vec3((a.x + b.x) / 2, (a.y + b.y) / 2, (a.z + b.z) / 2)
    
    def length(self) -> float:
        """Calculate the length (magnitude) of the vector."""
        return math.sqrt(Vec3.dot(self, self))
    
    def length_squared(self) -> float:
        """Calculate the squared length of the vector."""
        return Vec3.dot(self, self)
    
    def normalized(self) -> Vec3:
        """Return a normalized version of this vector."""
        return Vec3.normal(self)
    
    @staticmethod
    def origin() -> Vec3:
        """Return the origin point (0, 0, 0)."""
        return Vec3(0.0, 0.0, 0.0)
    
    @staticmethod
    def zero() -> Vec3:
        """Return the zero vector (0, 0, 0)."""
        return Vec3(0.0, 0.0, 0.0)
    
    @staticmethod
    def unit_x() -> Vec3:
        """Return the unit vector in X direction."""
        return Vec3(1.0, 0.0, 0.0)
    
    @staticmethod
    def unit_y() -> Vec3:
        """Return the unit vector in Y direction."""
        return Vec3(0.0, 1.0, 0.0)
    
    @staticmethod
    def unit_z() -> Vec3:
        """Return the unit vector in Z direction."""
        return Vec3(0.0, 0.0, 1.0)
    
    @staticmethod
    def min_value() -> Vec3:
        """Return a vector with minimum float values."""
        return Vec3(-float('inf'), -float('inf'), -float('inf'))
    
    @staticmethod
    def max_value() -> Vec3:
        """Return a vector with maximum float values."""
        return Vec3(float('inf'), float('inf'), float('inf'))
    
    @staticmethod
    def normal(a: Vec3) -> Vec3:
        """Return the normalized (unit) vector."""
        length = a.length()
        if length == 0:
            return Vec3(0.0, 0.0, 0.0)
        return a / length
    
    @staticmethod
    def dot(a: Vec3, b: Vec3) -> float:
        """Calculate the dot product of two vectors."""
        return a.x * b.x + a.y * b.y + a.z * b.z
    
    @staticmethod
    def cross(a: Vec3, b: Vec3) -> Vec3:
        """Calculate the cross product of two vectors."""
        return Vec3(
            a.y * b.z - a.z * b.y,
            a.z * b.x - a.x * b.z,
            a.x * b.y - a.y * b.x
        )
    
    @staticmethod
    def distance(a: Vec3, b: Vec3) -> float:
        """Calculate the distance between two points."""
        dx = b.x - a.x
        dy = b.y - a.y
        dz = b.z - a.z
        return math.sqrt(dx * dx + dy * dy + dz * dz)
    
    @staticmethod
    def min(a: Vec3, b: Vec3) -> Vec3:
        """Return component-wise minimum of two vectors."""
        return Vec3(min(a.x, b.x), min(a.y, b.y), min(a.z, b.z))
    
    @staticmethod
    def max(a: Vec3, b: Vec3) -> Vec3:
        """Return component-wise maximum of two vectors."""
        return Vec3(max(a.x, b.x), max(a.y, b.y), max(a.z, b.z))
    
    @staticmethod
    def distance(a: Vec3, b: Vec3) -> float:
        """Calculate the distance between two vectors."""
        return math.sqrt((b.x - a.x)**2 + (b.y - a.y)**2 + (b.z - a.z)**2)
    
    def __pos__(self) -> Vec3:
        """Unary positive operator."""
        return self
    
    def __add__(self, other: Vec3) -> Vec3:
        """Vector addition."""
        return Vec3(self.x + other.x, self.y + other.y, self.z + other.z)
    
    def __neg__(self) -> Vec3:
        """Unary negative operator."""
        return Vec3(-self.x, -self.y, -self.z)
    
    def __sub__(self, other: Vec3) -> Vec3:
        """Vector subtraction."""
        return Vec3(self.x - other.x, self.y - other.y, self.z - other.z)
    
    def __mul__(self, scalar: float) -> Vec3:
        """Scalar multiplication (vector * scalar)."""
        return Vec3(self.x * scalar, self.y * scalar, self.z * scalar)
    
    def __rmul__(self, scalar: float) -> Vec3:
        """Scalar multiplication (scalar * vector)."""
        return Vec3(scalar * self.x, scalar * self.y, scalar * self.z)
    
    def __truediv__(self, scalar: float) -> Vec3:
        """Scalar division."""
        return Vec3(self.x / scalar, self.y / scalar, self.z / scalar)
    
    def dist(self, other: Vec3) -> float:
        """Calculate distance to another point (alias for distance).
        
        C# Reference: Method needed for compatibility
        
        Args:
            other: The other vector/point
            
        Returns:
            The Euclidean distance between the two points
        """
        return Vec3.distance(self, other)
    
    def dot(self, other: Vec3) -> float:
        """Calculate the dot product with another vector (instance method).
        
        C# Reference: Instance method for compatibility
        
        Args:
            other: The vector to calculate dot product with
            
        Returns:
            The dot product as a scalar
        """
        return self.x * other.x + self.y * other.y + self.z * other.z
    
    def cross(self, other: Vec3) -> Vec3:
        """Calculate the cross product with another vector (instance method).
        
        C# Reference: Instance method for compatibility
        
        Args:
            other: The vector to calculate cross product with
            
        Returns:
            A new vector representing the cross product
        """
        return Vec3(
            self.y * other.z - self.z * other.y,
            self.z * other.x - self.x * other.z,
            self.x * other.y - self.y * other.x
        )
    
    def __str__(self) -> str:
        """String representation."""
        return f"({self.x},{self.y},{self.z})"
    
    def __repr__(self) -> str:
        """Detailed string representation."""
        return f"Vec3(x={self.x}, y={self.y}, z={self.z})"