"""Plane primitive for 3D geometry.

This module corresponds to Plane3 structure from C# Geo.cs.
C# Reference: Geo.cs lines 1359-1487
"""

from __future__ import annotations
import math
from dataclasses import dataclass
from typing import Op<PERSON>, Tuple
from .primitives import Vec3
from .lines import Line3


@dataclass
class Plane3:
    """
    Represents a plane in 3D space.
    
    The plane equation is: ax + by + cz + d = 0
    where (a, b, c) is the normal vector and d is the distance constant.
    
    C# Reference: Geo.cs lines 1359-1487
    C# Definition: public struct Plane3 : IEquatable<Plane3>
    """
    
    # C# Ref: Lines 1364-1365 - public Vec3 Normal { get; set; } / public double D { get; set; }
    normal: Vec3
    d: float
    
    def __init__(self, nx: float, ny: float, nz: float, d: float):
        """Initialize from normal components and distance."""
        self.normal = Vec3(nx, ny, nz)
        self.d = d
    
    @staticmethod
    def from_normal_and_distance(normal: Vec3, d: float) -> Plane3:
        """Create a plane from normal vector and distance."""
        return Plane3(normal.x, normal.y, normal.z, d)
    
    @staticmethod
    def from_three_points(p1: Vec3, p2: Vec3, p3: Vec3) -> Plane3:
        """Create a plane from three points."""
        v1 = p2 - p1
        v2 = p3 - p1
        
        n = Vec3.cross(v1, v2)
        normal = Vec3.normal(n)
        
        d = -Vec3.dot(normal, p1)
        
        return Plane3(normal.x, normal.y, normal.z, d)
    
    @staticmethod
    def from_normal_and_point(normal: Vec3, p: Vec3) -> Plane3:
        """Create a plane from normal vector and a point on the plane."""
        d = -Vec3.dot(normal, p)
        return Plane3(normal.x, normal.y, normal.z, d)
    
    @staticmethod
    def dot_v(f: Plane3, v: Vec3) -> float:
        """Dot product of plane normal with a vector."""
        return Vec3.dot(f.normal, v)
    
    @staticmethod
    def dot_p(f: Plane3, p: Vec3) -> float:
        """Evaluate the plane equation at a point."""
        return Plane3.dot_v(f, p) + f.d
    
    @staticmethod
    def inters_line(l: Line3, f: Plane3, infinite: bool = False) -> Optional[Vec3]:
        """
        Calculate intersection between a line (segment) and a plane.
        
        Args:
            l: Line to intersect
            f: Plane to intersect with
            infinite: If True, treat line as infinite
            
        Returns:
            Intersection point if exists, None otherwise
        """
        return Plane3.inters_point_vector(l.start, l.length(), f, infinite)
    
    @staticmethod
    def inters_point_vector(p: Vec3, v: Vec3, f: Plane3, infinite: bool = False) -> Optional[Vec3]:
        """
        Calculate intersection between a ray and a plane.
        
        Args:
            p: Ray origin
            v: Ray direction
            f: Plane to intersect with
            infinite: If True, treat ray as infinite in both directions
            
        Returns:
            Intersection point if exists, None otherwise
        """
        fv = Plane3.dot_v(f, v)
        
        if abs(fv) > 1e-10:  # Not parallel
            t = -Plane3.dot_p(f, p) / fv
            if infinite or (0 <= t <= 1):
                q = p + v * t
                return q
        
        return None
    
    @staticmethod
    def inters_planes(f1: Plane3, f2: Plane3) -> Optional[Tuple[Vec3, Vec3]]:
        """
        Calculate intersection line between two planes.
        
        Returns:
            Tuple of (point on line, direction vector) if planes intersect,
            None if planes are parallel
        """
        n1 = f1.normal
        n2 = f2.normal
        
        v = Vec3.cross(n1, n2)
        det = Vec3.dot(v, v)
        
        if abs(det) > 1e-10:  # Not parallel
            p = (Vec3.cross(v, n2) * f1.d + Vec3.cross(n1, v) * f2.d) / det
            return (p, v)
        
        return None
    
    def distance_to_point(self, point: Vec3) -> float:
        """Calculate signed distance from point to plane."""
        return Plane3.dot_p(self, point) / self.normal.length()
    
    def project_point(self, point: Vec3) -> Vec3:
        """Project a point onto the plane."""
        distance = self.distance_to_point(point)
        return point - self.normal * distance
    
    def __str__(self) -> str:
        """String representation."""
        return f"Plane3(normal={self.normal}, d={self.d})"
    
    def __repr__(self) -> str:
        """Detailed string representation."""
        return f"Plane3({self.normal.x}, {self.normal.y}, {self.normal.z}, {self.d})"