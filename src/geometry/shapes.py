"""Shape classes for geometry operations.

This module provides shape classes like Triangle for geometric calculations.
"""

from __future__ import annotations
import math
from dataclasses import dataclass
from typing import Tuple
from .primitives import Vec2, Vec3


@dataclass
class Triangle:
    """Represents a 2D triangle defined by three vertices."""
    
    a: Vec2
    b: Vec2
    c: Vec2
    
    def area(self) -> float:
        """Calculate the area of the triangle using the cross product formula."""
        # Area = 0.5 * |AB × AC|
        ab = Vec2(self.b.x - self.a.x, self.b.y - self.a.y)
        ac = Vec2(self.c.x - self.a.x, self.c.y - self.a.y)
        cross = Vec2.cross(ab, ac)
        return abs(cross) / 2.0
    
    def perimeter(self) -> float:
        """Calculate the perimeter of the triangle."""
        ab = Vec2.distance(self.a, self.b)
        bc = Vec2.distance(self.b, self.c)
        ca = Vec2.distance(self.c, self.a)
        return ab + bc + ca
    
    def centroid(self) -> Vec2:
        """Calculate the centroid (center of mass) of the triangle."""
        return Vec2(
            (self.a.x + self.b.x + self.c.x) / 3.0,
            (self.a.y + self.b.y + self.c.y) / 3.0
        )
    
    def contains(self, point: Vec2) -> bool:
        """Check if a point is inside the triangle using barycentric coordinates."""
        # Calculate vectors
        v0 = Vec2(self.c.x - self.a.x, self.c.y - self.a.y)
        v1 = Vec2(self.b.x - self.a.x, self.b.y - self.a.y)
        v2 = Vec2(point.x - self.a.x, point.y - self.a.y)
        
        # Calculate dot products
        dot00 = Vec2.dot(v0, v0)
        dot01 = Vec2.dot(v0, v1)
        dot02 = Vec2.dot(v0, v2)
        dot11 = Vec2.dot(v1, v1)
        dot12 = Vec2.dot(v1, v2)
        
        # Calculate barycentric coordinates
        inv_denom = 1 / (dot00 * dot11 - dot01 * dot01)
        u = (dot11 * dot02 - dot01 * dot12) * inv_denom
        v = (dot00 * dot12 - dot01 * dot02) * inv_denom
        
        # Check if point is in triangle
        return (u >= 0) and (v >= 0) and (u + v <= 1)
    
    def circumcircle(self) -> Tuple[Vec2, float]:
        """Calculate the circumcircle (circle passing through all vertices).
        
        Returns:
            Tuple of (center, radius)
        """
        # Convert to vectors relative to vertex a
        b_rel = Vec2(self.b.x - self.a.x, self.b.y - self.a.y)
        c_rel = Vec2(self.c.x - self.a.x, self.c.y - self.a.y)
        
        # Calculate D = 2(b.x * c.y - b.y * c.x)
        d = 2 * (b_rel.x * c_rel.y - b_rel.y * c_rel.x)
        
        if abs(d) < 1e-10:
            # Points are collinear
            return Vec2(0, 0), float('inf')
        
        # Calculate circumcenter relative to vertex a
        b_len_sq = b_rel.x * b_rel.x + b_rel.y * b_rel.y
        c_len_sq = c_rel.x * c_rel.x + c_rel.y * c_rel.y
        
        cx = (c_rel.y * b_len_sq - b_rel.y * c_len_sq) / d
        cy = (b_rel.x * c_len_sq - c_rel.x * b_len_sq) / d
        
        # Convert back to absolute coordinates
        center = Vec2(self.a.x + cx, self.a.y + cy)
        
        # Calculate radius
        radius = Vec2.distance(center, self.a)
        
        return center, radius
    
    def is_clockwise(self) -> bool:
        """Check if the vertices are ordered clockwise."""
        # Use the sign of the cross product
        ab = Vec2(self.b.x - self.a.x, self.b.y - self.a.y)
        ac = Vec2(self.c.x - self.a.x, self.c.y - self.a.y)
        cross = Vec2.cross(ab, ac)
        return cross < 0
    
    def is_valid(self) -> bool:
        """Check if the triangle is valid (non-degenerate)."""
        return self.area() > 1e-10


@dataclass
class Triangle3D:
    """Represents a 3D triangle defined by three vertices."""
    
    a: Vec3
    b: Vec3
    c: Vec3
    
    def area(self) -> float:
        """Calculate the area of the triangle using the cross product formula."""
        # Area = 0.5 * |AB × AC|
        ab = self.b - self.a
        ac = self.c - self.a
        cross = Vec3.cross(ab, ac)
        return cross.length() / 2.0
    
    def normal(self) -> Vec3:
        """Calculate the normal vector of the triangle."""
        ab = self.b - self.a
        ac = self.c - self.a
        normal = Vec3.cross(ab, ac)
        return normal.normalized()
    
    def centroid(self) -> Vec3:
        """Calculate the centroid of the triangle."""
        return Vec3(
            (self.a.x + self.b.x + self.c.x) / 3.0,
            (self.a.y + self.b.y + self.c.y) / 3.0,
            (self.a.z + self.b.z + self.c.z) / 3.0
        )