"""4x4 transformation matrix implementation for 3D transformations.

This module corresponds to the Mat4 structure from the C# Mat4.cs file.
C# Reference: Mat4.cs lines 1-305
"""

from __future__ import annotations
import math
from dataclasses import dataclass
from typing import Tuple
from .primitives import Vec3
from .basis import Basis3


@dataclass
class Mat4:
    """
    Represents a 4x4 transformation matrix.
    
    Matrix multiplication follows the convention: v' = Mv, where M is a matrix 
    and v is a column vector.
    
    C# Reference: Mat4.cs lines 13-303
    C# Definition: public struct Mat4 : IEquatable<Mat4>
    C# Remark: v' = Mv, where M is a matrix and v is a column vector.
    """
    
    # C# Ref: Lines 15-30 - public double M11-M44 { get; set; }
    m11: float = 1.0
    m12: float = 0.0
    m13: float = 0.0
    m14: float = 0.0
    m21: float = 0.0
    m22: float = 1.0
    m23: float = 0.0
    m24: float = 0.0
    m31: float = 0.0
    m32: float = 0.0
    m33: float = 1.0
    m34: float = 0.0
    m41: float = 0.0
    m42: float = 0.0
    m43: float = 0.0
    m44: float = 1.0
    
    def get_translation(self) -> Vec3:
        """Extract the translation component from the matrix.
        
        C# Ref: Lines 56-59 - public Vec3 GetTranslation()
        """
        return Vec3(self.m14, self.m24, self.m34)
    
    def get_basis(self) -> Basis3:
        """Extract the basis vectors (rotation/scale component) from the matrix.
        
        C# Ref: Lines 61-67 - public Basis3 GetBasis()
        """
        return Basis3(
            Vec3(self.m11, self.m21, self.m31),
            Vec3(self.m12, self.m22, self.m32),
            Vec3(self.m13, self.m23, self.m33)
        )
    
    @staticmethod
    def identity() -> Mat4:
        """Return the identity matrix."""
        return Mat4(
            1.0, 0.0, 0.0, 0.0,
            0.0, 1.0, 0.0, 0.0,
            0.0, 0.0, 1.0, 0.0,
            0.0, 0.0, 0.0, 1.0
        )
    
    @staticmethod
    def rotation_x(angle: float) -> Mat4:
        """Create a rotation matrix around the X axis."""
        return Mat4.create_rotation_x(angle)
    
    @staticmethod
    def rotation_y(angle: float) -> Mat4:
        """Create a rotation matrix around the Y axis."""
        return Mat4.create_rotation_y(angle)
    
    @staticmethod
    def rotation_z(angle: float) -> Mat4:
        """Create a rotation matrix around the Z axis."""
        return Mat4.create_rotation_z(angle)
    
    @staticmethod
    def translation(x, y=None, z=None) -> Mat4:
        """Create a translation matrix.
        
        Can be called as:
        - translation(x, y, z) with three floats
        - translation(vec3) with a Vec3 object
        """
        if y is None and z is None and hasattr(x, 'x') and hasattr(x, 'y') and hasattr(x, 'z'):
            # x is a Vec3 object
            return Mat4.create_translation_vec(x)
        else:
            # x, y, z are floats
            return Mat4.create_translation(x, y, z)
    
    @staticmethod
    def scale(x, y=None, z=None) -> Mat4:
        """Create a scale matrix.
        
        Can be called as:
        - scale(x, y, z) with three floats
        - scale(vec3) with a Vec3 object
        - scale(s) with a single float for uniform scale
        """
        if y is None and z is None:
            if hasattr(x, 'x') and hasattr(x, 'y') and hasattr(x, 'z'):
                # x is a Vec3 object
                return Mat4.create_scale_vec(x)
            else:
                # x is a single float for uniform scale
                return Mat4.create_scale(x)
        else:
            # x, y, z are floats
            return Mat4.create_scale_xyz(x, y, z)
    
    @staticmethod
    def create_rotation_x(angle: float) -> Mat4:
        """Create a rotation matrix around the X axis."""
        c = math.cos(angle)
        s = math.sin(angle)
        
        return Mat4(
            1.0, 0.0,  0.0, 0.0,
            0.0,   c,   -s, 0.0,
            0.0,   s,    c, 0.0,
            0.0, 0.0,  0.0, 1.0
        )
    
    @staticmethod
    def create_rotation_y(angle: float) -> Mat4:
        """Create a rotation matrix around the Y axis."""
        c = math.cos(angle)
        s = math.sin(angle)
        
        return Mat4(
             c, 0.0,   s, 0.0,
            0.0, 1.0, 0.0, 0.0,
            -s, 0.0,   c, 0.0,
            0.0, 0.0, 0.0, 1.0
        )
    
    @staticmethod
    def create_rotation_z(angle: float) -> Mat4:
        """Create a rotation matrix around the Z axis."""
        c = math.cos(angle)
        s = math.sin(angle)
        
        return Mat4(
              c,  -s, 0.0, 0.0,
              s,   c, 0.0, 0.0,
            0.0, 0.0, 1.0, 0.0,
            0.0, 0.0, 0.0, 1.0
        )
    
    @staticmethod
    def create_translation(x: float, y: float, z: float) -> Mat4:
        """Create a translation matrix from components."""
        return Mat4.create_translation_vec(Vec3(x, y, z))
    
    @staticmethod
    def create_translation_vec(v: Vec3) -> Mat4:
        """Create a translation matrix from a vector."""
        return Mat4(
            1.0, 0.0, 0.0, v.x,
            0.0, 1.0, 0.0, v.y,
            0.0, 0.0, 1.0, v.z,
            0.0, 0.0, 0.0, 1.0
        )
    
    @staticmethod
    def create_scale(s: float) -> Mat4:
        """Create a uniform scale matrix."""
        return Mat4.create_scale_xyz(s, s, s)
    
    @staticmethod
    def create_scale_xyz(sx: float, sy: float, sz: float) -> Mat4:
        """Create a non-uniform scale matrix from components."""
        return Mat4.create_scale_vec(Vec3(sx, sy, sz))
    
    @staticmethod
    def create_scale_vec(s: Vec3) -> Mat4:
        """Create a scale matrix from a vector."""
        return Mat4(
            s.x, 0.0, 0.0, 0.0,
            0.0, s.y, 0.0, 0.0,
            0.0, 0.0, s.z, 0.0,
            0.0, 0.0, 0.0, 1.0
        )
    
    @staticmethod
    def create_basis(x: Vec3, y: Vec3, z: Vec3) -> Mat4:
        """Create a matrix from basis vectors."""
        return Mat4.create_basis_from_basis3(Basis3(x, y, z))
    
    @staticmethod
    def create_basis_from_basis3(basis: Basis3) -> Mat4:
        """Create a matrix from a Basis3 object."""
        return Mat4(
            basis.x.x, basis.y.x, basis.z.x, 0.0,
            basis.x.y, basis.y.y, basis.z.y, 0.0,
            basis.x.z, basis.y.z, basis.z.z, 0.0,
                  0.0,       0.0,       0.0, 1.0
        )
    
    @staticmethod
    def create_transform(basis: Basis3, pos: Vec3) -> Mat4:
        """Create a transformation matrix from basis and position."""
        return Mat4.create_transform_components(basis.x, basis.y, basis.z, pos)
    
    @staticmethod
    def create_transform_components(basis_x: Vec3, basis_y: Vec3, basis_z: Vec3, pos: Vec3) -> Mat4:
        """Create a transformation matrix from basis vectors and position."""
        return Mat4(
            basis_x.x, basis_y.x, basis_z.x, pos.x,
            basis_x.y, basis_y.y, basis_z.y, pos.y,
            basis_x.z, basis_y.z, basis_z.z, pos.z,
                  0.0,       0.0,       0.0,   1.0
        )
    
    def __mul__(self, other: Mat4) -> Mat4:
        """Matrix multiplication."""
        return Mat4(
            self.m11 * other.m11 + self.m12 * other.m21 + self.m13 * other.m31 + self.m14 * other.m41,
            self.m11 * other.m12 + self.m12 * other.m22 + self.m13 * other.m32 + self.m14 * other.m42,
            self.m11 * other.m13 + self.m12 * other.m23 + self.m13 * other.m33 + self.m14 * other.m43,
            self.m11 * other.m14 + self.m12 * other.m24 + self.m13 * other.m34 + self.m14 * other.m44,
            self.m21 * other.m11 + self.m22 * other.m21 + self.m23 * other.m31 + self.m24 * other.m41,
            self.m21 * other.m12 + self.m22 * other.m22 + self.m23 * other.m32 + self.m24 * other.m42,
            self.m21 * other.m13 + self.m22 * other.m23 + self.m23 * other.m33 + self.m24 * other.m43,
            self.m21 * other.m14 + self.m22 * other.m24 + self.m23 * other.m34 + self.m24 * other.m44,
            self.m31 * other.m11 + self.m32 * other.m21 + self.m33 * other.m31 + self.m34 * other.m41,
            self.m31 * other.m12 + self.m32 * other.m22 + self.m33 * other.m32 + self.m34 * other.m42,
            self.m31 * other.m13 + self.m32 * other.m23 + self.m33 * other.m33 + self.m34 * other.m43,
            self.m31 * other.m14 + self.m32 * other.m24 + self.m33 * other.m34 + self.m34 * other.m44,
            self.m41 * other.m11 + self.m42 * other.m21 + self.m43 * other.m31 + self.m44 * other.m41,
            self.m41 * other.m12 + self.m42 * other.m22 + self.m43 * other.m32 + self.m44 * other.m42,
            self.m41 * other.m13 + self.m42 * other.m23 + self.m43 * other.m33 + self.m44 * other.m43,
            self.m41 * other.m14 + self.m42 * other.m24 + self.m43 * other.m34 + self.m44 * other.m44
        )
    
    def transform_point(self, p: Vec3) -> Vec3:
        """Transform a position (point) by this matrix."""
        return self.transform_position(p)
    
    def transform_position(self, p: Vec3) -> Vec3:
        """Transform a position (point) by this matrix."""
        return Vec3(
            self.m11 * p.x + self.m12 * p.y + self.m13 * p.z + self.m14,
            self.m21 * p.x + self.m22 * p.y + self.m23 * p.z + self.m24,
            self.m31 * p.x + self.m32 * p.y + self.m33 * p.z + self.m34
        )
    
    def transform_vector(self, v: Vec3) -> Vec3:
        """Transform a vector (direction) by this matrix, ignoring translation."""
        return Vec3(
            self.m11 * v.x + self.m12 * v.y + self.m13 * v.z,
            self.m21 * v.x + self.m22 * v.y + self.m23 * v.z,
            self.m31 * v.x + self.m32 * v.y + self.m33 * v.z
        )
    
    def transform_normal(self, n: Vec3) -> Vec3:
        """
        Transform a normal by this matrix.
        Assumes the top-left 3x3 portion of the matrix is orthogonal.
        """
        return Vec3(
            n.x * self.m11 + n.y * self.m21 + n.z * self.m31,
            n.x * self.m12 + n.y * self.m22 + n.z * self.m32,
            n.x * self.m13 + n.y * self.m23 + n.z * self.m33
        )
    
    def inverse(self) -> Mat4:
        """Calculate the inverse of this matrix."""
        return self.get_inverse()
    
    def get_inverse(self) -> Mat4:
        """Calculate the inverse of this matrix."""
        a = Vec3(self.m11, self.m21, self.m31)
        b = Vec3(self.m12, self.m22, self.m32)
        c = Vec3(self.m13, self.m23, self.m33)
        d = Vec3(self.m14, self.m24, self.m34)
        
        x = self.m41
        y = self.m42
        z = self.m43
        w = self.m44
        
        s = Vec3.cross(a, b)
        t = Vec3.cross(c, d)
        u = a * y - b * x
        v = c * w - d * z
        
        inv_det = 1.0 / (Vec3.dot(s, v) + Vec3.dot(t, u))
        s = s * inv_det
        t = t * inv_det
        u = u * inv_det
        v = v * inv_det
        
        r0 = Vec3.cross(b, v) + t * y
        r1 = Vec3.cross(v, a) - t * x
        r2 = Vec3.cross(d, u) + s * w
        r3 = Vec3.cross(u, c) - s * z
        
        return Mat4(
            r0.x, r0.y, r0.z, -Vec3.dot(b, t),
            r1.x, r1.y, r1.z, +Vec3.dot(a, t),
            r2.x, r2.y, r2.z, -Vec3.dot(d, s),
            r3.x, r3.y, r3.z, +Vec3.dot(c, s)
        )
    
    @property
    def m(self) -> list:
        """Get the matrix as a 2D list."""
        return [
            [self.m11, self.m12, self.m13, self.m14],
            [self.m21, self.m22, self.m23, self.m24],
            [self.m31, self.m32, self.m33, self.m34],
            [self.m41, self.m42, self.m43, self.m44]
        ]
    
    def to_list(self) -> list:
        """Get the matrix as a flat list (column-major order for GLTF)."""
        # GLTF expects column-major order
        return [
            self.m11, self.m21, self.m31, self.m41,
            self.m12, self.m22, self.m32, self.m42,
            self.m13, self.m23, self.m33, self.m43,
            self.m14, self.m24, self.m34, self.m44
        ]
    
    def __str__(self) -> str:
        """String representation."""
        return (f"{{ {{M11:{self.m11} M12:{self.m12} M13:{self.m13} M14:{self.m14}}} "
                f"{{M21:{self.m21} M22:{self.m22} M23:{self.m23} M24:{self.m24}}} "
                f"{{M31:{self.m31} M32:{self.m32} M33:{self.m33} M34:{self.m34}}} "
                f"{{M41:{self.m41} M42:{self.m42} M43:{self.m43} M44:{self.m44}}} }}")
    
    def __repr__(self) -> str:
        """Detailed string representation."""
        return (f"Mat4(\n"
                f"  {self.m11}, {self.m12}, {self.m13}, {self.m14},\n"
                f"  {self.m21}, {self.m22}, {self.m23}, {self.m24},\n"
                f"  {self.m31}, {self.m32}, {self.m33}, {self.m34},\n"
                f"  {self.m41}, {self.m42}, {self.m43}, {self.m44}\n"
                f")")