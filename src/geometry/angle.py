"""Angle class for working with angles in degrees and radians.

This module provides the <PERSON><PERSON> class for angle conversions and operations.
"""

from __future__ import annotations
import math
from dataclasses import dataclass


@dataclass
class Angle:
    """Represents an angle that can be accessed in both degrees and radians."""
    
    _radians: float
    
    @staticmethod
    def from_radians(radians: float) -> <PERSON><PERSON>:
        """Create an angle from radians."""
        return Angle(radians)
    
    @staticmethod
    def from_degrees(degrees: float) -> <PERSON><PERSON>:
        """Create an angle from degrees."""
        return Angle(math.radians(degrees))
    
    @property
    def radians(self) -> float:
        """Get the angle in radians."""
        return self._radians
    
    @property
    def degrees(self) -> float:
        """Get the angle in degrees."""
        return math.degrees(self._radians)
    
    def normalized(self) -> <PERSON><PERSON>:
        """Return angle normalized to [0, 2π) range."""
        radians = self._radians % (2 * math.pi)
        if radians < 0:
            radians += 2 * math.pi
        return Angle(radians)
    
    def sin(self) -> float:
        """Return the sine of the angle."""
        return math.sin(self._radians)
    
    def cos(self) -> float:
        """Return the cosine of the angle."""
        return math.cos(self._radians)
    
    def tan(self) -> float:
        """Return the tangent of the angle."""
        return math.tan(self._radians)
    
    def __add__(self, other: Angle) -> Angle:
        """Add two angles."""
        return Angle(self._radians + other._radians)
    
    def __sub__(self, other: Angle) -> Angle:
        """Subtract two angles."""
        return Angle(self._radians - other._radians)
    
    def __mul__(self, scalar: float) -> Angle:
        """Multiply angle by scalar."""
        return Angle(self._radians * scalar)
    
    def __truediv__(self, scalar: float) -> Angle:
        """Divide angle by scalar."""
        return Angle(self._radians / scalar)
    
    def __str__(self) -> str:
        """String representation."""
        return f"{self.degrees:.2f}°"
    
    def __repr__(self) -> str:
        """Detailed representation."""
        return f"Angle(radians={self._radians}, degrees={self.degrees})"