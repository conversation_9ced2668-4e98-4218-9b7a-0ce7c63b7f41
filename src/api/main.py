"""
BIM Backend FastAPI Application

Main entry point for the BIM Backend API service.

C# Reference: BimAPI/Program.cs, Startup.cs
"""

from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import logging
from datetime import datetime
import os

from .endpoints import router
from .swagger_enhanced import enhanced_router
from .models import HealthResponse, ErrorResponse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="BIM Backend API",
    description="""Building Information Modeling API for generating 3D carports and sheds.
    
    ## Features
    - 🏗️ Generate IFC files for BIM software compatibility
    - 🎮 Create GLB files for 3D visualization
    - 📐 Support for multiple roof types (FLAT, GABLE, AWNING, ATTACHED_AWNING)
    - 🔧 Engineering material overrides
    - 🔐 Encrypted API endpoints for secure communication
    - 📦 Batch generation of multiple formats
    
    ## Quick Start
    1. Use `/api/v2/examples` to get sample configurations
    2. Try `/api/v2/generate/ifc` or `/api/v2/generate/glb` for direct generation
    3. Download files using the provided URLs
    
    ## API Versions
    - v1: Original encrypted API (`/api/carport/*`)
    - v2: Enhanced direct API (`/api/v2/*`)
    """,
    version="2.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc",
    openapi_url="/api/openapi.json"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=os.getenv("CORS_ORIGINS", "*").split(","),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.on_event("startup")
async def startup_event():
    """Initialize services and connections on startup"""
    logger.info("BIM Backend API starting up...")
    # TODO: Initialize database connections, cache, etc.
    logger.info("BIM Backend API started successfully")


@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup resources on shutdown"""
    logger.info("BIM Backend API shutting down...")
    # TODO: Close database connections, cleanup temp files, etc.
    logger.info("BIM Backend API shutdown complete")


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler for unhandled errors"""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error="internal_server_error",
            message="An unexpected error occurred",
            details={"type": type(exc).__name__},
            request_id=request.headers.get("X-Request-ID")
        ).model_dump()
    )


@app.get("/", include_in_schema=False)
async def root():
    """Root endpoint - redirects to documentation"""
    return {"message": "BIM Backend API", "docs": "/api/docs"}


@app.get("/api/health", response_model=HealthResponse, tags=["System"])
async def health_check():
    """
    Health check endpoint
    
    Returns the current health status of the API service.
    """
    return HealthResponse(
        status="healthy",
        version="1.0.0",
        timestamp=datetime.utcnow().isoformat() + "Z"
    )


# Include API routes
app.include_router(router, prefix="/api")
app.include_router(enhanced_router, prefix="/api/v2")


if __name__ == "__main__":
    import uvicorn
    
    # Run the application
    uvicorn.run(
        "api.main:app",
        host="0.0.0.0",
        port=int(os.getenv("PORT", 8000)),
        reload=os.getenv("ENV", "development") == "development",
        log_level="info"
    )