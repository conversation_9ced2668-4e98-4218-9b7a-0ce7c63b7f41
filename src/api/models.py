"""
API Request and Response Models

This module defines the Pydantic models for API requests and responses.

C# Reference: BimAPI/Models/
"""

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from enum import Enum


class ResponseStatus(str, Enum):
    """Response status enumeration"""
    SUCCESS = "success"
    ERROR = "error"
    VALIDATION_ERROR = "validation_error"


class CarportRequest(BaseModel):
    """
    Encrypted carport creation request model.
    
    C# Reference: CarportRequest.cs
    """
    encrypted_data: str = Field(..., description="AES-encrypted JSON payload containing building input data")
    
    class Config:
        json_schema_extra = {
            "example": {
                "encrypted_data": "base64_encoded_encrypted_json_string"
            }
        }


class CarportResponse(BaseModel):
    """
    Carport creation response model.
    
    C# Reference: CarportResponse.cs
    """
    success: bool = Field(..., description="Whether the request was successful")
    status: ResponseStatus = Field(..., description="Response status code")
    message: str = Field(..., description="Human-readable response message")
    file_url: Optional[str] = Field(None, description="URL to download the generated file")
    error_details: Optional[Dict[str, Any]] = Field(None, description="Detailed error information if applicable")
    
    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "status": "success",
                "message": "Carport generated successfully",
                "file_url": "/api/download/abc123.gltf",
                "error_details": None
            }
        }


class HealthResponse(BaseModel):
    """Health check response model"""
    status: str = Field(..., description="Service health status")
    version: str = Field(..., description="API version")
    timestamp: str = Field(..., description="Current server timestamp")
    
    class Config:
        json_schema_extra = {
            "example": {
                "status": "healthy",
                "version": "1.0.0",
                "timestamp": "2024-01-15T10:30:00Z"
            }
        }


class ErrorResponse(BaseModel):
    """Standard error response model"""
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    request_id: Optional[str] = Field(None, description="Request tracking ID")
    
    class Config:
        json_schema_extra = {
            "example": {
                "error": "validation_error",
                "message": "Invalid building input parameters",
                "details": {
                    "field": "span",
                    "reason": "Span must be between 3000 and 12000mm"
                },
                "request_id": "req_123456"
            }
        }