"""
BIM Backend Python Implementation

This package provides a complete Building Information Modeling (BIM) system
for generating 3D carports and sheds with engineering validation.

C# Reference: BimCoreLibrary and Shedkit.Bim.Shed
Author: BIM Backend Development Team
Date: 2024-01-15
"""

import sys
from pathlib import Path

# Add src directory to Python path for absolute imports
sys.path.insert(0, str(Path(__file__).parent.parent))

__version__ = "1.0.0"
__all__ = ["geometry", "materials", "bim", "business", "api", "services", "output"]