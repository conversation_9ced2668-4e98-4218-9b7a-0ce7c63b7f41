"""
Input validation for IFC generation.

This module provides validation functions to ensure input data is valid
before IFC generation begins.
"""

from typing import Any, List, Optional
import logging

from ...geometry.primitives import Vec3, Line3
from ...materials.frame_material import FrameMaterial
from ...business.building_input import BuildingInput, CarportRoofType
from .ifc_exceptions import InvalidInputError, InvalidGeometryError, InvalidMaterialError
from .ifc_constants import IFCConstants


logger = logging.getLogger(__name__)


class BuildingInputValidator:
    """Validates building input parameters."""
    
    # Minimum and maximum dimensions (mm)
    MIN_SPAN = 3000.0  # 3m minimum
    MAX_SPAN = 24000.0  # 24m maximum
    MIN_LENGTH = 3000.0  # 3m minimum
    MAX_LENGTH = 30000.0  # 30m maximum
    MIN_HEIGHT = 2100.0  # 2.1m minimum
    MAX_HEIGHT = 6000.0  # 6m maximum
    
    # Pitch constraints (degrees)
    MIN_PITCH_FLAT = 0.5
    MAX_PITCH_FLAT = 3.0
    MIN_PITCH_GABLE = 5.0
    MAX_PITCH_GABLE = 30.0
    
    # Bay constraints
    MIN_BAYS = 1
    MAX_BAYS = 10
    
    @classmethod
    def validate(cls, building_input: BuildingInput) -> None:
        """
        Validate building input parameters.
        
        Args:
            building_input: Building input to validate
            
        Raises:
            InvalidInputError: If any parameter is invalid
        """
        # Validate span
        if building_input.span <= 0:
            raise InvalidInputError("span", building_input.span, "Span must be positive")
        if building_input.span < cls.MIN_SPAN:
            raise InvalidInputError("span", building_input.span, f"Minimum span is {cls.MIN_SPAN}mm")
        if building_input.span > cls.MAX_SPAN:
            raise InvalidInputError("span", building_input.span, f"Maximum span is {cls.MAX_SPAN}mm")
        
        # Validate length
        if building_input.length <= 0:
            raise InvalidInputError("length", building_input.length, "Length must be positive")
        if building_input.length < cls.MIN_LENGTH:
            raise InvalidInputError("length", building_input.length, f"Minimum length is {cls.MIN_LENGTH}mm")
        if building_input.length > cls.MAX_LENGTH:
            raise InvalidInputError("length", building_input.length, f"Maximum length is {cls.MAX_LENGTH}mm")
        
        # Validate height
        if building_input.height <= 0:
            raise InvalidInputError("height", building_input.height, "Height must be positive")
        if building_input.height < cls.MIN_HEIGHT:
            raise InvalidInputError("height", building_input.height, f"Minimum height is {cls.MIN_HEIGHT}mm")
        if building_input.height > cls.MAX_HEIGHT:
            raise InvalidInputError("height", building_input.height, f"Maximum height is {cls.MAX_HEIGHT}mm")
        
        # Validate bays
        if building_input.bays < cls.MIN_BAYS:
            raise InvalidInputError("bays", building_input.bays, f"Minimum bays is {cls.MIN_BAYS}")
        if building_input.bays > cls.MAX_BAYS:
            raise InvalidInputError("bays", building_input.bays, f"Maximum bays is {cls.MAX_BAYS}")
        
        # Validate pitch based on roof type
        if hasattr(building_input, 'pitch'):
            cls._validate_pitch(building_input.roof_type, building_input.pitch)
        
        logger.debug(f"Building input validated: {building_input.roof_type.value} "
                    f"{building_input.span}x{building_input.length}x{building_input.height}")
    
    @classmethod
    def _validate_pitch(cls, roof_type: CarportRoofType, pitch: float) -> None:
        """Validate pitch for specific roof type."""
        if roof_type == CarportRoofType.FLAT:
            if pitch < cls.MIN_PITCH_FLAT or pitch > cls.MAX_PITCH_FLAT:
                raise InvalidInputError(
                    "pitch", pitch, 
                    f"Flat roof pitch must be between {cls.MIN_PITCH_FLAT}° and {cls.MAX_PITCH_FLAT}°"
                )
        elif roof_type == CarportRoofType.GABLE:
            if pitch < cls.MIN_PITCH_GABLE or pitch > cls.MAX_PITCH_GABLE:
                raise InvalidInputError(
                    "pitch", pitch,
                    f"Gable roof pitch must be between {cls.MIN_PITCH_GABLE}° and {cls.MAX_PITCH_GABLE}°"
                )


class GeometryValidator:
    """Validates geometric data."""
    
    EPSILON = 1e-6  # Tolerance for floating point comparisons
    
    @classmethod
    def validate_point(cls, point: Vec3, name: str = "point") -> None:
        """
        Validate a 3D point.
        
        Args:
            point: Point to validate
            name: Name for error messages
            
        Raises:
            InvalidGeometryError: If point is invalid
        """
        if point is None:
            raise InvalidGeometryError(f"{name} is None")
        
        # Check for NaN or infinity
        for coord, axis in [(point.x, "X"), (point.y, "Y"), (point.z, "Z")]:
            if not cls._is_finite(coord):
                raise InvalidGeometryError(
                    f"{name} has invalid {axis} coordinate: {coord}", 
                    point
                )
    
    @classmethod
    def validate_line(cls, line: Line3, name: str = "line") -> None:
        """
        Validate a 3D line.
        
        Args:
            line: Line to validate
            name: Name for error messages
            
        Raises:
            InvalidGeometryError: If line is invalid
        """
        if line is None:
            raise InvalidGeometryError(f"{name} is None")
        
        # Validate endpoints
        cls.validate_point(line.start, f"{name}.start")
        cls.validate_point(line.end, f"{name}.end")
        
        # Check for zero-length line
        length = line.length()
        if length < cls.EPSILON:
            raise InvalidGeometryError(
                f"{name} has zero length", 
                {"start": line.start, "end": line.end, "length": length}
            )
    
    @classmethod
    def validate_dimension(cls, value: float, name: str, min_value: float = 0.0) -> None:
        """
        Validate a dimension value.
        
        Args:
            value: Dimension value
            name: Name for error messages
            min_value: Minimum allowed value
            
        Raises:
            InvalidGeometryError: If dimension is invalid
        """
        if not cls._is_finite(value):
            raise InvalidGeometryError(f"{name} is not a valid number: {value}")
        
        if value < min_value:
            raise InvalidGeometryError(
                f"{name} must be at least {min_value}, got {value}"
            )
    
    @staticmethod
    def _is_finite(value: float) -> bool:
        """Check if value is finite (not NaN or infinity)."""
        try:
            return float('-inf') < value < float('inf')
        except:
            return False


class MaterialValidator:
    """Validates material definitions."""
    
    # Known material patterns
    C_SECTION_PATTERN = r'^C\d{3,4}\d{2}$'  # e.g., C15012, C20020
    Z_SECTION_PATTERN = r'^Z\d{3,4}\d{2}$'  # e.g., Z15015, Z20020
    SHS_PATTERN = r'^SHS\d{3,4}\d{3,4}\d{2}$'  # e.g., SHS07507525
    TH_PATTERN = r'^TH\d{3}\d{3}$'  # e.g., TH064100
    
    @classmethod
    def validate_frame_material(cls, material: FrameMaterial) -> None:
        """
        Validate frame material.
        
        Args:
            material: Material to validate
            
        Raises:
            InvalidMaterialError: If material is invalid
        """
        if material is None:
            raise InvalidMaterialError("None", [])
        
        if not material.name:
            raise InvalidMaterialError("(empty name)", [])
        
        # Validate dimensions
        if material.width <= 0:
            raise InvalidMaterialError(
                material.name, 
                reason=f"Invalid width: {material.width}"
            )
        
        if material.height <= 0:
            raise InvalidMaterialError(
                material.name,
                reason=f"Invalid height: {material.height}"
            )
        
        if hasattr(material, 'thickness') and material.thickness <= 0:
            raise InvalidMaterialError(
                material.name,
                reason=f"Invalid thickness: {material.thickness}"
            )
        
        logger.debug(f"Material validated: {material.name}")


class IFCDataValidator:
    """Validates data before IFC generation."""
    
    def __init__(self):
        """Initialize IFC data validator."""
        self.building_validator = BuildingInputValidator()
        self.geometry_validator = GeometryValidator()
        self.material_validator = MaterialValidator()
    
    def validate_carport_product(self, product: Any) -> None:
        """
        Validate a complete carport product.
        
        Args:
            product: Carport product to validate
            
        Raises:
            Various validation exceptions if data is invalid
        """
        # Validate building input if present
        if hasattr(product, 'building_input'):
            self.building_validator.validate(product.building_input)
        
        # Validate materials
        if hasattr(product, 'column') and product.column:
            self.material_validator.validate_frame_material(product.column)
        
        if hasattr(product, 'rafter') and product.rafter:
            self.material_validator.validate_frame_material(product.rafter)
        
        if hasattr(product, 'purlin') and product.purlin:
            self.material_validator.validate_frame_material(product.purlin)
        
        logger.info("Carport product validation complete")