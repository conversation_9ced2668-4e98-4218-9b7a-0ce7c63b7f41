"""
Mesh building utilities for 3D output generation.

This module provides tools for converting BIM geometry into
triangulated meshes suitable for 3D file formats.
"""

from dataclasses import dataclass, field
from typing import List, Tuple, Optional, Dict, Any
import numpy as np

from ...geometry.primitives import Vec3, Vec2
from ...geometry.matrix import Mat4
from ...materials.base import FrameMaterial
from ...materials.visual import ColorMaterial


@dataclass
class MeshData:
    """Container for 3D mesh data."""
    vertices: List[Vec3] = field(default_factory=list)      # Vertex positions
    normals: List[Vec3] = field(default_factory=list)       # Vertex normals
    uvs: List[Vec2] = field(default_factory=list)          # Texture coordinates
    triangles: List[Tuple[int, int, int]] = field(default_factory=list)  # Triangle indices
    material_name: Optional[str] = None                     # Material reference
    
    def add_vertex(self, position: Vec3, normal: Vec3 = None, uv: Vec2 = None) -> int:
        """
        Add a vertex to the mesh.
        
        Args:
            position: 3D position
            normal: Surface normal (optional)
            uv: Texture coordinate (optional)
            
        Returns:
            Index of the added vertex
        """
        index = len(self.vertices)
        self.vertices.append(position)
        
        if normal:
            self.normals.append(normal)
        else:
            # Default to up vector if no normal provided
            self.normals.append(Vec3(0, 0, 1))
            
        if uv:
            self.uvs.append(uv)
        else:
            # Default UV
            self.uvs.append(Vec2(0, 0))
            
        return index
    
    def add_triangle(self, i0: int, i1: int, i2: int):
        """Add a triangle using vertex indices."""
        self.triangles.append((i0, i1, i2))
        
    def add_quad(self, i0: int, i1: int, i2: int, i3: int):
        """Add a quad as two triangles."""
        self.add_triangle(i0, i1, i2)
        self.add_triangle(i0, i2, i3)
        
    def transform(self, matrix: Mat4):
        """Transform all vertices by a matrix."""
        for i, vertex in enumerate(self.vertices):
            self.vertices[i] = matrix.transform_point(vertex)
            
        # Transform normals (without translation)
        for i, normal in enumerate(self.normals):
            self.normals[i] = matrix.transform_vector(normal).normalized()
            
    def merge(self, other: 'MeshData'):
        """Merge another mesh into this one."""
        vertex_offset = len(self.vertices)
        
        # Add vertices
        self.vertices.extend(other.vertices)
        self.normals.extend(other.normals)
        self.uvs.extend(other.uvs)
        
        # Add triangles with offset indices
        for tri in other.triangles:
            self.triangles.append((
                tri[0] + vertex_offset,
                tri[1] + vertex_offset,
                tri[2] + vertex_offset
            ))


class MeshBuilder:
    """Utility class for building 3D meshes from BIM components."""
    
    @staticmethod
    def create_box(min_pt: Vec3, max_pt: Vec3) -> MeshData:
        """
        Create a box mesh from min/max points.
        
        Args:
            min_pt: Minimum corner
            max_pt: Maximum corner
            
        Returns:
            MeshData for the box
        """
        mesh = MeshData()
        
        # Create 8 vertices
        vertices = [
            Vec3(min_pt.x, min_pt.y, min_pt.z),  # 0: bottom-front-left
            Vec3(max_pt.x, min_pt.y, min_pt.z),  # 1: bottom-front-right
            Vec3(max_pt.x, max_pt.y, min_pt.z),  # 2: bottom-back-right
            Vec3(min_pt.x, max_pt.y, min_pt.z),  # 3: bottom-back-left
            Vec3(min_pt.x, min_pt.y, max_pt.z),  # 4: top-front-left
            Vec3(max_pt.x, min_pt.y, max_pt.z),  # 5: top-front-right
            Vec3(max_pt.x, max_pt.y, max_pt.z),  # 6: top-back-right
            Vec3(min_pt.x, max_pt.y, max_pt.z),  # 7: top-back-left
        ]
        
        # Add vertices with normals
        for v in vertices:
            mesh.add_vertex(v)
            
        # Create faces (CCW winding)
        # Bottom face
        mesh.add_quad(0, 3, 2, 1)
        # Top face  
        mesh.add_quad(4, 5, 6, 7)
        # Front face
        mesh.add_quad(0, 1, 5, 4)
        # Back face
        mesh.add_quad(2, 3, 7, 6)
        # Left face
        mesh.add_quad(0, 4, 7, 3)
        # Right face
        mesh.add_quad(1, 2, 6, 5)
        
        return mesh
    
    @staticmethod
    def create_cylinder(base_center: Vec3, top_center: Vec3, radius: float, 
                       segments: int = 16) -> MeshData:
        """
        Create a cylinder mesh.
        
        Args:
            base_center: Center of bottom circle
            top_center: Center of top circle
            radius: Cylinder radius
            segments: Number of segments around circumference
            
        Returns:
            MeshData for the cylinder
        """
        mesh = MeshData()
        
        # Calculate cylinder axis
        axis = (top_center - base_center).normalized()
        
        # Find perpendicular vectors
        if abs(axis.z) < 0.9:
            perp1 = Vec3.cross(axis, Vec3.unit_z()).normalized()
        else:
            perp1 = Vec3.cross(axis, Vec3.unit_x()).normalized()
        perp2 = Vec3.cross(axis, perp1).normalized()
        
        # Generate vertices
        for i in range(segments):
            angle = (i / segments) * 2 * np.pi
            cos_a = np.cos(angle)
            sin_a = np.sin(angle)
            
            # Radial offset
            offset = perp1 * (cos_a * radius) + perp2 * (sin_a * radius)
            
            # Bottom vertex
            mesh.add_vertex(base_center + offset)
            # Top vertex  
            mesh.add_vertex(top_center + offset)
            
        # Add center vertices for caps
        base_center_idx = mesh.add_vertex(base_center)
        top_center_idx = mesh.add_vertex(top_center)
        
        # Create side faces
        for i in range(segments):
            next_i = (i + 1) % segments
            
            # Side quad
            mesh.add_quad(
                i * 2,          # bottom current
                i * 2 + 1,      # top current
                next_i * 2 + 1, # top next
                next_i * 2      # bottom next
            )
            
            # Bottom cap triangle
            mesh.add_triangle(
                base_center_idx,
                next_i * 2,
                i * 2
            )
            
            # Top cap triangle
            mesh.add_triangle(
                top_center_idx,
                i * 2 + 1,
                next_i * 2 + 1
            )
            
        return mesh
    
    @staticmethod
    def create_frame_profile(frame_material: FrameMaterial, length: float,
                           transform: Mat4 = None) -> MeshData:
        """
        Create a 3D mesh from a frame material profile.
        
        Args:
            frame_material: Frame material with profile
            length: Extrusion length
            transform: Optional transformation matrix
            
        Returns:
            MeshData for the extruded profile
        """
        mesh = MeshData()
        
        # Get 2D profile points
        profile_points = frame_material.get_profile_points()
        if not profile_points:
            # Fallback to simple box
            width = frame_material.width / 1000.0  # mm to m
            height = frame_material.height / 1000.0
            return MeshBuilder.create_box(
                Vec3(-width/2, -height/2, 0),
                Vec3(width/2, height/2, length)
            )
            
        # Convert profile points to 3D at z=0 and z=length
        bottom_verts = []
        top_verts = []
        
        for pt in profile_points:
            # Convert mm to m
            x = pt.x / 1000.0
            y = pt.y / 1000.0
            
            bottom_idx = mesh.add_vertex(Vec3(x, y, 0))
            top_idx = mesh.add_vertex(Vec3(x, y, length))
            
            bottom_verts.append(bottom_idx)
            top_verts.append(top_idx)
            
        n_points = len(profile_points)
        
        # Create side faces
        for i in range(n_points):
            next_i = (i + 1) % n_points
            
            mesh.add_quad(
                bottom_verts[i],
                bottom_verts[next_i],
                top_verts[next_i],
                top_verts[i]
            )
            
        # Create end caps using simple triangulation
        # Bottom cap
        for i in range(1, n_points - 1):
            mesh.add_triangle(bottom_verts[0], bottom_verts[i], bottom_verts[i + 1])
            
        # Top cap
        for i in range(1, n_points - 1):
            mesh.add_triangle(top_verts[0], top_verts[i + 1], top_verts[i])
            
        # Apply transformation if provided
        if transform:
            mesh.transform(transform)
            
        mesh.material_name = frame_material.name
        
        return mesh
    
    @staticmethod
    def create_sheet(corners: List[Vec3], thickness: float = 0.001) -> MeshData:
        """
        Create a sheet/panel mesh from corner points.
        
        Args:
            corners: 4 corner points defining the sheet
            thickness: Sheet thickness in meters
            
        Returns:
            MeshData for the sheet
        """
        if len(corners) != 4:
            raise ValueError("Sheet requires exactly 4 corners")
            
        mesh = MeshData()
        
        # Calculate normal from first 3 points
        edge1 = corners[1] - corners[0]
        edge2 = corners[2] - corners[0]
        normal = Vec3.cross(edge1, edge2).normalized()
        
        # Create vertices for both sides
        for corner in corners:
            # Front face
            mesh.add_vertex(corner, normal)
            
        for corner in corners:
            # Back face
            mesh.add_vertex(corner + normal * (-thickness), -normal)
            
        # Create faces
        # Front face
        mesh.add_quad(0, 1, 2, 3)
        # Back face
        mesh.add_quad(4, 7, 6, 5)
        
        # Side faces
        mesh.add_quad(0, 4, 5, 1)
        mesh.add_quad(1, 5, 6, 2)
        mesh.add_quad(2, 6, 7, 3)
        mesh.add_quad(3, 7, 4, 0)
        
        return mesh
    
    @staticmethod
    def calculate_normals(mesh: MeshData):
        """
        Recalculate normals for a mesh based on face orientation.
        
        Args:
            mesh: MeshData to update normals for
        """
        # Reset normals
        mesh.normals = [Vec3(0, 0, 0) for _ in mesh.vertices]
        
        # Accumulate normals from faces
        for tri in mesh.triangles:
            v0 = mesh.vertices[tri[0]]
            v1 = mesh.vertices[tri[1]]
            v2 = mesh.vertices[tri[2]]
            
            # Calculate face normal
            edge1 = v1 - v0
            edge2 = v2 - v0
            face_normal = Vec3.cross(edge1, edge2).normalized()
            
            # Add to vertex normals
            for idx in tri:
                current = mesh.normals[idx]
                mesh.normals[idx] = current + face_normal
                
        # Normalize all vertex normals
        for i in range(len(mesh.normals)):
            mesh.normals[i] = mesh.normals[i].normalized()