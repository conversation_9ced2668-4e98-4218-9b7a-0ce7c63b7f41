"""
Base classes for output generation.

This module defines the abstract interfaces and common functionality
for all output generators (GLTF, DXF, IFC).
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
from typing import Dict, Any, Optional, List
from pathlib import Path
import logging

from src.bim.shed_bim import ShedBim


class OutputFormat(Enum):
    """Supported output file formats."""
    GLTF = "gltf"      # GL Transmission Format for 3D models
    GLB = "glb"        # Binary GLTF
    DXF = "dxf"        # Drawing Exchange Format
    IFC = "ifc"        # Industry Foundation Classes
    IFC4 = "ifc4"      # IFC4 specific format
    STL = "stl"        # Stereolithography format
    OBJ = "obj"        # Wavefront OBJ format


@dataclass
class OutputResult:
    """Result of output generation operation."""
    success: bool                      # Whether generation succeeded
    format: OutputFormat              # Format that was generated
    file_path: Optional[Path] = None  # Path to generated file
    file_size: Optional[int] = None   # Size in bytes
    errors: List[str] = None          # Any errors encountered
    warnings: List[str] = None        # Any warnings
    metadata: Dict[str, Any] = None   # Additional metadata
    
    def __post_init__(self):
        """Initialize empty lists if None."""
        if self.errors is None:
            self.errors = []
        if self.warnings is None:
            self.warnings = []
        if self.metadata is None:
            self.metadata = {}


class OutputGenerator(ABC):
    """
    Abstract base class for all output generators.
    
    Each generator must implement the generate method to create
    output files from a BIM model.
    """
    
    def __init__(self, output_dir: Path = None):
        """
        Initialize output generator.
        
        Args:
            output_dir: Directory to save output files (default: current dir)
        """
        self.output_dir = output_dir or Path.cwd()
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.logger = logging.getLogger(self.__class__.__name__)
        
    @abstractmethod
    def generate(self, bim: ShedBim, filename: str, **options) -> OutputResult:
        """
        Generate output file from BIM model.
        
        Args:
            bim: The BIM model to export
            filename: Output filename (without extension)
            **options: Format-specific options
            
        Returns:
            OutputResult with generation status and details
        """
        pass
    
    @abstractmethod
    def get_supported_formats(self) -> List[OutputFormat]:
        """
        Get list of formats supported by this generator.
        
        Returns:
            List of supported OutputFormat values
        """
        pass
    
    def validate_bim(self, bim: ShedBim) -> List[str]:
        """
        Validate BIM model for export.
        
        Args:
            bim: The BIM model to validate
            
        Returns:
            List of validation errors (empty if valid)
        """
        errors = []
        
        # Basic validation
        if not bim:
            errors.append("BIM model is None")
            return errors
            
        if not bim.main:
            errors.append("BIM model has no main structure")
            return errors
            
        # Check for required components
        if not hasattr(bim.main, 'roof_type'):
            errors.append("Main structure missing roof_type")
            
        sides = bim.main.get_sides() if hasattr(bim.main, 'get_sides') else []
        if not sides:
            errors.append("No sides found in structure")
            
        # Validate materials exist
        for side in sides:
            if hasattr(side, 'columns'):
                for column in side.columns:
                    if not column.frame_material:
                        errors.append(f"Column missing frame material")
                        
        return errors
    
    def get_output_path(self, filename: str, format: OutputFormat) -> Path:
        """
        Get full output path for file.
        
        Args:
            filename: Base filename (without extension)
            format: Output format
            
        Returns:
            Full path with appropriate extension
        """
        extension = format.value
        return self.output_dir / f"{filename}.{extension}"
    
    def create_metadata(self, bim: ShedBim) -> Dict[str, Any]:
        """
        Create metadata for the output file.
        
        Args:
            bim: The BIM model
            
        Returns:
            Dictionary of metadata
        """
        metadata = {
            "generator": "BIM Backend Python",
            "version": "1.0.0",
            "building_type": getattr(bim, 'building_type', 'UNKNOWN'),
        }
        
        if bim.main:
            metadata["roof_type"] = getattr(bim.main, 'roof_type', 'UNKNOWN')
            
            # Count components
            sides = bim.main.get_sides() if hasattr(bim.main, 'get_sides') else []
            total_columns = sum(len(getattr(side, 'columns', [])) for side in sides)
            metadata["column_count"] = total_columns
            
            roofs = bim.main.get_roofs() if hasattr(bim.main, 'get_roofs') else []
            total_rafters = sum(len(getattr(roof, 'rafters', [])) for roof in roofs)
            metadata["rafter_count"] = total_rafters
            
        return metadata