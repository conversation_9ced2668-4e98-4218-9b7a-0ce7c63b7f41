"""
DXF output generator for CAD drawings.

This module converts BIM models to DXF (Drawing Exchange Format)
for use in AutoCAD and other CAD applications.
"""

from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
import math

from src.bim.shed_bim import ShedBim
from src.bim.components import ShedBimColumn, ShedBimSection
from src.geometry.primitives import Vec3, Vec2
from src.geometry.matrix import Mat4
from src.geometry.helpers import Geo
from src.materials.helpers import FrameMaterialHelper
from src.output.base.output_base import OutputGenerator, OutputFormat, OutputResult


class DXFGenerator(OutputGenerator):
    """
    Generator for DXF (Drawing Exchange Format) files.
    
    DXF is a CAD data file format developed by Autodesk for enabling
    data interoperability between AutoCAD and other programs.
    """
    
    # DXF color codes
    COLOR_RED = 1
    COLOR_YELLOW = 2
    COLOR_GREEN = 3
    COLOR_CYAN = 4
    COLOR_BLUE = 5
    COLOR_MAGENTA = 6
    COLOR_WHITE = 7
    COLOR_GRAY = 8
    COLOR_BYLAYER = 256
    
    def __init__(self, output_dir: Path = None):
        """Initialize DXF generator."""
        super().__init__(output_dir)
        self.material_library = MaterialLibrary.get_default()
        
    def get_supported_formats(self) -> List[OutputFormat]:
        """Get supported formats."""
        return [OutputFormat.DXF]
        
    def generate(self, bim: ShedBim, filename: str, **options) -> OutputResult:
        """
        Generate DXF file from BIM model.
        
        Args:
            bim: The BIM model to export
            filename: Output filename (without extension)
            **options: Additional options:
                - views: List of views to generate ['top', 'front', 'side', '3d']
                - scale: Drawing scale (default: 1:100)
                - units: Drawing units ('mm', 'm') (default: 'mm')
                
        Returns:
            OutputResult with generation status
        """
        # Validate BIM
        errors = self.validate_bim(bim)
        if errors:
            return OutputResult(
                success=False,
                format=OutputFormat.DXF,
                errors=errors
            )
            
        try:
            # Get options
            views = options.get('views', ['top', 'front', 'side'])
            scale = options.get('scale', 100)  # 1:100
            units = options.get('units', 'mm')
            
            # Initialize DXF content
            dxf_content = []
            
            # Write header
            self._write_header(dxf_content, units)
            
            # Write tables (layers, styles, etc.)
            self._write_tables(dxf_content)
            
            # Write blocks
            self._write_blocks(dxf_content)
            
            # Write entities
            self._write_entities(dxf_content, bim, views, scale)
            
            # Write footer
            self._write_footer(dxf_content)
            
            # Write file
            output_path = self.get_output_path(filename, OutputFormat.DXF)
            content = '\n'.join(dxf_content)
            output_path.write_text(content)
            
            return OutputResult(
                success=True,
                format=OutputFormat.DXF,
                file_path=output_path,
                file_size=len(content),
                metadata=self.create_metadata(bim)
            )
            
        except Exception as e:
            self.logger.error(f"Failed to generate DXF: {str(e)}")
            return OutputResult(
                success=False,
                format=OutputFormat.DXF,
                errors=[f"Generation failed: {str(e)}"]
            )
            
    def _write_header(self, dxf: List[str], units: str):
        """Write DXF header section."""
        dxf.extend([
            "0", "SECTION",
            "2", "HEADER",
            "9", "$ACADVER", "1", "AC1024",  # AutoCAD 2010
            "9", "$INSBASE", "10", "0.0", "20", "0.0", "30", "0.0",
            "9", "$EXTMIN", "10", "-10000.0", "20", "-10000.0", "30", "-10000.0",
            "9", "$EXTMAX", "10", "10000.0", "20", "10000.0", "30", "10000.0",
            "9", "$LIMMIN", "10", "-10000.0", "20", "-10000.0",
            "9", "$LIMMAX", "10", "10000.0", "20", "10000.0",
            "9", "$ORTHOMODE", "70", "0",
            "9", "$REGENMODE", "70", "1",
            "9", "$FILLMODE", "70", "1",
            "9", "$QTEXTMODE", "70", "0",
            "9", "$MIRRTEXT", "70", "0",
            "9", "$LTSCALE", "40", "1.0",
            "9", "$ATTMODE", "70", "1",
            "9", "$TEXTSIZE", "40", "200.0",
            "9", "$TRACEWID", "40", "1.0",
            "9", "$TEXTSTYLE", "7", "STANDARD",
            "9", "$CLAYER", "8", "0",
            "9", "$CELTYPE", "6", "BYLAYER",
            "9", "$CECOLOR", "62", "256",
            "9", "$CELTSCALE", "40", "1.0",
            "9", "$DISPSILH", "70", "0",
        ])
        
        # Set units
        if units == 'mm':
            dxf.extend(["9", "$INSUNITS", "70", "4"])  # Millimeters
        else:
            dxf.extend(["9", "$INSUNITS", "70", "6"])  # Meters
            
        dxf.extend(["0", "ENDSEC"])
        
    def _write_tables(self, dxf: List[str]):
        """Write DXF tables section."""
        dxf.extend(["0", "SECTION", "2", "TABLES"])
        
        # LTYPE table
        dxf.extend([
            "0", "TABLE", "2", "LTYPE",
            "5", "5", "330", "0", "100", "AcDbSymbolTable", "70", "3",
            "0", "LTYPE", "5", "14", "330", "5", "100", "AcDbSymbolTableRecord",
            "100", "AcDbLinetypeTableRecord", "2", "BYBLOCK", "70", "0",
            "3", "", "72", "65", "73", "0", "40", "0.0",
            "0", "LTYPE", "5", "15", "330", "5", "100", "AcDbSymbolTableRecord",
            "100", "AcDbLinetypeTableRecord", "2", "BYLAYER", "70", "0",
            "3", "", "72", "65", "73", "0", "40", "0.0",
            "0", "LTYPE", "5", "16", "330", "5", "100", "AcDbSymbolTableRecord",
            "100", "AcDbLinetypeTableRecord", "2", "CONTINUOUS", "70", "0",
            "3", "Solid line", "72", "65", "73", "0", "40", "0.0",
            "0", "ENDTAB"
        ])
        
        # LAYER table
        dxf.extend([
            "0", "TABLE", "2", "LAYER",
            "5", "2", "330", "0", "100", "AcDbSymbolTable", "70", "10"
        ])
        
        # Define layers
        layers = [
            ("0", self.COLOR_WHITE, "CONTINUOUS"),
            ("COLUMNS", self.COLOR_RED, "CONTINUOUS"),
            ("RAFTERS", self.COLOR_GREEN, "CONTINUOUS"),
            ("PURLINS", self.COLOR_BLUE, "CONTINUOUS"),
            ("ROOF", self.COLOR_CYAN, "CONTINUOUS"),
            ("DIMENSIONS", self.COLOR_YELLOW, "CONTINUOUS"),
            ("TEXT", self.COLOR_WHITE, "CONTINUOUS"),
            ("HIDDEN", self.COLOR_GRAY, "CONTINUOUS")
        ]
        
        for layer_name, color, linetype in layers:
            dxf.extend([
                "0", "LAYER", "5", "40", "330", "2", "100", "AcDbSymbolTableRecord",
                "100", "AcDbLayerTableRecord", "2", layer_name, "70", "0",
                "62", str(color), "6", linetype, "370", "-3", "390", "F"
            ])
            
        dxf.extend(["0", "ENDTAB"])
        
        # TEXT STYLE table
        dxf.extend([
            "0", "TABLE", "2", "STYLE",
            "5", "3", "330", "0", "100", "AcDbSymbolTable", "70", "1",
            "0", "STYLE", "5", "11", "330", "3", "100", "AcDbSymbolTableRecord",
            "100", "AcDbTextStyleTableRecord", "2", "STANDARD", "70", "0",
            "40", "0.0", "41", "1.0", "50", "0.0", "71", "0", "42", "200.0",
            "3", "Arial", "4", "",
            "0", "ENDTAB"
        ])
        
        dxf.extend(["0", "ENDSEC"])
        
    def _write_blocks(self, dxf: List[str]):
        """Write DXF blocks section."""
        dxf.extend([
            "0", "SECTION", "2", "BLOCKS",
            "0", "BLOCK", "5", "20", "330", "1F", "100", "AcDbEntity",
            "8", "0", "100", "AcDbBlockBegin", "2", "*MODEL_SPACE", "70", "0",
            "10", "0.0", "20", "0.0", "30", "0.0", "3", "*MODEL_SPACE",
            "1", "", "0", "ENDBLK", "5", "21", "330", "1F", "100", "AcDbEntity",
            "8", "0", "100", "AcDbBlockEnd",
            "0", "ENDSEC"
        ])
        
    def _write_entities(self, dxf: List[str], bim: ShedBim, views: List[str], scale: float):
        """Write DXF entities section."""
        dxf.extend(["0", "SECTION", "2", "ENTITIES"])
        
        if not bim.main:
            dxf.extend(["0", "ENDSEC"])
            return
            
        # Calculate layout positions for multiple views
        view_spacing = 15000  # 15m spacing between views
        
        for idx, view in enumerate(views):
            origin_x = idx * view_spacing
            origin_y = 0
            
            if view == 'top':
                self._draw_top_view(dxf, bim, origin_x, origin_y, scale)
            elif view == 'front':
                self._draw_front_view(dxf, bim, origin_x, origin_y, scale)
            elif view == 'side':
                self._draw_side_view(dxf, bim, origin_x, origin_y, scale)
            elif view == '3d':
                self._draw_3d_view(dxf, bim, origin_x, origin_y, scale)
                
            # Add view label
            self._add_text(dxf, f"{view.upper()} VIEW", 
                          origin_x + 1000, origin_y - 1000, 500, "TEXT")
                          
        dxf.extend(["0", "ENDSEC"])
        
    def _draw_top_view(self, dxf: List[str], bim: ShedBim, origin_x: float, origin_y: float, scale: float):
        """Draw top/plan view."""
        # Draw columns as circles
        sides = bim.main.get_sides()
        for side in sides:
            if hasattr(side, 'columns'):
                for column in side.columns:
                    # Project to XY plane
                    x = origin_x + column.base.x * scale
                    y = origin_y + column.base.y * scale
                    
                    # Get column size from material
                    frame_mat = self.material_library.get_frame_material(column.frame_material)
                    if frame_mat:
                        radius = max(frame_mat.width, frame_mat.height) / 2 * scale / 1000
                        self._add_circle(dxf, x, y, radius, "COLUMNS")
                        
        # Draw roof outline
        roofs = bim.main.get_roofs()
        if roofs:
            # Simplified roof outline
            min_x = min_y = float('inf')
            max_x = max_y = float('-inf')
            
            for side in sides:
                if hasattr(side, 'columns'):
                    for column in side.columns:
                        min_x = min(min_x, column.base.x)
                        max_x = max(max_x, column.base.x)
                        min_y = min(min_y, column.base.y)
                        max_y = max(max_y, column.base.y)
                        
            # Draw roof rectangle
            if min_x != float('inf'):
                overhang = 600  # 600mm overhang
                min_x -= overhang / 1000
                max_x += overhang / 1000
                min_y -= overhang / 1000
                max_y += overhang / 1000
                
                self._add_rectangle(dxf, 
                                  origin_x + min_x * scale,
                                  origin_y + min_y * scale,
                                  origin_x + max_x * scale,
                                  origin_y + max_y * scale,
                                  "ROOF")
                                  
    def _draw_front_view(self, dxf: List[str], bim: ShedBim, origin_x: float, origin_y: float, scale: float):
        """Draw front elevation view."""
        # Get front side columns
        sides = bim.main.get_sides()
        if not sides:
            return
            
        front_side = sides[0]  # Assume first side is front
        
        # Draw columns
        if hasattr(front_side, 'columns'):
            for column in front_side.columns:
                x1 = origin_x + column.base.x * scale
                y1 = origin_y + column.base.z * scale
                x2 = origin_x + column.top.x * scale
                y2 = origin_y + column.top.z * scale
                
                # Draw column as rectangle
                frame_mat = self.material_library.get_frame_material(column.frame_material)
                if frame_mat:
                    width = frame_mat.width * scale / 1000 / 2
                    self._add_line(dxf, x1 - width, y1, x1 - width, y2, "COLUMNS")
                    self._add_line(dxf, x1 + width, y1, x1 + width, y2, "COLUMNS")
                    self._add_line(dxf, x1 - width, y1, x1 + width, y1, "COLUMNS")
                    self._add_line(dxf, x1 - width, y2, x1 + width, y2, "COLUMNS")
                    
        # Draw rafters if visible
        roofs = bim.main.get_roofs()
        for roof in roofs:
            if hasattr(roof, 'rafters'):
                for rafter in roof.rafters:
                    # Only draw if rafter is roughly perpendicular to view
                    if abs(rafter.start.y - rafter.end.y) < 100:
                        x1 = origin_x + rafter.start.x * scale
                        y1 = origin_y + rafter.start.z * scale
                        x2 = origin_x + rafter.end.x * scale
                        y2 = origin_y + rafter.end.z * scale
                        self._add_line(dxf, x1, y1, x2, y2, "RAFTERS")
                        
    def _draw_side_view(self, dxf: List[str], bim: ShedBim, origin_x: float, origin_y: float, scale: float):
        """Draw side elevation view."""
        # Similar to front view but from side perspective
        sides = bim.main.get_sides()
        if len(sides) > 1:
            side_view = sides[1]  # Use second side
        else:
            side_view = sides[0]
            
        # Draw columns from side
        if hasattr(side_view, 'columns'):
            for column in side_view.columns:
                x1 = origin_x + column.base.y * scale
                y1 = origin_y + column.base.z * scale
                x2 = origin_x + column.top.y * scale  
                y2 = origin_y + column.top.z * scale
                
                frame_mat = self.material_library.get_frame_material(column.frame_material)
                if frame_mat:
                    width = frame_mat.height * scale / 1000 / 2
                    self._add_line(dxf, x1 - width, y1, x1 - width, y2, "COLUMNS")
                    self._add_line(dxf, x1 + width, y1, x1 + width, y2, "COLUMNS")
                    
    def _draw_3d_view(self, dxf: List[str], bim: ShedBim, origin_x: float, origin_y: float, scale: float):
        """Draw isometric 3D view."""
        # Create isometric transformation
        # 30-degree rotation around vertical axis
        angle = math.radians(30)
        cos_a = math.cos(angle)
        sin_a = math.sin(angle)
        
        # Draw all columns
        sides = bim.main.get_sides()
        for side in sides:
            if hasattr(side, 'columns'):
                for column in side.columns:
                    # Transform points
                    x1, y1 = self._isometric_transform(column.base, cos_a, sin_a)
                    x2, y2 = self._isometric_transform(column.top, cos_a, sin_a)
                    
                    self._add_line(dxf,
                                 origin_x + x1 * scale,
                                 origin_y + y1 * scale,
                                 origin_x + x2 * scale,
                                 origin_y + y2 * scale,
                                 "COLUMNS")
                                 
    def _isometric_transform(self, point: Vec3, cos_a: float, sin_a: float) -> Tuple[float, float]:
        """Transform 3D point to isometric 2D coordinates."""
        # Rotate around Z axis
        x_rot = point.x * cos_a - point.y * sin_a
        y_rot = point.x * sin_a + point.y * cos_a
        
        # Project to 2D with Z contributing to Y
        x_2d = x_rot - y_rot * 0.5
        y_2d = point.z + y_rot * 0.5
        
        return x_2d, y_2d
        
    def _add_line(self, dxf: List[str], x1: float, y1: float, x2: float, y2: float, layer: str):
        """Add a line entity to DXF."""
        dxf.extend([
            "0", "LINE",
            "8", layer,
            "10", str(x1),
            "20", str(y1),
            "30", "0.0",
            "11", str(x2),
            "21", str(y2),
            "31", "0.0"
        ])
        
    def _add_circle(self, dxf: List[str], x: float, y: float, radius: float, layer: str):
        """Add a circle entity to DXF."""
        dxf.extend([
            "0", "CIRCLE",
            "8", layer,
            "10", str(x),
            "20", str(y),
            "30", "0.0",
            "40", str(radius)
        ])
        
    def _add_rectangle(self, dxf: List[str], x1: float, y1: float, x2: float, y2: float, layer: str):
        """Add a rectangle using polyline."""
        dxf.extend([
            "0", "LWPOLYLINE",
            "8", layer,
            "90", "4",  # Number of vertices
            "70", "1",  # Closed polyline
            "10", str(x1), "20", str(y1),
            "10", str(x2), "20", str(y1),
            "10", str(x2), "20", str(y2),
            "10", str(x1), "20", str(y2)
        ])
        
    def _add_text(self, dxf: List[str], text: str, x: float, y: float, height: float, layer: str):
        """Add text entity to DXF."""
        dxf.extend([
            "0", "TEXT",
            "8", layer,
            "10", str(x),
            "20", str(y),
            "30", "0.0",
            "40", str(height),
            "1", text,
            "7", "STANDARD"
        ])
        
    def _write_footer(self, dxf: List[str]):
        """Write DXF footer."""
        dxf.extend(["0", "EOF"])