# Accuracy Tests for BIM Backend Python/C# Alignment

## Purpose
These tests verify that the Python implementation produces **exactly** the same numerical results as the C# implementation for real-world scenarios. Unlike unit tests that verify individual methods work correctly, these accuracy tests ensure the entire system produces identical outputs.

## Test Structure

```
accuracy_tests/
├── reference_data/          # C# generated reference outputs
│   ├── geometry/           # Geometry calculation results from C#
│   └── materials/          # Material processing results from C#
├── generators/             # C# code to generate reference data
│   ├── GeometryRefGen.cs  # C# geometry reference generator
│   └── MaterialRefGen.cs  # C# material reference generator
├── test_geometry_accuracy.py
├── test_material_accuracy.py
└── reports/               # Accuracy comparison reports
```

## Test Scenarios

### Geometry Accuracy Tests
1. **Complex 3D Transformations**
   - Chain of rotations, translations, and scales
   - Matrix multiplication sequences
   - Quaternion interpolations

2. **Intersection Calculations**
   - Line-line intersections in 2D/3D
   - Ray-plane intersections
   - Box-box overlap tests

3. **Geometric Algorithms**
   - Polygon area calculations
   - Convex hull generation
   - Triangle mesh operations

4. **Real Building Geometry**
   - Carport frame transformations
   - Roof slope calculations
   - Structural member positioning

### Material Accuracy Tests
1. **Material Property Calculations**
   - Frame section properties (area, moment of inertia)
   - Material weight calculations
   - Strength validations

2. **Profile Generation**
   - Mesh profile point generation
   - Cladding profile shapes
   - Punching patterns

3. **Material Segmentation**
   - Roof sheet optimization
   - Wall cladding layouts
   - Material quantity calculations

4. **Real Building Materials**
   - Complete material list for a carport
   - Bill of materials generation
   - Cost calculations

## Validation Criteria

### Numerical Accuracy
- Floating point values must match to 10 decimal places
- Cumulative error tracking for chained operations
- Statistical analysis of differences

### Structural Accuracy
- Same number of output elements
- Identical ordering of results
- Matching data structures

### Performance Metrics
- Execution time comparison
- Memory usage analysis
- Scalability testing