# BIM Backend Codebase Quick Reference

## 🏗️ Architecture Overview

```
BIM Backend (Python)
├── Core Geometry Layer (Foundation)
├── Materials System (Component Definitions)  
├── BIM Data Model (3D Structure Representation)
├── Business Logic (Construction Algorithms)
├── API Layer (REST Endpoints)
├── Services (Encryption, Engineering)
└── Output Generation (GLTF, DXF, IFC)
```

## 📁 Directory Structure

```
PyModel/
├── src/
│   ├── geometry/          # Mathematical primitives
│   │   ├── primitives.py  # Vec2, Vec3
│   │   ├── matrix.py      # Mat4 transformations
│   │   ├── lines.py       # Line1, Line2, Line3
│   │   ├── boxes.py       # Box2, Box3, Rect
│   │   ├── plane.py       # Plane3
│   │   └── helpers.py     # Utility functions
│   │
│   ├── materials/         # Physical components
│   │   ├── base.py        # Material definitions
│   │   ├── profiles.py    # Cross-section profiles
│   │   ├── mesh.py        # 3D mesh data
│   │   └── helpers.py     # Material utilities
│   │
│   ├── bim/              # Building Information Model
│   │   ├── shed_bim.py   # Main BIM structure
│   │   ├── components.py # Structural elements
│   │   ├── wall_roof.py  # Wall and roof systems
│   │   └── accessories.py # Additional components
│   │
│   ├── business/         # Business logic
│   │   ├── building_input.py    # Input validation
│   │   ├── structure_builder.py # 14-step pipeline
│   │   └── engineering.py       # External integration
│   │
│   ├── api/              # REST API
│   │   ├── main.py       # FastAPI app
│   │   ├── endpoints.py  # Route handlers
│   │   └── models.py     # Request/response models
│   │
│   ├── services/         # Shared services
│   │   ├── encryption.py # AES encryption
│   │   └── output_service.py # File generation
│   │
│   └── output/           # File generators
│       ├── gltf/         # 3D model export
│       ├── dxf/          # CAD drawing export
│       └── ifc/          # BIM exchange format
│
├── tests/                # Test suite
├── docs/                 # Documentation
└── scripts/              # Utility scripts
```

## 🔑 Key Classes and Their Responsibilities

### Geometry Layer
| Class | Purpose | Key Methods |
|-------|---------|-------------|
| `Vec2` | 2D vector operations | `length()`, `dot()`, `inters()` |
| `Vec3` | 3D vector operations | `cross()`, `distance()`, `normal()` |
| `Mat4` | 4x4 transformation matrix | `transform_position()`, `get_inverse()` |
| `Line2/3` | Linear geometry | `intersect()`, `distance_to_point()` |
| `Box2/3` | Bounding boxes | `contains()`, `intersects()` |

### Materials System
| Class | Purpose | Example |
|-------|---------|---------|
| `FrameMaterial` | Structural steel profiles | C-channel, SHS, Z-section |
| `BracketMaterial` | Connection brackets | Standard brackets with mesh |
| `CladdingMaterial` | Roof/wall sheeting | Corrugated profiles |
| `FootingMaterial` | Foundation types | Concrete block or bored |

### BIM Model
| Class | Purpose | Relationships |
|-------|---------|---------------|
| `ShedBim` | Root container | Contains all building parts |
| `ShedBimPartMain` | Main structure | Has sides, roofs, frames |
| `ShedBimColumn` | Vertical members | Part of sides |
| `ShedBimRafter` | Roof beams | Part of roofs |
| `ShedBimPurlin` | Horizontal supports | Spans between rafters |

### Business Logic
| Class | Purpose | Key Methods |
|-------|---------|-------------|
| `BuildingInput` | User parameters | `validate()` |
| `CarportBuilder` | Factory pattern | `create_carport()` |
| `StructureBuilder` | Base builder | 14-step pipeline |

## 🔄 Data Flow

```
1. User Input (JSON)
   ↓
2. Encryption (AES-256)
   ↓
3. API Endpoint (/api/carport/create)
   ↓
4. Decryption & Validation
   ↓
5. Engineering Check (Optional)
   ↓
6. CarportBuilder (14 steps)
   ↓
7. ShedBim Model
   ↓
8. Output Generator (GLTF/DXF/IFC)
   ↓
9. File Response
```

## 🏗️ 14-Step Construction Pipeline

1. **Initialize BIM** - Create base structure
2. **Find Slab** - Ground foundation
3. **Find Brackets** - Connection points
4. **Find Columns & Footings** - Vertical structure
5. **Find Rafters** - Primary roof beams
6. **Find Roof Cladding** - Sheeting layout
7. **Find Purlins** - Secondary roof supports
8. **Find Eave Purlins** - Edge supports
9. **Find Wall Structure** - For attached types
10. **Find Flashings** - Weatherproofing
11. **Find Gutters & Downpipes** - Drainage
12. **Find Braces** - Structural stability
13. **Find Punchings** - Bolt holes
14. **Find Fasteners** - Screws and bolts

## 🔧 Common Operations

### Create a Vector
```python
from src.geometry.primitives import Vec3

point = Vec3(1000, 2000, 3000)  # mm units
```

### Create a Material
```python
from src.materials.base import FrameMaterial, FrameMaterialType

c_channel = FrameMaterial(
    name="C10015",
    material_type=FrameMaterialType.C,
    width=102,
    height=51,
    thickness=1.5
)
```

### Generate a Carport
```python
from src.business.building_input import BuildingInput
from src.business.structure_builder import CarportBuilder

input_data = BuildingInput(
    span=6000,
    length=6000,
    height=2700
)

carport = CarportBuilder.create_carport(input_data)
```

### Export to GLTF
```python
from src.output.gltf.gltf_generator import GLTFGenerator

generator = GLTFGenerator()
gltf_data = generator.generate(carport)
```

## 📊 Important Constants

| Constant | Value | Usage |
|----------|-------|--------|
| MIN_SPAN | 3000mm | Minimum carport width |
| MAX_SPAN | 12000mm | Maximum carport width |
| MIN_HEIGHT | 2100mm | Minimum eave height |
| DEFAULT_BAY | 3000mm | Standard bay spacing |
| WIND_SPEEDS | [28, 32, 36, 41] | Design wind speeds |

## 🔍 Debugging Tips

### Import Issues
```python
# Add to top of file
import sys
sys.path.insert(0, 'path/to/src')
```

### Check Material Properties
```python
print(f"Web: {material.web}, Flange: {material.flange}")
print(f"Profile: {material.get_profile_points()}")
```

### Validate Structure
```python
assert len(carport.main.side_left.columns) > 0
assert carport.main.roof_left is not None
```

### Debug Output Generation
```python
# Enable verbose logging
generator = GLTFGenerator(verbose=True)
```

## 🚨 Common Pitfalls

1. **Import Errors**: Use absolute imports from `src`
2. **Missing Methods**: Check if method exists before calling
3. **Unit Confusion**: All dimensions in millimeters
4. **Angle Units**: Radians for calculations, degrees for input
5. **Material Names**: Must match engineering database

## 📝 Testing Checklist

- [ ] All geometry operations tested
- [ ] Material validation working
- [ ] BIM model integrity maintained
- [ ] API encryption/decryption verified
- [ ] Output files valid
- [ ] Performance acceptable
- [ ] Error handling comprehensive

## 🔗 Related Documentation

- [CLAUDE.md](CLAUDE.md) - Conversion guide
- [CRITICAL_REVIEW_TASKS_1_5.md](CRITICAL_REVIEW_TASKS_1_5.md) - Task analysis
- [ARCHITECTURE_DOCUMENTATION.md](ARCHITECTURE_DOCUMENTATION.md) - Detailed architecture
- [SESSION_TESTING_GUIDE.md](SESSION_TESTING_GUIDE.md) - Testing procedures
- [DOCUMENTATION_INDEX.md](DOCUMENTATION_INDEX.md) - All 48 MD files

## 💡 Quick Fixes

### Fix Import Error
```python
# Instead of:
from ..geometry import Vec3

# Use:
from src.geometry.primitives import Vec3
```

### Fix Missing Method
```python
# Add to Vec2 class:
def add(self, other: 'Vec2') -> 'Vec2':
    return Vec2(self.x + other.x, self.y + other.y)
```

### Fix Profile Generation
```python
def get_profile_points(self) -> List[Vec2]:
    if self.material_type == FrameMaterialType.C:
        return self._generate_c_profile()
    # Add other types...
```

This quick reference provides immediate access to the most important information about the codebase structure and common operations.