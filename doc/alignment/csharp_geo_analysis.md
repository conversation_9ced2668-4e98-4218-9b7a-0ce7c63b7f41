# Comprehensive Analysis of C# Geo.cs and Mat4.cs

## 1. Overview
The C# geometry library consists of two main files:
- `Geo.cs`: Contains geometric primitives (Vec2, Vec3, Line1/2/3, Box2/3, Plane3, Basis3, TriIndex) and static utility methods
- `Mat4.cs`: Contains 4x4 transformation matrix implementation

## 2. Static Class: Geo

### 2.1 Constants
```csharp
public const double Deg0 = 0;
public const double Deg45 = Math.PI / 4;
public const double Deg90 = Math.PI / 2;
public const double Deg180 = Math.PI;
public const double Deg270 = Math.PI + Math.PI / 2;
public const double Deg360 = Math.PI * 2;
public const double ToDeg = 180 / Math.PI;
public const double ToRad = Math.PI / 180;
```

### 2.2 Static Methods

#### Angle Operations
- `NormalizeAngle(double angle)`: Normalizes angle to [0, 2π) range
- `MirrorAngle(double angle)`: Returns angle + π (normalized)

#### Vector Creation Shortcuts
- `V2(double x, double y)`: Creates Vec2
- `V2xy(Vec3 a)`: Extracts XY from Vec3
- `V2xz(Vec3 a)`: Extracts XZ from Vec3
- `V2yz(Vec3 a)`: Extracts YZ from Vec3
- `V3(double x, double y, double z)`: Creates Vec3
- `Vx(double x)`: Creates Vec3(x, 0, 0)
- `Vy(double y)`: Creates Vec3(0, y, 0)
- `Vz(double z)`: Creates Vec3(0, 0, z)

#### Line Creation
- `Ln1(double start, double end)`: Creates Line1
- `Ln2(double startX, double startY, double endX, double endY)`: Creates Line2
- `Ln2(Vec2 start, Vec2 end)`: Creates Line2
- `Ln2xy(Line3 line)`: Projects Line3 to XY plane
- `Ln2xz(Line3 line)`: Projects Line3 to XZ plane
- `Ln2yz(Line3 line)`: Projects Line3 to YZ plane
- `Ln3(double x1, double y1, double z1, double x2, double y2, double z2)`: Creates Line3
- `Ln3(Vec3 start, Vec3 end)`: Creates Line3

#### Trigonometry Helpers
- `TrigSoh(double angle, double o)`: Returns hypotenuse from opposite
- `TrigSho(double angle, double h)`: Returns opposite from hypotenuse
- `TrigCah(double angle, double a)`: Returns hypotenuse from adjacent
- `TrigCha(double angle, double h)`: Returns adjacent from hypotenuse
- `TrigToa(double angle, double o)`: Returns adjacent from opposite
- `TrigTao(double angle, double a)`: Returns opposite from adjacent

#### Geometric Operations
- `Polar(double ang, double dist)`: Creates Vec2 from polar coordinates
- `Rotate(double ang, Vec2 v)`: Rotates Vec2 by angle
- `Mid(double a, double b)`: Returns midpoint
- `Mid(Vec2 a, Vec2 b)`: Returns midpoint
- `Mid(Vec3 a, Vec3 b)`: Returns midpoint
- `Mid(Line1/2/3 line)`: Returns midpoint of line
- `Interpolate(Line1/2/3 line, double scale)`: Linear interpolation along line
- `Round(Vec2/Vec3 a, int decimals = 0)`: Rounds coordinates
- `Round(Line2/3 a, int decimals = 0)`: Rounds line endpoints

#### Line Operations
- `LnSwap(Line1/2/3 a)`: Swaps start and end
- `LnExtend(Line2 line, Line2 test)`: Extends line to intersection
- `LnExtend(Vec2/Vec3 start, Vec2/Vec3 end, double startExtend, double endExtend)`: Extends line by distances
- `LnUp(Vec2 start, Vec2 end, double distance)`: Offsets line upward
- `LnDown(Vec2 start, Vec2 end, double distance)`: Offsets line downward
- `LnOffset(Vec2 start, Vec2 end, double distance)`: Offsets line perpendicular
- `LnOffset(IList<Vec2> line, double distance)`: Offsets polyline
- `LnLowHigh(Line2 line)`: Orders points by Y (then X)
- `LnLeftRight(Line2 line)`: Orders points by X (then Y)
- `LnRange(double middle, double size)`: Creates Line1 from center and size

#### Polygon Operations
- `PolyOffset(List<Vec2> poly, double distance)`: Offsets closed polygon

#### Utility Methods
- `WrapIndex<T>(ICollection<T> list, int index)`: Wraps index for circular access
- `MirrorVecX(double baseX, Vec2 v)`: Mirrors Vec2 across vertical line
- `GetExtents(IEnumerable<Vec2> verts, double extend = 0.0)`: Returns bounding box
- `GetOffsets(IEnumerable<double> bays, double startPos = 0.0, int dir = +1)`: Cumulative offsets
- `GetBaySizes(double length, int numBays, double precision = 10)`: Divides length into bays
- `BoxLineInters(Box2 box, Line2 line)`: Clips line to box
- `GetNearFar(Vec3 basePt, Vec3 pt1, Vec3 pt2)`: Orders points by distance

## 3. Structs and Classes

### 3.1 Vec2 (struct, IEquatable<Vec2>)
**Properties:**
- `double X { get; set; }`
- `double Y { get; set; }`

**Methods:**
- `Length()`: Returns magnitude
- `LengthSquared()`: Returns squared magnitude
- `Angle()`: Returns angle in radians (atan2)

**Static Members:**
- `Origin`: (0, 0)
- `UnitX`: (1, 0)
- `UnitY`: (0, 1)
- `MinValue`: (double.MinValue, double.MinValue)
- `MaxValue`: (double.MaxValue, double.MaxValue)
- `Dot(Vec2 a, Vec2 b)`: Dot product
- `Normal(Vec2 a)`: Normalized vector
- `Inters(Vec2 a1, Vec2 a2, Vec2 b1, Vec2 b2, bool infinite = false)`: Line intersection
- `IntersMust(...)`: Throws if no intersection
- `IntersList(...)`: Intersection with polyline
- `IntersListMust(...)`: Throws if no intersection
- `Min(Vec2 a, Vec2 b)`: Component-wise minimum
- `Max(Vec2 a, Vec2 b)`: Component-wise maximum

**Operators:**
- `+Vec2`: Unary plus
- `Vec2 + Vec2`: Addition
- `-Vec2`: Negation
- `Vec2 - Vec2`: Subtraction
- `Vec2 * double`: Scalar multiplication
- `double * Vec2`: Scalar multiplication
- `Vec2 / double`: Scalar division
- `==`, `!=`: Equality

### 3.2 Vec3 (struct, IEquatable<Vec3>)
**Properties:**
- `double X { get; set; }`
- `double Y { get; set; }`
- `double Z { get; set; }`

**Methods:**
- `Length()`: Returns magnitude
- `LengthSquared()`: Returns squared magnitude
- `DistanceSquared(Vec3 other)`: Returns float (not double!)
- `ToArray()`: Returns double[3]

**Static Members:**
- `Origin`: (0, 0, 0)
- `UnitX`: (1, 0, 0)
- `UnitY`: (0, 1, 0)
- `UnitZ`: (0, 0, 1)
- `MinValue`: (double.MinValue, double.MinValue, double.MinValue)
- `MaxValue`: (double.MaxValue, double.MaxValue, double.MaxValue)
- `Normal(Vec3 a)`: Normalized vector
- `Dot(Vec3 a, Vec3 b)`: Dot product
- `Cross(Vec3 a, Vec3 b)`: Cross product
- `Min(Vec3 a, Vec3 b)`: Component-wise minimum
- `Max(Vec3 a, Vec3 b)`: Component-wise maximum
- `Distance(Vec3 a, Vec3 b)`: Euclidean distance
- `Midpoint(Vec3 a, Vec3 b)`: Midpoint

**Operators:** Same as Vec2 but for 3D

### 3.3 Line1 (struct, IEquatable<Line1>)
**Properties:**
- `double Start { get; set; }`
- `double End { get; set; }`

**Methods:**
- `Length()`: Returns |End - Start|
- `Contains(double value)`: Checks if value is in range

**Static Methods:**
- `FromCenter(double center, double size)`: Creates from center and length

### 3.4 Line2 (struct, IEquatable<Line2>)
**Properties:**
- `Vec2 Start { get; set; }`
- `Vec2 End { get; set; }`

**Methods:**
- `Length()`: Returns End - Start (Vec2)
- `Direction()`: Returns normalized direction
- `Magnitude()`: Returns scalar length

**Static Methods:**
- `Inters(Line2 a, Line2 b, bool infinite = false)`: Line intersection
- `IntersMust(...)`: Throws if no intersection
- `FromStart(Vec2 position, Vec2 direction)`: Creates from point and direction
- `FromCenter(Vec2 center, Vec2 length)`: Creates from center and vector

**Operators:**
- `Line2 + Vec2`: Translates line
- `Line2 - Vec2`: Translates line
- `==`, `!=`: Equality

### 3.5 Line3 (struct, IEquatable<Line3>)
**Properties:**
- `Vec3 Start { get; set; }`
- `Vec3 End { get; set; }`

**Methods:**
- `Length()`: Returns End - Start (Vec3)
- `Direction()`: Returns normalized direction
- `Magnitude()`: Returns scalar length

**Operators:**
- `Line3 + Vec3`: Translates line
- `Line3 - Vec3`: Translates line
- `==`, `!=`: Equality

### 3.6 Box2 (struct, IEquatable<Box2>)
**Properties:**
- `Vec2 Min { get; set; }`
- `Vec2 Max { get; set; }`

**Methods:**
- `Size()`: Returns Max - Min
- `Middle()`: Returns center point
- `BottomLeft()`, `BottomRight()`, `TopLeft()`, `TopRight()`: Corner accessors
- `Bottom()`, `Top()`, `Left()`, `Right()`: Edge coordinate accessors
- `Contains(Vec2 position)`: Point containment test
- `Intersects(Box2 box)`: Box intersection test
- `Union(Box2 box)`: Returns bounding box of both

**Static Methods:**
- `FromPositionAndSize(Vec2 position, Vec2 size)`
- `FromCenter(Vec2 center, Vec2 size)`
- `FromBottomCenter(Vec2 bottomCenter, Vec2 size)`
- `FromCorners(double ax, double ay, double bx, double by)`
- `FromCorners(Vec2 a, Vec2 b)`
- `FromList(IEnumerable<Vec2> vecs)`: Bounding box of points

### 3.7 Box3 (struct, IEquatable<Box3>)
Similar to Box2 but for 3D:
- Same pattern of properties and methods
- `FromCenter` and `FromList` static methods

### 3.8 Plane3 (struct, IEquatable<Plane3>)
**Properties:**
- `Vec3 Normal { get; set; }`: Normal vector
- `double D { get; set; }`: Distance from origin

**Constructors:**
- `Plane3(double nx, double ny, double nz, double d)`
- `Plane3(Vec3 normal, double d)`
- `Plane3(Vec3 p1, Vec3 p2, Vec3 p3)`: From three points
- `Plane3(Vec3 normal, Vec3 p)`: From normal and point

**Static Methods:**
- `DotV(Plane3 f, Vec3 v)`: Dot product with vector
- `DotP(Plane3 f, Vec3 p)`: Dot product with point (includes D)
- `Inters(Line3 l, Plane3 f, bool infinite = false)`: Line-plane intersection
- `Inters(Vec3 p, Vec3 v, Plane3 f, bool infinite = false)`: Ray-plane intersection
- `Inters(Plane3 f1, Plane3 f2)`: Plane-plane intersection (returns line)

### 3.9 Basis3 (struct, IEquatable<Basis3>)
**Properties:**
- `Vec3 X { get; set; }`
- `Vec3 Y { get; set; }`
- `Vec3 Z { get; set; }`

**Static Members:**
- `UnitXYZ`: Standard basis
- `FromXY(Vec3 basisX, Vec3 basisY)`: Z = X × Y
- `FromXZ(Vec3 basisX, Vec3 basisZ)`: Y = Z × X
- `FromYZ(Vec3 basisY, Vec3 basisZ)`: X = Y × Z

### 3.10 TriIndex (struct, IEquatable<TriIndex>)
**Properties:**
- `int A { get; set; }`
- `int B { get; set; }`
- `int C { get; set; }`

Simple triangle index structure.

## 4. Mat4 Structure

### 4.1 Properties
16 double properties: M11-M14, M21-M24, M31-M34, M41-M44

### 4.2 Methods
- `GetTranslation()`: Returns Vec3(M14, M24, M34)
- `GetBasis()`: Returns Basis3 from rotation part

### 4.3 Static Members
- `Identity`: Identity matrix
- `Origin`: Vec3(0, 0, 0)
- `CreateRotationX/Y/Z(double ang)`: Rotation matrices
- `CreateTranslation(double x, double y, double z)` / `CreateTranslation(Vec3 v)`
- `CreateScale(double s)` / `CreateScale(double sx, double sy, double sz)` / `CreateScale(Vec3 s)`
- `CreateBasis(Vec3 x, Vec3 y, Vec3 z)` / `CreateBasis(Basis3 basis)`
- `CreateTransform(Basis3 basis, Vec3 pos)` / `CreateTransform(Vec3 basisX, Vec3 basisY, Vec3 basisZ, Vec3 pos)`
- `GetInverse(Mat4 m)`: Matrix inversion (uses cross products)
- `TransformPosition(Mat4 m, Vec3 p)`: Full transformation (rotation + translation)
- `TransformVector(Mat4 m, Vec3 v)`: Rotation only
- `TransformNormal(Vec3 n, Mat4 m)`: Normal transformation (transposed rotation)

### 4.4 Operators
- `Mat4 * Mat4`: Matrix multiplication
- `Mat4 * Vec3`: Same as TransformPosition
- `==`, `!=`: Equality

## 5. Helper Classes (Internal)

### 5.1 DebuggerDisplayHelper
- `public const int Precision = 5`: Used for debug display rounding

### 5.2 HashCodeHelper
- `CombineHashCodes(int h1, int h2)`: Hash combination using `(((h1 << 5) + h1) ^ h2)`

## 6. Special Algorithms and Optimizations

### 6.1 Line Intersection (Vec2.Inters)
- Uses parametric line equations
- Handles infinite lines with boolean flag
- Returns null for parallel lines
- Efficient implementation without matrix inversion

### 6.2 GetBaySizes Algorithm
- Distributes length into equal bays
- Rounds to specified precision (1, 10, 100, 1000)
- First bay gets remainder to maintain total length
- Example: 10000mm / 3 bays @ 10mm precision = [3340, 3330, 3330]

### 6.3 Polygon Offset (PolyOffset)
- Handles closed polygons
- Uses line intersection to find new vertices
- Circular indexing for wraparound

### 6.4 Matrix Inverse (Mat4.GetInverse)
- Uses optimized algorithm from FGED1 (Foundations of Game Engine Development)
- Avoids full matrix inversion using cross products
- More efficient than standard Gauss-Jordan elimination

### 6.5 Plane-Line Intersection
- Based on FGED1 algorithms
- Handles both finite segments and infinite lines
- Uses dot products for efficiency

## 7. Edge Cases and Special Handling

### 7.1 Angle Normalization
- Handles negative angles by adding 2π
- Handles angles ≥ 2π by subtracting 2π
- Uses while loops for extreme cases

### 7.2 Division by Zero
- Line intersection checks for zero denominator
- Plane intersection checks for near-zero dot products
- Normal() methods divide by length (no explicit zero check)

### 7.3 GetBaySizes Validation
- Throws ArgumentOutOfRangeException for:
  - length ≤ 0
  - numBays ≤ 0
  - Invalid precision values

### 7.4 Intersection Methods
- Return nullable types for optional results
- "Must" variants throw exceptions with detailed messages
- Include intersection parameters in error messages

## 8. C#-Specific Features Requiring Python Attention

### 8.1 Value Types (structs)
- All geometric types are structs (value semantics)
- Python classes will need careful equality implementation
- Consider using `@dataclass` with `frozen=True` for immutability

### 8.2 Properties with Getters/Setters
- C# uses `{ get; set; }` syntax
- Python needs `@property` decorators or direct attributes

### 8.3 Operator Overloading
- C# has explicit operator methods
- Python uses magic methods (`__add__`, `__mul__`, etc.)

### 8.4 IEquatable Interface
- Explicit interface implementation in C#
- Python needs `__eq__` and `__hash__` methods

### 8.5 Nullable Types
- C# uses `Vec2?` for nullable structs
- Python can use `Optional[Vec2]` or `Vec2 | None`

### 8.6 Method Overloading
- C# supports multiple methods with same name
- Python needs default parameters or separate names

### 8.7 DebuggerDisplay Attribute
- C# uses `[DebuggerDisplay]` for debugging
- Python can use `__repr__` for similar functionality

### 8.8 Extension Methods Pattern
- Static methods in Geo class act like extensions
- Python can use standalone functions or class methods

## 9. Performance Considerations

### 9.1 Struct Usage
- C# structs avoid heap allocation
- Python may need optimization for large datasets

### 9.2 LengthSquared Methods
- Avoid square root calculation when possible
- Used for distance comparisons

### 9.3 Inline Calculations
- Many methods use direct calculations without temporary variables
- Matrix multiplication fully unrolled

### 9.4 Special Case: DistanceSquared returns float
- Vec3.DistanceSquared returns float instead of double
- Possible optimization for graphics/game engines

## 10. Testing Insights

From GeoTests.cs:
- GetBaySizes is thoroughly tested with various inputs
- Plane-line intersection tested for both finite and infinite cases
- Test data uses precise values (3334, 3333, 3333 for 10000/3)

## 11. External Dependencies

- `System.Diagnostics` for DebuggerDisplay
- `Csg` namespace (likely for CSG operations)
- No complex math library dependencies
- Uses standard `System.Math` for trigonometry

## 12. Recommended Python Implementation Strategy

1. Create base module structure:
   - `geo.py` for geometric primitives and utilities
   - `mat4.py` for matrix operations
   - Use type hints throughout

2. Implement value semantics:
   - Use `@dataclass(frozen=True)` for immutable types
   - Implement proper `__hash__` methods
   - Consider `slots` for memory efficiency

3. Handle method overloading:
   - Use default parameters
   - Consider `@overload` decorators for type hints
   - Create factory classmethods for different constructors

4. Optimize critical paths:
   - Consider NumPy for heavy calculations
   - Cache computed properties (length, normalized vectors)
   - Use `__slots__` to reduce memory overhead

5. Maintain compatibility:
   - Keep same method names and signatures
   - Preserve algorithm implementations
   - Match precision and rounding behavior