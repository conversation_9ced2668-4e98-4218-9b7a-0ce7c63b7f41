# .NET to Python Conversion Reference Guide

## Overview

This guide serves as a comprehensive reference for converting the BIM Backend system from .NET (C#) to Python. It documents the conversion patterns, naming conventions, and architectural decisions made during the port.

## Source Code Mapping

### Directory Structure Comparison

```
.NET Project Structure              Python Project Structure
BimBackend-master/                  PyModel/
├── Shedkit.Bim.Shed/              ├── src/
│   ├── Geo.cs                     │   └── geometry/
│   ├── Mat4.cs                    │       ├── primitives.py  (Vec2, Vec3)
│   └── ...                        │       ├── matrix.py      (Mat4)
├── BimCoreLibrary/                │       ├── lines.py       (Line1/2/3)
├── Shedkit.Output.*/              │       ├── boxes.py       (Box2/3)
└── Shedkit.Tests/                 │       ├── plane.py       (Plane3)
                                   │       ├── basis.py       (Basis3)
                                   │       ├── triangle.py    (TriIndex)
                                   │       └── helpers.py     (Geo static)
                                   └── tests/
                                       └── geometry/
```

## Naming Convention Mappings

### Type Names
| C# Type | Python Type | Notes |
|---------|-------------|-------|
| `double` | `float` | Python's float is 64-bit like C# double |
| `int` | `int` | Python int has arbitrary precision |
| `string` | `str` | |
| `bool` | `bool` | |
| `List<T>` | `list[T]` | Using Python 3.9+ type hints |
| `IEnumerable<T>` | `Iterable[T]` | From typing module |
| `T?` (nullable) | `Optional[T]` | From typing module |
| `(T1, T2)` tuple | `tuple[T1, T2]` | |

### Method/Property Names
| C# Convention | Python Convention | Example |
|---------------|-------------------|---------|
| PascalCase methods | snake_case | `Length()` → `length()` |
| PascalCase properties | snake_case | `X` → `x` |
| SCREAMING_SNAKE constants | UPPER_SNAKE | Same convention |
| Private fields with _ | Private with _ | `_field` → `_field` |

### Common Patterns

#### C# Property → Python Property
```csharp
// C#
public double X { get; set; }
```
```python
# Python (using dataclass)
x: float
```

#### C# Static Method → Python Static Method
```csharp
// C#
public static Vec2 Dot(Vec2 a, Vec2 b) { ... }
```
```python
# Python
@staticmethod
def dot(a: Vec2, b: Vec2) -> float: ...
```

#### C# Constructor Overloads → Python Factory Methods
```csharp
// C#
public Line2(Vec2 start, Vec2 end) { ... }
public static Line2 FromCenter(Vec2 center, Vec2 size) { ... }
```
```python
# Python
def __init__(self, start: Vec2, end: Vec2): ...
@staticmethod
def from_center(center: Vec2, size: Vec2) -> Line2: ...
```

## Key Architectural Decisions

### 1. Immutability
- C# uses `struct` for value types → Python uses `@dataclass` with immutable fields
- Ensures value semantics are preserved

### 2. Operator Overloading
- All C# operators mapped to Python magic methods
- Example: `operator +` → `__add__`

### 3. Nullable Types
- C# `Vec2?` → Python `Optional[Vec2]`
- Explicit None checks required in Python

### 4. Exception Handling
- C# `InvalidOperationException` → Python `ValueError`
- C# `ArgumentOutOfRangeException` → Python `ValueError` with descriptive message

### 5. Numeric Precision
- C# `double.MaxValue` → Python `float('inf')`
- C# `double.MinValue` → Python `-float('inf')`
- Epsilon comparisons for floating-point equality

## Method-by-Method Conversion Guide

### Vec2 Methods

| C# Method | Python Method | Conversion Notes |
|-----------|---------------|------------------|
| `Length()` | `length()` | Direct port |
| `LengthSquared()` | `length_squared()` | Avoids sqrt |
| `Angle()` | `angle()` | Returns radians |
| `static Dot()` | `@staticmethod dot()` | |
| `static Normal()` | `@staticmethod normal()` | Handle zero vector |
| `static Inters()` | `@staticmethod inters()` | Returns Optional |
| `static IntersMust()` | `@staticmethod inters_must()` | Raises ValueError |

### Mat4 Methods

| C# Method | Python Method | Special Considerations |
|-----------|---------------|------------------------|
| Constructor with 16 params | `__init__` with defaults | Identity matrix by default |
| `CreateRotationX/Y/Z()` | `create_rotation_x/y/z()` | Angle in radians |
| `CreateTranslation()` | `create_translation()` | Overloaded versions |
| `GetInverse()` | `get_inverse()` | Based on FGED1 algorithm |
| `operator *` | `__mul__` | Matrix multiplication |

### Geometric Helpers (Geo class)

| C# Method | Python Method | Implementation Notes |
|-----------|---------------|---------------------|
| `GetBaySizes()` | `get_bay_sizes()` | Precision rounding logic |
| `NormalizeAngle()` | `normalize_angle()` | Range [0, 2π) |
| `PolyOffset()` | `poly_offset()` | Critical for wall thickness |
| `LnUp/LnDown()` | `ln_up/ln_down()` | Direction-aware offsetting |

## Testing Alignment

### Test Data Preservation
All test cases from C# are preserved with exact same inputs/outputs:
```csharp
// C# Test
[DataRow(10000, 3, 10, new[] { 3340, 3330, 3330 })]
```
```python
# Python Test
(10000, 3, 10, [3340, 3330, 3330])
```

### Assertion Mappings
| C# Assertion | Python Assertion |
|--------------|------------------|
| `Assert.AreEqual(a, b)` | `assert a == b` |
| `Assert.IsNull(x)` | `assert x is None` |
| `Assert.IsTrue(x)` | `assert x` |
| `CollectionAssert.AreEqual()` | `assert list1 == list2` |

## Performance Considerations

### NumPy Integration
Mat4 operations can use NumPy for performance:
```python
def to_numpy(self) -> np.ndarray:
    """Convert to NumPy array for batch operations."""
    return np.array([
        [self.m11, self.m12, self.m13, self.m14],
        [self.m21, self.m22, self.m23, self.m24],
        [self.m31, self.m32, self.m33, self.m34],
        [self.m41, self.m42, self.m43, self.m44]
    ])
```

### Caching Strategies
- Immutable objects can be cached
- Use `@lru_cache` for expensive computations
- Profile before optimizing

## Common Pitfalls and Solutions

### 1. Division by Zero
C# may throw exception, Python returns inf/nan:
```python
if length == 0:
    return Vec2(0, 0)  # Explicit handling
return Vec2(self.x / length, self.y / length)
```

### 2. Index vs Iterator
C# often uses indices, Python prefers iteration:
```python
# Instead of: for i in range(len(points)):
for i, point in enumerate(points):
    # Use both index and value
```

### 3. Reference vs Value Semantics
Dataclasses provide value semantics:
```python
@dataclass
class Vec2:
    x: float
    y: float
    # Automatically generates __eq__, __hash__, etc.
```

## Validation Checklist

Before considering a module converted:

- [ ] All methods from C# implemented
- [ ] All test cases ported and passing
- [ ] Type hints complete and accurate
- [ ] C# reference comments added
- [ ] Edge cases handled identically
- [ ] Performance acceptable
- [ ] Documentation complete

## Future Enhancements

### Python-Specific Improvements
1. Context managers for resource management
2. Generators for large datasets
3. Async operations where beneficial
4. Protocol classes for interfaces

### Maintaining Compatibility
1. Keep method signatures aligned
2. Preserve numeric precision
3. Match exception behavior
4. Maintain same coordinate systems

## Quick Reference Card

### Import Mappings
```python
# Instead of C# using statements:
from geometry import Vec2, Vec3, Mat4, Line2, Box2, Plane3
from geometry import Geo  # Static helper class
```

### Common Operations
```python
# Vector operations
v1 = Vec2(3, 4)
v2 = Vec2(1, 2)
dot_product = Vec2.dot(v1, v2)
normalized = Vec2.normal(v1)

# Matrix operations
m = Mat4.create_rotation_z(Geo.DEG45)
translated = Mat4.create_translation(10, 20, 30)
combined = translated * m  # Note: order matters!

# Geometric helpers
bays = Geo.get_bay_sizes(10000, 3, 10)
angle = Geo.normalize_angle(angle + Geo.DEG360)
```

This guide should be updated as the conversion progresses to capture new patterns and decisions.