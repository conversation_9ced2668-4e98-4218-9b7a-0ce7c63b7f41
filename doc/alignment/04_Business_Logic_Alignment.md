# Business Logic Alignment - C# to Python

This document details the alignment between C# and Python business logic implementations, focusing on the structure builders and engineering calculations.

## Table of Contents
1. [Builder Pattern Alignment](#builder-pattern-alignment)
2. [Structure Builder Implementation](#structure-builder-implementation)
3. [Engineering Calculations](#engineering-calculations)
4. [Business Rules](#business-rules)
5. [Building Factory](#building-factory)
6. [Validation Logic](#validation-logic)

## Builder Pattern Alignment

### C# Interface Structure (IStructureBuilder.cs)
```csharp
public interface IStructureBuilder {
    Building CreateBuilding(BuildingInput input);
    void AddStructure(Building building, StructureParameters parameters);
    void AddCladding(Building building, CladdingParameters parameters);
    void AddOpenings(Building building, List<Opening> openings);
    void AddAccessories(Building building, AccessoryParameters parameters);
    ValidationResult Validate(BuildingInput input);
}
```

### Python Abstract Base Class
```python
from abc import ABC, abstractmethod

class IStructureBuilder(ABC):
    """Structure builder interface.
    
    C# Reference: IStructureBuilder.cs interface
    """
    
    @abstractmethod
    def create_building(self, building_input: BuildingInput) -> Building:
        """C# Ref: Building CreateBuilding(BuildingInput input)"""
        pass
    
    @abstractmethod
    def add_structure(self, building: Building, parameters: StructureParameters):
        """C# Ref: void AddStructure(Building building, StructureParameters parameters)"""
        pass
    
    @abstractmethod
    def add_cladding(self, building: Building, parameters: CladdingParameters):
        """C# Ref: void AddCladding(Building building, CladdingParameters parameters)"""
        pass
    
    @abstractmethod
    def add_openings(self, building: Building, openings: List[Opening]):
        """C# Ref: void AddOpenings(Building building, List<Opening> openings)"""
        pass
    
    @abstractmethod
    def add_accessories(self, building: Building, parameters: AccessoryParameters):
        """C# Ref: void AddAccessories(Building building, AccessoryParameters parameters)"""
        pass
    
    @abstractmethod
    def validate(self, building_input: BuildingInput) -> ValidationResult:
        """C# Ref: ValidationResult Validate(BuildingInput input)"""
        pass
```

## Structure Builder Implementation

### C# CarportBuilder (CarportBuilder.cs)
```csharp
public class CarportBuilder : IStructureBuilder {
    private readonly EngineeringService _engineeringService;
    
    public CarportBuilder(EngineeringService engineeringService) {
        _engineeringService = engineeringService;
    }
    
    public Building CreateBuilding(BuildingInput input) {
        var building = new Building {
            Name = $"Carport_{input.Id}",
            Type = BuildingType.Carport,
            Width = input.Width,
            Length = input.Length,
            Height = input.Height
        };
        
        // Add structure
        var structureParams = new StructureParameters {
            BaySpacing = input.BaySpacing ?? CalculateOptimalBaySpacing(input.Length),
            FrameType = DetermineFrameType(input.Width),
            ColumnSize = SelectColumnSize(input.Height, input.Width),
            RafterSize = SelectRafterSize(input.Width, input.RoofPitch)
        };
        
        AddStructure(building, structureParams);
        
        return building;
    }
    
    private double CalculateOptimalBaySpacing(double length) {
        // Optimal spacing between 3-6m
        var numBays = Math.Ceiling(length / 4500);
        return length / numBays;
    }
    
    private FrameType DetermineFrameType(double width) {
        if (width <= 6000) return FrameType.SimpleBeam;
        if (width <= 12000) return FrameType.Portal;
        return FrameType.MultiSpan;
    }
}
```

### Python CarportBuilder Implementation
```python
class CarportBuilder(IStructureBuilder):
    """Carport structure builder.
    
    C# Reference: CarportBuilder.cs class
    """
    
    def __init__(self, engineering_service: EngineeringService):
        """C# Ref: Constructor with EngineeringService dependency"""
        self._engineering_service = engineering_service
    
    def create_building(self, building_input: BuildingInput) -> Building:
        """Create carport building.
        
        C# Reference: CreateBuilding method
        Logic matches C# implementation
        """
        building = Building(
            name=f"Carport_{building_input.id}",  # C# string interpolation
            building_type=BuildingType.CARPORT,    # C# BuildingType.Carport
            width=building_input.width,            # C# input.Width
            length=building_input.length,          # C# input.Length
            height=building_input.height           # C# input.Height
        )
        
        # Add structure - matches C# logic
        structure_params = StructureParameters(
            bay_spacing=building_input.bay_spacing or 
                       self._calculate_optimal_bay_spacing(building_input.length),
            frame_type=self._determine_frame_type(building_input.width),
            column_size=self._select_column_size(building_input.height, 
                                               building_input.width),
            rafter_size=self._select_rafter_size(building_input.width, 
                                               building_input.roof_pitch)
        )
        
        self.add_structure(building, structure_params)
        
        return building
    
    def _calculate_optimal_bay_spacing(self, length: float) -> float:
        """Calculate optimal bay spacing.
        
        C# Reference: CalculateOptimalBaySpacing private method
        Formula matches C# exactly
        """
        # C# logic: Math.Ceiling(length / 4500)
        num_bays = math.ceil(length / 4500)
        return length / num_bays
    
    def _determine_frame_type(self, width: float) -> FrameType:
        """Determine frame type based on width.
        
        C# Reference: DetermineFrameType private method
        Conditions match C# exactly
        """
        if width <= 6000:
            return FrameType.SIMPLE_BEAM  # C# FrameType.SimpleBeam
        elif width <= 12000:
            return FrameType.PORTAL       # C# FrameType.Portal
        else:
            return FrameType.MULTI_SPAN   # C# FrameType.MultiSpan
```

### Structure Generation

**C# Implementation:**
```csharp
public void AddStructure(Building building, StructureParameters parameters) {
    // Calculate frame positions
    var framePositions = new List<double>();
    var currentPos = 0.0;
    
    while (currentPos <= building.Length) {
        framePositions.Add(currentPos);
        currentPos += parameters.BaySpacing;
    }
    
    // Ensure end frame
    if (Math.Abs(framePositions.Last() - building.Length) > 0.001) {
        framePositions.Add(building.Length);
    }
    
    // Create frames
    foreach (var position in framePositions) {
        var frame = CreatePortalFrame(
            position: new Vec3(0, position, 0),
            width: building.Width,
            height: building.Height,
            parameters: parameters
        );
        
        building.AddAssembly(frame);
    }
    
    // Add purlins
    AddPurlins(building, parameters);
    
    // Add bracing
    AddBracing(building, parameters);
}
```

**Python Implementation:**
```python
def add_structure(self, building: Building, parameters: StructureParameters):
    """Add structural framework.
    
    C# Reference: AddStructure method
    Algorithm matches C# implementation
    """
    # Calculate frame positions - matches C# logic
    frame_positions = []
    current_pos = 0.0
    
    # C# while loop logic
    while current_pos <= building.length:
        frame_positions.append(current_pos)
        current_pos += parameters.bay_spacing
    
    # Ensure end frame - matches C# condition
    if abs(frame_positions[-1] - building.length) > 0.001:
        frame_positions.append(building.length)
    
    # Create frames - matches C# foreach
    for position in frame_positions:
        frame = self._create_portal_frame(
            position=Vec3(0, position, 0),  # C# new Vec3(0, position, 0)
            width=building.width,
            height=building.height,
            parameters=parameters
        )
        
        building.add_assembly(frame)
    
    # Add secondary structure - matches C# method calls
    self._add_purlins(building, parameters)
    self._add_bracing(building, parameters)
```

## Engineering Calculations

### C# Engineering Service (Engineering.cs)
```csharp
public class EngineeringService {
    public LoadCalculation CalculateLoads(BuildingInput input) {
        var loads = new LoadCalculation();
        
        // Dead loads
        loads.RoofDeadLoad = GetRoofMaterialWeight(input.RoofMaterial);
        loads.StructureDeadLoad = EstimateStructureWeight(input);
        
        // Live loads
        loads.RoofLiveLoad = GetRoofLiveLoad(input.BuildingUse, input.Location);
        
        // Wind loads
        var windSpeed = GetBasicWindSpeed(input.Location);
        loads.WindPressure = CalculateWindPressure(windSpeed, input.Height);
        loads.WindUplift = CalculateWindUplift(windSpeed, input.RoofPitch);
        
        // Snow loads (if applicable)
        if (RequiresSnowLoad(input.Location)) {
            loads.SnowLoad = CalculateSnowLoad(input.Location, input.RoofPitch);
        }
        
        return loads;
    }
    
    private double CalculateWindPressure(double windSpeed, double height) {
        // AS1170.2 simplified
        var heightFactor = Math.Pow(height / 10.0, 0.2);
        var velocityPressure = 0.6 * Math.Pow(windSpeed, 2) / 1000; // kPa
        return velocityPressure * heightFactor * 0.85; // Cp * Cfig
    }
}
```

### Python Engineering Service
```python
class EngineeringService:
    """Engineering calculations service.
    
    C# Reference: Engineering.cs EngineeringService class
    """
    
    def calculate_loads(self, building_input: BuildingInput) -> LoadCalculation:
        """Calculate structural loads.
        
        C# Reference: CalculateLoads method
        Calculations match C# formulas
        """
        loads = LoadCalculation()
        
        # Dead loads - matches C# property assignments
        loads.roof_dead_load = self._get_roof_material_weight(
            building_input.roof_material
        )
        loads.structure_dead_load = self._estimate_structure_weight(building_input)
        
        # Live loads - matches C# method call
        loads.roof_live_load = self._get_roof_live_load(
            building_input.building_use,
            building_input.location
        )
        
        # Wind loads - matches C# calculations
        wind_speed = self._get_basic_wind_speed(building_input.location)
        loads.wind_pressure = self._calculate_wind_pressure(
            wind_speed, 
            building_input.height
        )
        loads.wind_uplift = self._calculate_wind_uplift(
            wind_speed,
            building_input.roof_pitch
        )
        
        # Snow loads - matches C# condition
        if self._requires_snow_load(building_input.location):
            loads.snow_load = self._calculate_snow_load(
                building_input.location,
                building_input.roof_pitch
            )
        
        return loads
    
    def _calculate_wind_pressure(self, wind_speed: float, height: float) -> float:
        """Calculate wind pressure.
        
        C# Reference: CalculateWindPressure private method
        Formula matches AS1170.2 simplified calculation
        """
        # C# calculation exactly
        height_factor = (height / 10.0) ** 0.2  # Math.Pow in C#
        velocity_pressure = 0.6 * wind_speed ** 2 / 1000  # kPa
        return velocity_pressure * height_factor * 0.85  # Cp * Cfig
```

### Member Selection

**C# Implementation:**
```csharp
public class MemberSelector {
    public FrameMaterial SelectColumn(double height, double load, double bucklingLength) {
        // Calculate required capacity
        var requiredCapacity = load * 1.5; // Factor of safety
        
        // Check buckling
        var slendernessRatio = bucklingLength / CalculateRadiusOfGyration(height);
        
        // Select from standard sections
        var candidates = StandardSections.Columns
            .Where(c => c.AxialCapacity >= requiredCapacity)
            .Where(c => c.SlendernessLimit >= slendernessRatio)
            .OrderBy(c => c.MassPerMeter)
            .ToList();
        
        return candidates.FirstOrDefault() ?? StandardSections.GetDefault("Column");
    }
    
    public FrameMaterial SelectBeam(double span, double load, BeamSupport support) {
        // Calculate moment
        double moment = support switch {
            BeamSupport.SimplelySupported => load * Math.Pow(span, 2) / 8,
            BeamSupport.Fixed => load * Math.Pow(span, 2) / 12,
            BeamSupport.Cantilever => load * Math.Pow(span, 2) / 2,
            _ => load * Math.Pow(span, 2) / 8
        };
        
        // Required section modulus
        var requiredZ = moment * 1.5 / 250; // fy = 250 MPa
        
        return StandardSections.Beams
            .Where(b => b.SectionModulusX >= requiredZ)
            .OrderBy(b => b.MassPerMeter)
            .FirstOrDefault();
    }
}
```

**Python Implementation:**
```python
class MemberSelector:
    """Structural member selection.
    
    C# Reference: Engineering.cs MemberSelector class
    """
    
    def select_column(self, height: float, load: float, 
                     buckling_length: float) -> FrameMaterial:
        """Select appropriate column section.
        
        C# Reference: SelectColumn method
        Logic matches C# LINQ queries
        """
        # Calculate required capacity - matches C#
        required_capacity = load * 1.5  # Factor of safety
        
        # Check buckling - matches C# calculation
        slenderness_ratio = buckling_length / self._calculate_radius_of_gyration(height)
        
        # Select from standard sections - implements C# LINQ logic
        candidates = [
            c for c in StandardSections.COLUMNS
            if c.axial_capacity >= required_capacity
            and c.slenderness_limit >= slenderness_ratio
        ]
        
        # Sort by mass - matches C# OrderBy
        candidates.sort(key=lambda c: c.mass_per_meter)
        
        # Return first or default - matches C# FirstOrDefault
        return candidates[0] if candidates else StandardSections.get_default("Column")
    
    def select_beam(self, span: float, load: float, 
                   support: BeamSupport) -> FrameMaterial:
        """Select appropriate beam section.
        
        C# Reference: SelectBeam method
        Moment calculations match C# switch expression
        """
        # Calculate moment - Python match statement equivalent to C# switch
        if support == BeamSupport.SIMPLY_SUPPORTED:
            moment = load * span ** 2 / 8
        elif support == BeamSupport.FIXED:
            moment = load * span ** 2 / 12
        elif support == BeamSupport.CANTILEVER:
            moment = load * span ** 2 / 2
        else:
            moment = load * span ** 2 / 8  # Default
        
        # Required section modulus - matches C# calculation
        required_z = moment * 1.5 / 250  # fy = 250 MPa
        
        # Select beam - implements C# LINQ query
        candidates = [
            b for b in StandardSections.BEAMS
            if b.section_modulus_x >= required_z
        ]
        
        # Sort and return - matches C# OrderBy and FirstOrDefault
        candidates.sort(key=lambda b: b.mass_per_meter)
        return candidates[0] if candidates else None
```

## Business Rules

### C# Business Rules (CarportRules.cs)
```csharp
public static class CarportBusinessRules {
    public static ValidationResult ValidateDimensions(BuildingInput input) {
        var result = new ValidationResult();
        
        // Width validation
        if (input.Width < 2400) {
            result.AddError("Width must be at least 2.4m");
        }
        if (input.Width > 12000) {
            result.AddError("Width cannot exceed 12m for single span");
        }
        
        // Length validation
        if (input.Length < 3000) {
            result.AddError("Length must be at least 3m");
        }
        
        // Height validation
        if (input.Height < 2100) {
            result.AddError("Minimum clearance height is 2.1m");
        }
        if (input.Height > 4200) {
            result.AddWarning("Heights over 4.2m may require special approval");
        }
        
        // Aspect ratio
        var aspectRatio = input.Length / input.Width;
        if (aspectRatio > 4) {
            result.AddWarning($"High aspect ratio ({aspectRatio:F1}) may require additional bracing");
        }
        
        return result;
    }
    
    public static double CalculateMinimumFootingSize(double columnLoad, double soilBearing) {
        // AS2870 simplified
        var requiredArea = columnLoad * 1.5 / soilBearing; // m²
        var side = Math.Sqrt(requiredArea);
        
        // Round up to nearest 100mm
        return Math.Ceiling(side * 10) / 10;
    }
}
```

### Python Business Rules
```python
class CarportBusinessRules:
    """Carport-specific business rules.
    
    C# Reference: CarportRules.cs static class
    """
    
    @staticmethod
    def validate_dimensions(building_input: BuildingInput) -> ValidationResult:
        """Validate carport dimensions.
        
        C# Reference: ValidateDimensions static method
        Validation logic matches C# exactly
        """
        result = ValidationResult()
        
        # Width validation - matches C# conditions
        if building_input.width < 2400:
            result.add_error("Width must be at least 2.4m")
        if building_input.width > 12000:
            result.add_error("Width cannot exceed 12m for single span")
        
        # Length validation - matches C#
        if building_input.length < 3000:
            result.add_error("Length must be at least 3m")
        
        # Height validation - matches C#
        if building_input.height < 2100:
            result.add_error("Minimum clearance height is 2.1m")
        if building_input.height > 4200:
            result.add_warning("Heights over 4.2m may require special approval")
        
        # Aspect ratio - matches C# calculation and formatting
        aspect_ratio = building_input.length / building_input.width
        if aspect_ratio > 4:
            result.add_warning(
                f"High aspect ratio ({aspect_ratio:.1f}) may require additional bracing"
            )
        
        return result
    
    @staticmethod
    def calculate_minimum_footing_size(column_load: float, 
                                     soil_bearing: float) -> float:
        """Calculate minimum footing size.
        
        C# Reference: CalculateMinimumFootingSize static method
        AS2870 simplified calculation matches C#
        """
        # C# calculation exactly
        required_area = column_load * 1.5 / soil_bearing  # m²
        side = math.sqrt(required_area)
        
        # Round up to nearest 100mm - matches C# Math.Ceiling
        return math.ceil(side * 10) / 10
```

## Building Factory

### C# Factory Pattern (BuildingFactory.cs)
```csharp
public class BuildingFactory {
    private readonly Dictionary<BuildingType, Func<IStructureBuilder>> _builders;
    private readonly IServiceProvider _serviceProvider;
    
    public BuildingFactory(IServiceProvider serviceProvider) {
        _serviceProvider = serviceProvider;
        _builders = new Dictionary<BuildingType, Func<IStructureBuilder>> {
            [BuildingType.Carport] = () => new CarportBuilder(
                _serviceProvider.GetService<EngineeringService>()
            ),
            [BuildingType.Shed] = () => new ShedBuilder(
                _serviceProvider.GetService<EngineeringService>()
            )
        };
    }
    
    public IStructureBuilder GetBuilder(BuildingType type) {
        if (_builders.TryGetValue(type, out var factory)) {
            return factory();
        }
        
        throw new NotSupportedException($"Building type {type} not supported");
    }
}
```

### Python Factory Implementation
```python
class BuildingFactory:
    """Factory for creating structure builders.
    
    C# Reference: BuildingFactory.cs class
    """
    
    def __init__(self, service_provider: ServiceProvider):
        """C# Ref: Constructor with IServiceProvider"""
        self._service_provider = service_provider
        
        # Builder registry - matches C# Dictionary initialization
        self._builders = {
            BuildingType.CARPORT: lambda: CarportBuilder(
                self._service_provider.get_service(EngineeringService)
            ),
            BuildingType.SHED: lambda: ShedBuilder(
                self._service_provider.get_service(EngineeringService)
            )
        }
    
    def get_builder(self, building_type: BuildingType) -> IStructureBuilder:
        """Get builder for building type.
        
        C# Reference: GetBuilder method
        Exception handling matches C#
        """
        # C# TryGetValue pattern
        factory = self._builders.get(building_type)
        if factory:
            return factory()
        
        # C# throw NotSupportedException
        raise NotSupportedError(f"Building type {building_type} not supported")
```

## Validation Logic

### C# Validation Framework
```csharp
public class ValidationResult {
    public bool IsValid => !Errors.Any();
    public List<ValidationError> Errors { get; } = new List<ValidationError>();
    public List<ValidationWarning> Warnings { get; } = new List<ValidationWarning>();
    
    public void AddError(string message, string field = null) {
        Errors.Add(new ValidationError {
            Message = message,
            Field = field,
            Severity = ValidationSeverity.Error
        });
    }
    
    public void Merge(ValidationResult other) {
        Errors.AddRange(other.Errors);
        Warnings.AddRange(other.Warnings);
    }
}

public class BuildingValidator {
    private readonly List<IValidationRule> _rules;
    
    public ValidationResult Validate(BuildingInput input) {
        var result = new ValidationResult();
        
        foreach (var rule in _rules) {
            var ruleResult = rule.Validate(input);
            result.Merge(ruleResult);
        }
        
        return result;
    }
}
```

### Python Validation Implementation
```python
@dataclass
class ValidationResult:
    """Validation result container.
    
    C# Reference: ValidationResult class
    """
    errors: List[ValidationError] = field(default_factory=list)
    warnings: List[ValidationWarning] = field(default_factory=list)
    
    @property
    def is_valid(self) -> bool:
        """C# Ref: bool IsValid => !Errors.Any()"""
        return len(self.errors) == 0
    
    def add_error(self, message: str, field: str = None):
        """Add validation error.
        
        C# Reference: AddError method
        """
        self.errors.append(ValidationError(
            message=message,
            field=field,
            severity=ValidationSeverity.ERROR
        ))
    
    def merge(self, other: 'ValidationResult'):
        """Merge another validation result.
        
        C# Reference: Merge method
        """
        self.errors.extend(other.errors)
        self.warnings.extend(other.warnings)

class BuildingValidator:
    """Building input validator.
    
    C# Reference: BuildingValidator class
    """
    
    def __init__(self, rules: List[IValidationRule]):
        """C# Ref: Constructor with validation rules"""
        self._rules = rules
    
    def validate(self, building_input: BuildingInput) -> ValidationResult:
        """Validate building input.
        
        C# Reference: Validate method
        Algorithm matches C# foreach loop
        """
        result = ValidationResult()
        
        # C# foreach loop
        for rule in self._rules:
            rule_result = rule.validate(building_input)
            result.merge(rule_result)
        
        return result
```

## Testing Business Logic Alignment

### Builder Tests

```python
def test_carport_builder_dimensions():
    """Test carport builder matches C# dimension handling"""
    builder = CarportBuilder(EngineeringService())
    
    input_data = BuildingInput(
        width=6000,
        length=9000,
        height=2700,
        roof_type=RoofType.GABLE,
        roof_pitch=15
    )
    
    building = builder.create_building(input_data)
    
    # Test building properties match C#
    assert building.name == f"Carport_{input_data.id}"
    assert building.width == 6000
    assert building.length == 9000
    assert building.height == 2700
    
    # Test frame generation
    frames = building.get_assemblies_by_type("frame")
    expected_frames = 3  # 0m, 4.5m, 9m positions
    assert len(frames) == expected_frames

def test_engineering_calculations():
    """Test engineering calculations match C#"""
    service = EngineeringService()
    
    input_data = BuildingInput(
        width=6000,
        length=9000,
        height=3000,
        location="Brisbane",
        wind_region="B"
    )
    
    loads = service.calculate_loads(input_data)
    
    # Test wind pressure calculation
    # Expected from C# formula
    expected_pressure = 0.6 * 45**2 / 1000 * (3/10)**0.2 * 0.85
    assert abs(loads.wind_pressure - expected_pressure) < 0.01
```

### Validation Tests

```python
def test_business_rules_validation():
    """Test business rules match C# validation"""
    input_data = BuildingInput(
        width=2000,  # Too small
        length=3000,
        height=2100
    )
    
    result = CarportBusinessRules.validate_dimensions(input_data)
    
    # Should have width error
    assert not result.is_valid
    assert len(result.errors) == 1
    assert "Width must be at least 2.4m" in result.errors[0].message
    
    # Test aspect ratio warning
    input_data.width = 3000
    input_data.length = 15000  # 5:1 ratio
    
    result = CarportBusinessRules.validate_dimensions(input_data)
    assert len(result.warnings) == 1
    assert "High aspect ratio (5.0)" in result.warnings[0].message
```

## Summary

The business logic alignment ensures:
- Identical builder patterns and interfaces
- Same structural generation algorithms
- Matching engineering calculations and formulas
- Compatible validation frameworks and rules
- Verified business rule implementations

All business logic operations in PyModel produce results identical to the C# implementation, ensuring complete compatibility for building generation and validation.