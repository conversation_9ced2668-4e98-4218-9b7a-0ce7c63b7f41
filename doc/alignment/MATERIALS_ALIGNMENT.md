# Materials System C# to Python Alignment Guide

## Overview

This document provides a complete alignment mapping between the C# Materials.cs implementation and the Python materials module.

## File Mapping

| C# File | Python File | Lines | Status |
|---------|-------------|-------|--------|
| Materials.cs | materials/base.py | 588 → 1200+ | ✅ Complete |
| ColorMaterialHelper.cs | materials/visual.py | 184 → 400+ | ✅ Complete |
| MeshHelper.cs (Mesh3d) | materials/mesh.py | 7 → 200+ | ✅ Complete |

## Class-by-Class Alignment

### BracketMaterial (Lines 10-43)
```csharp
// C#
public class BracketMaterial
{
    public string Name { get; set; }
    public string MeshName { get; set; }
    private BracketMesh _mesh = null;
    internal void InternalSetMesh(BracketMesh mesh) { ... }
    public BracketMesh GetBracketMesh() { ... }
    public Mesh3d GetMesh() => GetBracketMesh().Mesh;
    public Box3 GetBounds() => GetMesh().Bounds;
    public Dictionary<string, BracketAttachment> GetAttachments() { ... }
}
```

```python
# Python
@dataclass
class BracketMaterial:
    name: str = ""
    mesh_name: str = ""
    _mesh: Optional[BracketMesh] = field(default=None, init=False, repr=False)
    
    def internal_set_mesh(self, mesh: BracketMesh) -> None: ...
    def get_bracket_mesh(self) -> BracketMesh: ...
    def get_mesh(self) -> 'Mesh3d': ...
    def get_bounds(self) -> Box3: ...
    def get_attachments(self) -> Dict[str, BracketAttachment]: ...
```

### ColorMaterial (Lines 97-168)
| C# Method | Python Method | Notes |
|-----------|---------------|-------|
| FromHex() | from_hex() | Static factory method |
| FromRgb() | from_rgb() | Static factory method |
| FromCMYK() | from_cmyk() | CMYK to RGB conversion |

### FrameMaterial (Lines 275-463)
| C# Property/Method | Python Implementation | Notes |
|--------------------|----------------------|-------|
| Id property | @property id | Includes flipped state |
| Web property | @property web | Returns height |
| Flange property | @property flange | Returns width |
| FlangeSingle | @property flange_single | B2B handling |
| CreateC() | create_c() | C-section factory |
| CreateZ() | create_z() | Z-section factory |
| CreateTH() | create_th() | TopHat factory |
| CreateSHS() | create_shs() | Square hollow section |
| CreatePadStile() | create_pad_stile() | PAD stile factory |
| CreateSideRollerDoorJamb() | create_side_roller_door_jamb() | SRDJ factory |

### Enumerations

#### FastenerMaterialType (Lines 225-236)
```python
class FastenerMaterialType(Enum):
    UNKNOWN = 0
    BOLT = 1
```

#### FootingMaterialType (Lines 269-273)
```python
class FootingMaterialType(Enum):
    BLOCK = 0
    BORED = 1
```

#### FrameMaterialType (Lines 465-503)
```python
class FrameMaterialType(Enum):
    UNKNOWN = 0
    C = 1
    TH = 2
    Z = 3
    SHS = 4
    PAD = 5
    SRDJ = 6
```

#### PunchingWhere (Lines 562-569)
```python
class PunchingWhere(Enum):
    WEB = 0
    FLANGE = 1
    CENTER = 2
    WEB_LEFT = 3
    WEB_RIGHT = 4
```

## Method Signature Conversions

### Naming Conventions
- PascalCase → snake_case for methods
- Properties remain as properties with @property decorator
- Static methods use @staticmethod decorator

### Type Conversions
| C# Type | Python Type |
|---------|-------------|
| string | str |
| double | float |
| int | int |
| bool | bool |
| byte | int (0-255) |
| List<T> | List[T] |
| Dictionary<K,V> | Dict[K,V] |
| T? | Optional[T] |

## Special Implementations

### 1. FootingMaterial Equality
C# implements IEquatable<FootingMaterial>. Python uses:
- `__eq__` for equality comparison
- `__hash__` for dictionary/set usage

### 2. Punching Struct Methods
C# struct methods converted to instance methods:
- Negate() → negate()
- Abs() → abs()
- Add() → add()

### 3. Color Library Extensions
Python implementation adds:
- ColorLibrary class with predefined colors
- get_or_create_color() with RGB string parsing
- Material appearance system
- Texture mapping capabilities

## Visual Properties Extensions

### ColorLibrary
```python
class ColorLibrary:
    COLORBOND_COLORS = {
        "MONUMENT": ColorMaterial.from_hex("Monument", "CB", "323233"),
        # ... 30+ colors
    }
    
    COLORSTEEL_COLORS = {
        "AZURE": ColorMaterial.from_rgb("Azure", "CB", 77, 109, 125),
        # ... 25+ colors
    }
```

### MaterialAppearance
```python
@dataclass
class MaterialAppearance:
    base_color: Optional[ColorMaterial] = None
    metallic: float = 0.0
    roughness: float = 0.5
    opacity: float = 1.0
    emissive_color: Optional[ColorMaterial] = None
    emissive_intensity: float = 0.0
```

## Testing Alignment

| C# Test Pattern | Python Test Pattern |
|-----------------|-------------------|
| [TestMethod] | def test_xxx() |
| Assert.AreEqual(a, b) | assert a == b |
| Assert.ThrowsException<T> | pytest.raises(T) |

## Performance Considerations

1. **Dataclasses**: Used for value semantics similar to C# structs
2. **Caching**: _mesh caching in BracketMaterial
3. **Property calculations**: Computed properties for derived values

## Migration Checklist

- ✅ All material classes implemented
- ✅ All enumerations converted
- ✅ All factory methods implemented
- ✅ Property getters converted
- ✅ Equality/hash methods added where needed
- ✅ C# reference comments added
- ✅ Comprehensive tests written
- ✅ Visual properties extended

## Validation

The Python implementation has been validated against:
1. All method signatures from Materials.cs
2. Expected behavior from C# tests
3. Type safety with Python type hints
4. Edge cases and error handling