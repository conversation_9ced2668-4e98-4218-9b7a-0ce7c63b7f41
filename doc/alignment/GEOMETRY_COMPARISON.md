# Geometry Implementation Comparison: .NET vs Python

## Summary
This document compares the .NET Geo.cs implementation with our Python geometry module to identify any missing methods or functionality.

## ✅ Implemented in Both

### Core Primitives
- **Vec2**: 2D vector with all operations
- **Vec3**: 3D vector with all operations  
- **Line1**: 1D line segment
- **Line2**: 2D line segment
- **Line3**: 3D line segment
- **Box2**: 2D axis-aligned bounding box
- **Box3**: 3D axis-aligned bounding box
- **Plane3**: 3D plane representation
- **Basis3**: 3D coordinate system basis vectors
- **TriIndex**: Triangle indices for mesh operations

### Helper Functions in Geo Class
- Angle constants (DEG0, DEG45, DEG90, DEG180, DEG270, DEG360)
- Conversion constants (TO_DEG, TO_RAD)
- `normalize_angle()`: Normalize angle to [0, 2π)
- `wrap_index()`: Wrap index for circular collections
- Trigonometry helpers (trig_soh, trig_sho, trig_cah, trig_cha, trig_toa, trig_tao)
- `mirror_angle()`: Mirror angle by 180°
- `mirror_vec_x()`: Mirror 2D vector across vertical line
- `get_extents()`: Get bounding box of 2D points
- `get_offsets()`: Calculate cumulative offsets from bay sizes
- `get_bay_sizes()`: Divide length into bay sizes with rounding
- Vector creation shortcuts (v2, v3, vx, vy, vz, v2xy, v2xz, v2yz)
- Line creation shortcuts (ln1, ln2, ln3, ln2xy, ln2xz, ln2yz)
- `polar()`: Create 2D vector from polar coordinates
- `rotate()`: Rotate 2D vector by angle
- `mid()`: Calculate midpoint of values/lines
- `interpolate()`: Interpolate along lines
- `round()`: Round vector/line components
- `ln_swap()`: Swap start/end of lines
- `ln_extend()`: Extend lines by specified amounts
- `ln_offset()`: Offset 2D lines perpendicular to direction
- `box_line_inters()`: Calculate line-box intersection
- `get_near_far()`: Sort points by distance from base point

## ✅ Recently Implemented (Added to Python)

### Geo Static Methods
1. **ln_up()/ln_down()**: Offset line upward/downward (direction-aware)
2. **ln_offset_polyline()**: Offset a list of connected line segments
3. **poly_offset()**: Offset an entire polygon
4. **ln_low_high()**: Order line points by Y coordinate (then X)
5. **ln_left_right()**: Order line points by X coordinate (then Y)
6. **ln_range()**: Create Line1 from center and size
7. **ln_extend_to_line()**: Extend line to intersection with test line

### Additional Notes
- .NET uses `double.MinValue/MaxValue` while Python uses `float('inf')/-float('inf')`
- .NET has equality operators and GetHashCode() for all structs
- .NET includes DebuggerDisplay attributes for better debugging
- Python implementation uses `@dataclass` which provides equality by default

## 🔧 Implementation Status

### ✅ Completed (All high-priority methods implemented)
- `ln_up()/ln_down()` - Direction-aware line offsetting
- `poly_offset()` - Polygon offset for wall/roof calculations
- `ln_offset_polyline()` - Polyline offset for complex geometries
- `ln_extend_to_line()` - Line extension to intersection
- `ln_low_high()/ln_left_right()` - Line point ordering
- `ln_range()` - Line1 creation from center and size

### 🎯 Python Implementation Features
- Consistent snake_case naming convention
- Type hints for all parameters and return values
- Comprehensive docstrings
- Follows Python best practices
- Compatible with the .NET implementation logic

## Code Quality Notes
- Python implementation is well-structured with proper type hints
- Good separation of concerns with different modules for each primitive type
- Missing some edge case handling that exists in .NET (e.g., some methods have different null/None handling)