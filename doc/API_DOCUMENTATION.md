# BIM Backend API Documentation

## Overview

The BIM Backend API provides RESTful endpoints for generating 3D building models (carports and sheds) with engineering validation. All requests use AES-256-CBC encryption for security.

## Base URL

```
http://localhost:8000
```

## Authentication

Currently, the API uses AES encryption for request/response security. Future versions will include JWT authentication.

## Endpoints

### 1. Health Check

Check if the API is running and healthy.

**Endpoint:** `GET /health`

**Response:**
```json
{
  "status": "healthy",
  "version": "1.0.0",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### 2. Create Carport

Generate a carport model based on input parameters.

**Endpoint:** `POST /api/carport/create`

**Request Body:**
```json
{
  "encrypted_data": "base64_encoded_aes_encrypted_json"
}
```

**Encrypted Payload Structure:**
```json
{
  "building_type": "Carport",
  "name": "My Carport",
  "roof_type": "Gable",
  "span": 6000,
  "length": 6000,
  "height": 2700,
  "bays": 2,
  "wind_speed": 32,
  "pitch": 15.0,
  "slab": true,
  "slab_thickness": 100.0,
  "soil": "M",
  "validate_engineering": false,
  "output_format": "gltf"
}
```

**Response:**
```json
{
  "success": true,
  "status": "success",
  "message": "Carport generated successfully",
  "file_url": "/api/download/abc123.gltf",
  "error_details": null
}
```

**Error Response:**
```json
{
  "success": false,
  "status": "error",
  "message": "Invalid span: must be between 3000 and 12000",
  "file_url": null,
  "error_details": {
    "field": "span",
    "value": 15000,
    "min": 3000,
    "max": 12000
  }
}
```

### 3. Download Generated File

Download a generated model file.

**Endpoint:** `GET /api/download/{file_id}`

**Parameters:**
- `file_id`: The file identifier returned from the create endpoint

**Response:** Binary file data (GLTF, DXF, or IFC format)

## Request Parameters

### Building Input Parameters

| Parameter | Type | Required | Description | Constraints |
|-----------|------|----------|-------------|-------------|
| building_type | string | Yes | Type of building | "Carport" or "Shed" |
| name | string | No | Building name | Max 100 chars |
| roof_type | string | Yes | Roof style | "Flat", "Gable", "Awning", "AttachedAwning" |
| span | number | Yes | Width in mm | 3000-12000 |
| length | number | Yes | Length in mm | Min 3000, must be divisible by bay size |
| height | number | Yes | Eave height in mm | 2100-6000 |
| bays | integer | Yes | Number of bays | 1-20 |
| wind_speed | integer | Yes | Design wind speed | 28, 32, 36, 41 |
| pitch | number | Conditional | Roof pitch in degrees | 5-30 (required for gable) |
| slab | boolean | No | Include concrete slab | Default: false |
| slab_thickness | number | Conditional | Slab thickness in mm | Required if slab=true |
| soil | string | No | Soil classification | "M", "H", "VH", "EH" |
| validate_engineering | boolean | No | Run engineering validation | Default: false |
| output_format | string | No | Output file format | "gltf", "dxf", "ifc" (default: "gltf") |

### Roof Type Constraints

- **Flat**: No pitch, supports overhang
- **Gable**: Requires pitch (5-30°), no overhang
- **Awning**: Single slope, requires pitch
- **AttachedAwning**: Attached to building, requires pitch

### Overhang Rules (Flat Roof Only)

| Span Range | Maximum Overhang |
|------------|------------------|
| ≤ 3600mm | 600mm |
| 3601-5000mm | 900mm |
| > 5000mm | 1200mm |

## Encryption

### AES-256-CBC Encryption

All request payloads must be encrypted using AES-256-CBC with:
- Key: SHA256 hash of shared secret
- IV: Random 16 bytes prepended to ciphertext
- Padding: PKCS7

### Encryption Example (Python)

```python
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
import base64
import hashlib
import json
import os

class AESEncryption:
    def __init__(self, key: str):
        self.key = hashlib.sha256(key.encode()).digest()
    
    def encrypt(self, plaintext: str) -> str:
        iv = os.urandom(16)
        cipher = Cipher(algorithms.AES(self.key), modes.CBC(iv), backend=default_backend())
        encryptor = cipher.encryptor()
        
        # Pad plaintext
        padding_length = 16 - (len(plaintext.encode()) % 16)
        padded_data = plaintext.encode() + bytes([padding_length] * padding_length)
        
        # Encrypt
        ciphertext = encryptor.update(padded_data) + encryptor.finalize()
        
        # Combine IV and ciphertext
        encrypted_data = iv + ciphertext
        
        return base64.b64encode(encrypted_data).decode()

# Usage
aes = AESEncryption("your-secret-key")
payload = {
    "building_type": "Carport",
    "span": 6000,
    "length": 6000,
    "height": 2700,
    "bays": 2,
    "wind_speed": 32
}
encrypted = aes.encrypt(json.dumps(payload))
```

## Error Codes

| Status Code | Error Type | Description |
|-------------|------------|-------------|
| 200 | Success | Request processed successfully |
| 400 | Bad Request | Invalid input parameters |
| 401 | Unauthorized | Invalid encryption or authentication |
| 422 | Validation Error | Input validation failed |
| 500 | Internal Server Error | Server processing error |

## Response Status Values

- `success`: Operation completed successfully
- `error`: General error occurred
- `validation_error`: Input validation failed

## Rate Limiting

Currently no rate limiting is implemented. Future versions will include:
- 100 requests per minute per IP
- 1000 requests per hour per API key

## Example Requests

### 1. Create Simple Flat Carport

```bash
# Prepare payload
PAYLOAD='{
  "building_type": "Carport",
  "roof_type": "Flat",
  "span": 6000,
  "length": 6000,
  "height": 2400,
  "bays": 2,
  "wind_speed": 32
}'

# Encrypt payload (example using Python script)
ENCRYPTED=$(python encrypt.py "$PAYLOAD")

# Make request
curl -X POST http://localhost:8000/api/carport/create \
  -H "Content-Type: application/json" \
  -d "{\"encrypted_data\": \"$ENCRYPTED\"}"
```

### 2. Create Gable Carport with Engineering

```bash
PAYLOAD='{
  "building_type": "Carport",
  "name": "Large Gable Carport",
  "roof_type": "Gable",
  "span": 9000,
  "length": 12000,
  "height": 3000,
  "bays": 4,
  "wind_speed": 36,
  "pitch": 20.0,
  "slab": true,
  "slab_thickness": 150.0,
  "soil": "H",
  "validate_engineering": true,
  "output_format": "ifc"
}'
```

### 3. Download Generated File

```bash
# Get file URL from create response
FILE_URL="/api/download/abc123.gltf"

# Download file
curl -O http://localhost:8000$FILE_URL
```

## Webhooks (Future)

Future versions will support webhooks for long-running operations:

```json
{
  "webhook_url": "https://your-server.com/webhook",
  "webhook_events": ["generation_complete", "engineering_complete", "error"]
}
```

## SDK Support

### Python SDK (Future)

```python
from bim_backend import BIMClient

client = BIMClient(api_key="your-api-key", secret="your-secret")

carport = client.create_carport(
    span=6000,
    length=6000,
    height=2700,
    roof_type="Gable",
    pitch=15.0
)

# Download generated file
carport.download("my_carport.gltf")
```

## Changelog

### Version 1.0.0 (2024-01-15)
- Initial release
- Support for carport generation
- GLTF, DXF, IFC output formats
- AES encryption for security
- Basic engineering validation

### Planned Features
- JWT authentication
- Webhook support
- Batch processing
- Real-time generation status
- SDK libraries
- GraphQL endpoint