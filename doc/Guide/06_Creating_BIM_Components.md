# Creating BIM Components - Building Information Modeling Elements

This guide explains how to create and work with BIM components in PyModel, from basic elements to complex assemblies.

## Table of Contents
1. [BIM Component Overview](#bim-component-overview)
2. [Basic Components](#basic-components)
3. [Structural Components](#structural-components)
4. [Cladding and Panels](#cladding-and-panels)
5. [Openings](#openings)
6. [Accessories and Hardware](#accessories-and-hardware)
7. [Component Relationships](#component-relationships)
8. [Advanced Components](#advanced-components)

## BIM Component Overview

BIM components in PyModel represent physical building elements with:
- **Geometry**: 3D shape and position
- **Material**: What it's made of
- **Properties**: Engineering and metadata
- **Relationships**: How it connects to other components

### Component Hierarchy

```
BIMComponent (Base)
├── StructuralComponent
│   ├── Column
│   ├── Beam
│   ├── Rafter
│   ├── Purlin
│   └── Brace
├── SurfaceComponent  
│   ├── WallPanel
│   ├── RoofPanel
│   └── FloorSlab
├── Opening
│   ├── Door
│   ├── Window
│   └── Vent
└── Accessory
    ├── Gutter
    ├── Downpipe
    ├── Flashing
    └── Fastener
```

## Basic Components

### Creating Simple Components

```python
from src.bim import Column, Beam, WallPanel
from src.geometry import Vec3
from src.materials import FrameMaterial, CladdingMaterial

# Create a column
column = Column(
    position=Vec3(0, 0, 0),      # Base position
    height=3600,                  # Height in mm
    material=FrameMaterial.get_standard("200UC59"),
    rotation=0,                   # Rotation in degrees
    name="C1"                     # Component ID
)

# Create a beam
beam = Beam(
    start=Vec3(0, 0, 3600),      # Start point
    end=Vec3(6000, 0, 3600),     # End point
    material=FrameMaterial.get_standard("250UB31"),
    rotation=0,                   # Roll angle
    name="B1"
)

# Create a wall panel
wall = WallPanel(
    origin=Vec3(0, 0, 0),        # Bottom-left corner
    width=3000,                   # Panel width
    height=3600,                  # Panel height
    normal=Vec3(0, -1, 0),       # Facing direction
    material=CladdingMaterial.get_standard("Trimdek_Monument"),
    name="W1"
)
```

### Component Properties

```python
# Access component properties
print(f"Column height: {column.height}mm")
print(f"Column weight: {column.get_weight()}kg")
print(f"Column volume: {column.get_volume()}mm³")

# Get bounding box
bbox = column.get_bounding_box()
print(f"Bounding box: {bbox.min} to {bbox.max}")

# Get transformation matrix
transform = column.get_transform()

# Get all vertices
vertices = column.get_vertices()
```

## Structural Components

### Columns with Base Plates

```python
from src.bim import ColumnWithBasePlate

# Column with base plate and anchor bolts
column_with_base = ColumnWithBasePlate(
    position=Vec3(6000, 0, 0),
    height=3600,
    column_material=FrameMaterial.get_standard("250UC89"),
    base_plate={
        "width": 350,
        "length": 350,
        "thickness": 20,
        "bolt_pattern": "4-bolt",
        "bolt_diameter": 24,
        "bolt_centers": 250
    }
)

# Access sub-components
base_plate = column_with_base.get_base_plate()
anchor_bolts = column_with_base.get_anchor_bolts()
```

### Portal Frame

```python
from src.bim import PortalFrame

# Create a portal frame (columns + rafters)
portal = PortalFrame(
    position=Vec3(0, 3000, 0),   # Frame position
    width=12000,                  # Frame width
    eave_height=4200,            # Eave height
    apex_height=5400,            # Ridge height
    column_material=FrameMaterial.get_standard("310UC97"),
    rafter_material=FrameMaterial.get_standard("360UB50"),
    knee_type="haunched",         # Knee connection type
    knee_depth=600,              # Haunch depth
    base_type="pinned"           # Base connection
)

# Get frame components
left_column = portal.get_left_column()
right_column = portal.get_right_column()
left_rafter = portal.get_left_rafter()
right_rafter = portal.get_right_rafter()
knee_connections = portal.get_knee_connections()
```

### Bracing Systems

```python
from src.bim import WallBracing, RoofBracing

# Wall bracing
wall_brace = WallBracing(
    start_bottom=Vec3(0, 0, 0),
    end_top=Vec3(0, 6000, 3600),
    material=FrameMaterial.get_standard("12mm_rod"),
    brace_type="cross",          # cross, single, chevron
    tension_only=True
)

# Roof bracing
roof_brace = RoofBracing(
    bay_start=Vec3(0, 0, 4200),
    bay_end=Vec3(12000, 6000, 5400),
    material=FrameMaterial.get_standard("90x90x6EA"),
    pattern="cross"
)
```

## Cladding and Panels

### Wall Cladding with Joints

```python
from src.bim import CladdingSystem

# Create cladding system for a wall
wall_cladding = CladdingSystem(
    surface_start=Vec3(0, 0, 0),
    surface_end=Vec3(12000, 0, 3600),
    direction=Vec3(0, -1, 0),     # Outward normal
    material=CladdingMaterial.get_standard("Trimdek_Monument"),
    orientation="vertical",        # vertical or horizontal
    sheet_length=6000,            # Standard sheet length
    include_overlaps=True
)

# Get individual sheets
sheets = wall_cladding.get_sheets()
for i, sheet in enumerate(sheets):
    print(f"Sheet {i+1}: {sheet.width}mm x {sheet.length}mm at {sheet.position}")

# Get fastener positions
fasteners = wall_cladding.get_fastener_positions(
    fastener_spacing=300,          # Along ribs
    edge_distance=50
)
```

### Insulated Panels

```python
from src.bim import InsulatedPanel

# Create insulated roof panel
insulated_roof = InsulatedPanel(
    origin=Vec3(0, 0, 4200),
    width=1000,                    # Panel module width
    length=12000,
    outer_material=CladdingMaterial.get_standard("Trimdek_Surfmist"),
    inner_material=CladdingMaterial.get_standard("Trimdek_White"),
    insulation={
        "type": "PIR_foam",
        "thickness": 75,           # mm
        "r_value": 3.2,
        "density": 32              # kg/m³
    },
    edge_type="male_female"        # Joint type
)
```

## Openings

### Doors

```python
from src.bim import RollerDoor, PersonnelDoor, SlidingDoor

# Roller door
roller_door = RollerDoor(
    wall_position=Vec3(3000, 0, 0),  # Position on wall
    width=4000,
    height=4200,
    material=CladdingMaterial.get_standard("Trimdek_Monument"),
    operation={
        "type": "chain",            # chain, motor, manual
        "motor_side": "right",
        "guides": "windlock",
        "bottom_rail": "double_channel"
    },
    wind_rating="W50"              # Wind rating
)

# Personnel door
pa_door = PersonnelDoor(
    wall_position=Vec3(8000, 0, 0),
    width=920,
    height=2040,
    swing_direction="outward",
    hinge_side="left",
    frame_material=FrameMaterial.get_standard("100x50x3RHS"),
    door_material="colorbond_monument",
    hardware={
        "lock_type": "mortice",
        "closer": True,
        "panic_bar": False
    }
)

# Sliding doors
sliding_door = SlidingDoor(
    wall_position=Vec3(1000, 0, 0),
    panels=[
        {"width": 1800, "type": "fixed"},
        {"width": 1800, "type": "sliding"},
        {"width": 1800, "type": "sliding"}
    ],
    height=2400,
    track_type="top_hung",
    frame_material=FrameMaterial.get_standard("100x50x3RHS")
)
```

### Windows

```python
from src.bim import Window, Louver

# Sliding window
window = Window(
    wall_position=Vec3(5000, 0, 1200),  # Sill at 1200mm
    width=1800,
    height=1200,
    window_type="sliding",
    operation="left_slide",
    frame_material="aluminum_bronze",
    glazing={
        "type": "single",
        "thickness": 6,
        "tint": "grey",
        "safety": True
    },
    insect_screen=True
)

# Louver window
louver = Louver(
    wall_position=Vec3(9000, 0, 2400),
    width=600,
    height=600,
    blade_material="aluminum_natural",
    blade_angle=45,
    operation="manual",
    insect_screen=True
)
```

### Skylights and Vents

```python
from src.bim import Skylight, WhirlyBird

# Skylight
skylight = Skylight(
    roof_position=Vec3(6000, 6000, 5000),
    width=900,
    length=1800,
    type="polycarbonate",
    profile="trimdek_compatible",
    color="opal",
    diffusion=70  # Light diffusion percentage
)

# Whirlybird vent
vent = WhirlyBird(
    roof_position=Vec3(3000, 9000, 5200),
    diameter=300,
    throat_diameter=250,
    color="colorbond_match",
    base_type="universal"
)
```

## Accessories and Hardware

### Gutters and Downpipes

```python
from src.bim import Gutter, Downpipe

# Gutter system
gutter = Gutter(
    start=Vec3(0, 0, 4200),
    end=Vec3(12000, 0, 4200),
    profile="quad",
    size=150,  # Quad 150
    material=ColorMaterial("colorbond", "monument"),
    fall=20,   # mm fall over length
    brackets={
        "type": "concealed",
        "spacing": 900,
        "material": "zincalume"
    }
)

# Downpipes
downpipe = Downpipe(
    top_position=Vec3(0, 0, 4200),
    bottom_position=Vec3(0, 0, 300),
    diameter=100,
    profile="round",
    material=ColorMaterial("colorbond", "monument"),
    offset_bends=[
        {"height": 4000, "offset": 300}  # Swan neck
    ],
    brackets={
        "type": "saddle",
        "spacing": 1800
    }
)
```

### Flashings

```python
from src.bim import Flashing, RidgeCapping

# Barge flashing
barge_flashing = Flashing(
    path=[
        Vec3(0, 0, 4200),
        Vec3(0, 0, 5400),
        Vec3(0, 12000, 5400),
        Vec3(0, 12000, 4200)
    ],
    profile="barge_capping",
    girth=300,  # Developed width
    material=ColorMaterial("colorbond", "surfmist"),
    laps=150    # Overlap at joints
)

# Ridge capping
ridge = RidgeCapping(
    start=Vec3(6000, 0, 5400),
    end=Vec3(6000, 12000, 5400),
    profile="corrugated_compatible",
    material=ColorMaterial("colorbond", "surfmist"),
    ridge_type="plain",  # plain, vented
    end_caps=True
)
```

## Component Relationships

### Parent-Child Relationships

```python
from src.bim import Assembly

# Create assembly with relationships
frame_assembly = Assembly("Portal Frame 1")

# Add components maintaining relationships
frame_assembly.add_component(portal.left_column, parent=None)
frame_assembly.add_component(portal.left_rafter, parent=portal.left_column)
frame_assembly.add_component(knee_connection, 
    parent=portal.left_column,
    connects_to=[portal.left_rafter]
)

# Query relationships
children = frame_assembly.get_children(portal.left_column)
connections = frame_assembly.get_connections(knee_connection)
```

### Connection Components

```python
from src.bim import Connection, Bracket

# Purlin to rafter connection
purlin_bracket = Bracket(
    position=Vec3(2000, 3000, 4800),
    bracket_type="purlin_cleat",
    size="PC150",
    material=FrameMaterial("6mm_plate"),
    rotation=15,  # Match roof pitch
    bolts=[
        {"diameter": 16, "grade": "8.8", "quantity": 2}
    ]
)

# Create connection
connection = Connection(
    type="bolted",
    primary_component=rafter,
    secondary_component=purlin,
    connection_hardware=[purlin_bracket],
    position=Vec3(2000, 3000, 4800)
)
```

## Advanced Components

### Mezzanine Floor System

```python
from src.bim import MezzanineFloor

mezzanine = MezzanineFloor(
    outline=[
        Vec3(0, 6000, 2700),
        Vec3(6000, 6000, 2700),
        Vec3(6000, 12000, 2700),
        Vec3(0, 12000, 2700)
    ],
    beam_layout={
        "primary_direction": "X",
        "primary_spacing": 3000,
        "primary_material": FrameMaterial.get_standard("250UB37"),
        "secondary_spacing": 1500,
        "secondary_material": FrameMaterial.get_standard("200UB25")
    },
    decking={
        "type": "composite_deck",
        "profile": "KF70",
        "thickness": 0.75,
        "concrete_depth": 100
    },
    edge_beam=FrameMaterial.get_standard("150PFC"),
    live_load=5.0  # kPa
)

# Get floor components
primary_beams = mezzanine.get_primary_beams()
secondary_beams = mezzanine.get_secondary_beams()
decking_sheets = mezzanine.get_decking()
edge_beams = mezzanine.get_edge_beams()
```

### Stairs

```python
from src.bim import Stair

stair = Stair(
    bottom_position=Vec3(6000, 9000, 0),
    top_position=Vec3(6000, 11000, 2700),
    width=1000,
    stair_type="straight",
    construction={
        "stringer_material": FrameMaterial.get_standard("250PFC"),
        "tread_material": "checker_plate_5mm",
        "handrail_material": FrameMaterial.get_standard("40NB"),
        "balustrade_type": "vertical_bars"
    },
    code_compliance={
        "max_rise": 190,
        "min_going": 240,
        "handrail_height": 900
    }
)

# Get stair components
stringers = stair.get_stringers()
treads = stair.get_treads()
handrails = stair.get_handrails()
landing = stair.get_landing()  # If required
```

### Crane Rails

```python
from src.bim import CraneRail, CraneBracket

# Crane rail beam
crane_rail = CraneRail(
    start=Vec3(0, 1000, 3000),
    end=Vec3(0, 11000, 3000),
    beam_material=FrameMaterial.get_standard("460UB67"),
    rail={
        "type": "A65",
        "fixing": "welded",
        "alignment_tolerance": 2  # mm
    },
    capacity=5000,  # kg
    brackets=[
        CraneBracket(
            position=Vec3(0, 4000, 3000),
            type="corbel",
            material=FrameMaterial.get_standard("16mm_plate"),
            connection_to_column="welded"
        )
    ]
)
```

## Component Generation Helpers

### Automatic Spacing

```python
from src.bim.helpers import ComponentSpacing

# Generate evenly spaced purlins
purlins = ComponentSpacing.create_linear_array(
    component_type=Purlin,
    start=Vec3(0, 0, 4200),
    end=Vec3(12000, 0, 5400),
    spacing=1200,
    material=FrameMaterial.get_standard("150PFC"),
    rotation_follows_slope=True
)

# Generate grid of components
grid_components = ComponentSpacing.create_grid_array(
    component_type=Column,
    origin=Vec3(0, 0, 0),
    x_count=5,
    y_count=4,
    x_spacing=3000,
    y_spacing=6000,
    height=3600,
    material=FrameMaterial.get_standard("200UC59")
)
```

### Component Validation

```python
from src.bim.validation import ComponentValidator

# Validate component placement
validator = ComponentValidator()

# Check for clashes
clashes = validator.check_clashes([column1, beam1, wall1])
for clash in clashes:
    print(f"Clash detected: {clash.component1.name} with {clash.component2.name}")

# Check structural connections
missing_connections = validator.check_connections(frame_assembly)

# Validate against building codes
code_violations = validator.check_building_codes(
    components=all_components,
    code="AS1170",
    region="cyclonic"
)
```

## Best Practices

1. **Use Meaningful Names**
   ```python
   # Good
   column = Column(position=pos, name="Grid_A1_Column")
   
   # Bad
   column = Column(position=pos, name="c1")
   ```

2. **Group Related Components**
   ```python
   # Create logical assemblies
   north_wall = Assembly("North Wall")
   north_wall.add_components([
       wall_panel_1,
       wall_panel_2,
       window_1,
       door_1
   ])
   ```

3. **Maintain Relationships**
   ```python
   # Keep track of connections
   beam.connects_to = [column1, column2]
   beam.supported_by = [column1, column2]
   beam.supports = [purlin1, purlin2, purlin3]
   ```

4. **Validate During Creation**
   ```python
   # Check validity immediately
   try:
       door = Door(width=5500, height=4500)  # Too large
   except ValueError as e:
       print(f"Invalid door: {e}")
   ```

## Summary

BIM components in PyModel provide:
- Rich object model for building elements
- Geometric and material integration
- Relationship management
- Validation capabilities
- Extensibility for custom components

You can now:
- Create any building component
- Manage complex assemblies
- Validate designs
- Generate complete building models

Next, explore [Generating Output Files](07_Generating_Output_Files.md) to learn how to export your BIM models to various formats.