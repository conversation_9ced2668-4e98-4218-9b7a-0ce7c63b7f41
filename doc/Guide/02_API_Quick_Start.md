# API Quick Start Guide

This guide will help you quickly start using the PyModel API for generating building models.

## Table of Contents
1. [Starting the API Server](#starting-the-api-server)
2. [Understanding the API](#understanding-the-api)
3. [Your First API Call](#your-first-api-call)
4. [Common API Endpoints](#common-api-endpoints)
5. [Working with Responses](#working-with-responses)
6. [<PERSON><PERSON><PERSON>ling](#error-handling)
7. [Advanced Usage](#advanced-usage)

## Starting the API Server

### Basic Start
```bash
# Activate virtual environment first
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Start the server
python run_api.py
```

### Development Mode (with auto-reload)
```bash
python run_api.py --reload
```

### Custom Port
```bash
python run_api.py --port 8080
```

The server will start and display:
```
INFO:     Uvicorn running on http://127.0.0.1:8000 (Press CTRL+C to quit)
INFO:     Started reloader process
INFO:     Started server process
INFO:     Waiting for application startup.
INFO:     Application startup complete.
```

## Understanding the API

### API Documentation

Once the server is running, visit:
- **Interactive Docs**: http://localhost:8000/docs (Swagger UI)
- **Alternative Docs**: http://localhost:8000/redoc (ReDoc)
- **OpenAPI Schema**: http://localhost:8000/openapi.json

### API Structure

The API follows RESTful principles:
- `GET` endpoints for retrieving data
- `POST` endpoints for creating/generating models
- `PUT` endpoints for updates
- `DELETE` endpoints for removal

### Authentication

The API uses token-based authentication for protected endpoints:
```python
headers = {
    "Authorization": "Bearer YOUR_API_TOKEN"
}
```

## Your First API Call

### Using cURL

```bash
# Health check
curl http://localhost:8000/health

# Generate a simple carport
curl -X POST http://localhost:8000/api/carport/generate \
  -H "Content-Type: application/json" \
  -d '{
    "width": 6000,
    "length": 9000,
    "height": 3000,
    "roof_type": "gable"
  }'
```

### Using Python

```python
import requests
import json

# API base URL
BASE_URL = "http://localhost:8000"

# 1. Health Check
response = requests.get(f"{BASE_URL}/health")
print(f"API Status: {response.json()}")

# 2. Generate Carport
carport_data = {
    "width": 6000,      # mm
    "length": 9000,     # mm
    "height": 3000,     # mm
    "roof_type": "gable",
    "roof_pitch": 15,   # degrees
    "bay_spacing": 3000,
    "materials": {
        "frame": "steel",
        "cladding": "colorbond",
        "color": "monument"
    }
}

response = requests.post(
    f"{BASE_URL}/api/carport/generate",
    json=carport_data
)

if response.status_code == 200:
    result = response.json()
    print(f"Model ID: {result['model_id']}")
    print(f"IFC File: {result['outputs']['ifc_url']}")
    print(f"GLB File: {result['outputs']['glb_url']}")
else:
    print(f"Error: {response.status_code} - {response.text}")
```

### Using JavaScript/Node.js

```javascript
const axios = require('axios');

const BASE_URL = 'http://localhost:8000';

// Generate carport
async function generateCarport() {
    const carportData = {
        width: 6000,
        length: 9000,
        height: 3000,
        roof_type: 'gable',
        roof_pitch: 15,
        materials: {
            frame: 'steel',
            cladding: 'colorbond'
        }
    };

    try {
        const response = await axios.post(
            `${BASE_URL}/api/carport/generate`,
            carportData
        );
        
        console.log('Model ID:', response.data.model_id);
        console.log('IFC URL:', response.data.outputs.ifc_url);
        console.log('GLB URL:', response.data.outputs.glb_url);
    } catch (error) {
        console.error('Error:', error.response.data);
    }
}

generateCarport();
```

## Common API Endpoints

### Building Generation

#### POST /api/carport/generate
Generate a carport model:
```python
{
    "width": 6000,          # Building width in mm
    "length": 9000,         # Building length in mm
    "height": 3000,         # Eave height in mm
    "roof_type": "gable",   # gable, skillion, flat
    "roof_pitch": 15,       # Roof angle in degrees
    "bay_spacing": 3000,    # Distance between frames
    "end_bay_type": "open", # open, closed
    "materials": {
        "frame": "steel",
        "cladding": "colorbond",
        "color": "monument"
    },
    "accessories": {
        "gutters": true,
        "downpipes": true,
        "ridge_capping": true
    }
}
```

#### POST /api/shed/generate
Generate a shed model (extended parameters):
```python
{
    # Basic dimensions (same as carport)
    "width": 12000,
    "length": 18000,
    "height": 4000,
    
    # Shed-specific
    "wall_height": 3000,
    "openings": [
        {
            "type": "roller_door",
            "wall": "front",
            "width": 3000,
            "height": 3000,
            "position": 6000  # Distance from left
        },
        {
            "type": "pa_door",
            "wall": "left",
            "width": 900,
            "height": 2100,
            "position": 3000
        }
    ],
    "mezzanine": {
        "enabled": true,
        "area": 40,  # m²
        "height": 2400
    }
}
```

### Model Management

#### GET /api/models/{model_id}
Retrieve model information:
```python
response = requests.get(f"{BASE_URL}/api/models/{model_id}")
model_info = response.json()
```

#### GET /api/models/{model_id}/download/{file_type}
Download generated files:
```python
# Download IFC file
response = requests.get(
    f"{BASE_URL}/api/models/{model_id}/download/ifc",
    stream=True
)
with open("model.ifc", "wb") as f:
    f.write(response.content)

# Download GLB file
response = requests.get(
    f"{BASE_URL}/api/models/{model_id}/download/glb",
    stream=True
)
with open("model.glb", "wb") as f:
    f.write(response.content)
```

### Validation Endpoints

#### POST /api/validate/dimensions
Validate building dimensions:
```python
validation_data = {
    "width": 25000,
    "length": 50000,
    "height": 6000,
    "building_type": "shed"
}

response = requests.post(
    f"{BASE_URL}/api/validate/dimensions",
    json=validation_data
)
```

#### POST /api/validate/engineering
Check engineering constraints:
```python
engineering_data = {
    "model_id": "12345",
    "wind_region": "C",
    "terrain_category": 2,
    "importance_level": 2
}

response = requests.post(
    f"{BASE_URL}/api/validate/engineering",
    json=engineering_data
)
```

## Working with Responses

### Successful Response Structure
```python
{
    "success": true,
    "model_id": "uuid-string",
    "message": "Model generated successfully",
    "outputs": {
        "ifc_url": "/api/models/uuid/download/ifc",
        "glb_url": "/api/models/uuid/download/glb",
        "dxf_url": "/api/models/uuid/download/dxf",
        "csv_url": "/api/models/uuid/download/csv"
    },
    "metadata": {
        "generation_time": 2.34,  # seconds
        "file_sizes": {
            "ifc": 125000,  # bytes
            "glb": 89000
        },
        "version": "1.0.0"
    }
}
```

### Polling for Long Operations
```python
import time

def wait_for_model(model_id, timeout=60):
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        response = requests.get(f"{BASE_URL}/api/models/{model_id}/status")
        status = response.json()
        
        if status["state"] == "completed":
            return status
        elif status["state"] == "failed":
            raise Exception(f"Model generation failed: {status['error']}")
        
        time.sleep(2)  # Poll every 2 seconds
    
    raise TimeoutError("Model generation timed out")
```

## Error Handling

### Common Error Responses

#### 400 Bad Request
Invalid input parameters:
```python
{
    "success": false,
    "error": "ValidationError",
    "message": "Invalid input parameters",
    "details": {
        "width": "Width must be between 3000 and 30000 mm",
        "roof_type": "Invalid roof type. Must be one of: gable, skillion, flat"
    }
}
```

#### 404 Not Found
Resource not found:
```python
{
    "success": false,
    "error": "NotFoundError",
    "message": "Model not found",
    "model_id": "invalid-uuid"
}
```

#### 500 Internal Server Error
Server error:
```python
{
    "success": false,
    "error": "InternalError",
    "message": "An unexpected error occurred",
    "request_id": "req-12345"
}
```

### Error Handling Best Practices

```python
def safe_api_call(endpoint, method="GET", data=None):
    try:
        if method == "GET":
            response = requests.get(f"{BASE_URL}{endpoint}")
        elif method == "POST":
            response = requests.post(f"{BASE_URL}{endpoint}", json=data)
        
        # Check HTTP status
        response.raise_for_status()
        
        # Parse JSON
        result = response.json()
        
        # Check API-level success
        if not result.get("success", True):
            print(f"API Error: {result.get('message')}")
            return None
            
        return result
        
    except requests.exceptions.HTTPError as e:
        print(f"HTTP Error: {e}")
        if e.response:
            try:
                error_data = e.response.json()
                print(f"Details: {error_data}")
            except:
                print(f"Response: {e.response.text}")
                
    except requests.exceptions.ConnectionError:
        print("Failed to connect to API server")
        
    except Exception as e:
        print(f"Unexpected error: {e}")
        
    return None
```

## Advanced Usage

### Batch Processing

```python
def generate_multiple_carports(configurations):
    results = []
    
    for i, config in enumerate(configurations):
        print(f"Generating carport {i+1}/{len(configurations)}...")
        
        response = requests.post(
            f"{BASE_URL}/api/carport/generate",
            json=config
        )
        
        if response.status_code == 200:
            results.append(response.json())
        else:
            results.append({"error": response.text, "config": config})
            
        # Rate limiting
        time.sleep(1)
    
    return results

# Example usage
configs = [
    {"width": 6000, "length": 9000, "height": 3000, "roof_type": "gable"},
    {"width": 7000, "length": 12000, "height": 3500, "roof_type": "skillion"},
    {"width": 8000, "length": 15000, "height": 4000, "roof_type": "flat"}
]

results = generate_multiple_carports(configs)
```

### Async Operations

```python
import asyncio
import aiohttp

async def generate_carport_async(session, config):
    async with session.post(
        f"{BASE_URL}/api/carport/generate",
        json=config
    ) as response:
        return await response.json()

async def generate_many_async(configurations):
    async with aiohttp.ClientSession() as session:
        tasks = [
            generate_carport_async(session, config)
            for config in configurations
        ]
        return await asyncio.gather(*tasks)

# Run async
results = asyncio.run(generate_many_async(configs))
```

### WebSocket for Real-time Updates

```python
import asyncio
import websockets
import json

async def monitor_generation(model_id):
    uri = f"ws://localhost:8000/ws/models/{model_id}"
    
    async with websockets.connect(uri) as websocket:
        while True:
            message = await websocket.recv()
            data = json.loads(message)
            
            print(f"Progress: {data['progress']}%")
            print(f"Status: {data['status']}")
            
            if data['status'] in ['completed', 'failed']:
                break
```

## Next Steps

Now that you understand the basics:

1. Try [Building Your First Model](03_Building_First_Model.md) for a complete tutorial
2. Explore [API Reference](../API_Reference.md) for all endpoints
3. Learn about [Extending Building Types](08_Extending_Building_Types.md)

## Tips for API Usage

1. **Always validate input** before sending to API
2. **Handle errors gracefully** - don't assume success
3. **Use appropriate timeouts** for long operations
4. **Implement retry logic** for network failures
5. **Cache results** when appropriate
6. **Monitor rate limits** if applicable
7. **Use compression** for large payloads
8. **Log requests** for debugging