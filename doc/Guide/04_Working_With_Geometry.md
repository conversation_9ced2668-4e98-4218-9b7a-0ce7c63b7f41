# Working with Geometry - Understanding 3D Mathematics in PyModel

This guide explains the geometry system used in PyModel for 3D modeling and transformations.

## Table of Contents
1. [Coordinate System](#coordinate-system)
2. [Basic Types](#basic-types)
3. [Transformations](#transformations)
4. [Practical Examples](#practical-examples)
5. [Common Operations](#common-operations)
6. [Geometry in Building Context](#geometry-in-building-context)
7. [Advanced Topics](#advanced-topics)

## Coordinate System

PyModel uses a **right-handed coordinate system**:
- **X-axis**: Points to the right (width direction)
- **Y-axis**: Points forward/away (length direction)  
- **Z-axis**: Points up (height direction)

```
      Z (up)
      |
      |
      +------ X (right)
     /
    /
   Y (forward)
```

### Building Orientation

In building context:
- **Width** is measured along X-axis
- **Length** is measured along Y-axis
- **Height** is measured along Z-axis
- Origin (0,0,0) is typically at front-left corner at ground level

## Basic Types

### Vec2 - 2D Vector

Used for 2D calculations, floor plans, and projections:

```python
from src.geometry import Vec2

# Creating 2D vectors
point = Vec2(100, 200)  # x=100, y=200
origin = Vec2.zero()    # (0, 0)

# Basic operations
v1 = Vec2(3, 4)
v2 = Vec2(1, 2)

# Addition and subtraction
sum_v = v1 + v2        # Vec2(4, 6)
diff_v = v1 - v2       # Vec2(2, 2)

# Scalar operations
scaled = v1 * 2        # Vec2(6, 8)
divided = v1 / 2       # Vec2(1.5, 2)

# Length and distance
length = v1.length()   # 5.0 (sqrt(3² + 4²))
distance = Vec2.distance(v1, v2)  # 2.236

# Dot product and angle
dot = Vec2.dot(v1, v2)  # 11.0
angle = Vec2.angle_between(v1, v2)  # radians

# Normalization
normalized = v1.normalize()  # Unit vector
```

### Vec3 - 3D Vector

Used for all 3D positions and directions:

```python
from src.geometry import Vec3

# Creating 3D vectors
pos = Vec3(1000, 2000, 3000)  # x, y, z in millimeters
up = Vec3.unit_z()             # (0, 0, 1)
right = Vec3.unit_x()          # (1, 0, 0)
forward = Vec3.unit_y()        # (0, 1, 0)

# Vector operations
v1 = Vec3(1, 2, 3)
v2 = Vec3(4, 5, 6)

# Basic math
sum_v = v1 + v2               # Vec3(5, 7, 9)
cross = Vec3.cross(v1, v2)    # Cross product
dot = Vec3.dot(v1, v2)        # Dot product

# Useful methods
length = v1.length()           # Magnitude
normalized = v1.normalize()    # Unit vector
distance = Vec3.distance(v1, v2)
midpoint = Vec3.lerp(v1, v2, 0.5)  # Linear interpolation
```

### Mat4 - 4x4 Transformation Matrix

Used for transformations (rotation, translation, scaling):

```python
from src.geometry import Mat4, Vec3
import math

# Identity matrix (no transformation)
identity = Mat4.identity()

# Translation
translation = Mat4.create_translation(100, 200, 300)
# or
translation = Mat4.create_translation_v(Vec3(100, 200, 300))

# Rotation (in radians)
rot_x = Mat4.create_rotation_x(math.radians(45))  # 45 degrees
rot_y = Mat4.create_rotation_y(math.radians(90))  # 90 degrees
rot_z = Mat4.create_rotation_z(math.radians(30))  # 30 degrees

# Scaling
scale = Mat4.create_scale(2, 2, 2)  # Uniform scale by 2
scale_xyz = Mat4.create_scale(1.5, 1.0, 2.0)  # Non-uniform

# Combining transformations (order matters!)
# First rotate, then translate
transform = translation * rot_z

# Transform a point
point = Vec3(100, 0, 0)
transformed = transform.transform_position(point)

# Transform a direction (ignores translation)
direction = Vec3(1, 0, 0)
rotated = transform.transform_direction(direction)
```

## Transformations

### Creating Building Transformations

```python
from src.geometry import Mat4, Vec3, Angle

def position_column(x, y, height):
    """Position a column at specific location"""
    # Create column at origin
    base_position = Vec3(0, 0, 0)
    
    # Move to desired position
    translation = Mat4.create_translation(x, y, 0)
    
    # Create column vector (pointing up)
    column_top = translation.transform_position(Vec3(0, 0, height))
    
    return base_position, column_top

def rotate_beam(beam_start, beam_end, angle_degrees):
    """Rotate a beam around its start point"""
    # Calculate beam center
    center = Vec3.lerp(beam_start, beam_end, 0.5)
    
    # Translate to origin
    to_origin = Mat4.create_translation_v(-beam_start)
    
    # Rotate
    rotation = Mat4.create_rotation_z(math.radians(angle_degrees))
    
    # Translate back
    from_origin = Mat4.create_translation_v(beam_start)
    
    # Combined transformation
    transform = from_origin * rotation * to_origin
    
    # Apply to end point
    new_end = transform.transform_position(beam_end)
    
    return beam_start, new_end
```

### Positioning Building Components

```python
def create_building_frame(width, length, height, bay_spacing):
    """Create positions for building frame"""
    frames = []
    
    # Number of frames
    num_frames = int(length / bay_spacing) + 1
    
    for i in range(num_frames):
        y_position = i * bay_spacing
        
        # Left column
        left_column = {
            "base": Vec3(0, y_position, 0),
            "top": Vec3(0, y_position, height)
        }
        
        # Right column
        right_column = {
            "base": Vec3(width, y_position, 0),
            "top": Vec3(width, y_position, height)
        }
        
        # Rafter (beam across top)
        rafter = {
            "start": left_column["top"],
            "end": right_column["top"]
        }
        
        frames.append({
            "left_column": left_column,
            "right_column": right_column,
            "rafter": rafter,
            "position": y_position
        })
    
    return frames
```

## Practical Examples

### Example 1: Calculating Roof Geometry

```python
def calculate_gable_roof(width, length, eave_height, pitch_degrees):
    """Calculate gable roof geometry"""
    # Convert pitch to radians
    pitch_rad = math.radians(pitch_degrees)
    
    # Calculate ridge height
    half_width = width / 2
    ridge_height = eave_height + half_width * math.tan(pitch_rad)
    
    # Define key points
    points = {
        "eave_left_front": Vec3(0, 0, eave_height),
        "eave_right_front": Vec3(width, 0, eave_height),
        "eave_left_back": Vec3(0, length, eave_height),
        "eave_right_back": Vec3(width, length, eave_height),
        "ridge_front": Vec3(half_width, 0, ridge_height),
        "ridge_back": Vec3(half_width, length, ridge_height)
    }
    
    # Create roof planes
    left_plane = Plane3.from_three_points(
        points["eave_left_front"],
        points["eave_left_back"],
        points["ridge_front"]
    )
    
    right_plane = Plane3.from_three_points(
        points["eave_right_front"],
        points["ridge_front"],
        points["eave_right_back"]
    )
    
    return points, left_plane, right_plane
```

### Example 2: Positioning Doors and Windows

```python
def position_opening(wall, opening_width, opening_height, position_along_wall, sill_height=0):
    """Calculate opening position on a wall"""
    
    # Wall definitions
    walls = {
        "front": {"normal": Vec3(0, -1, 0), "origin": Vec3(0, 0, 0)},
        "back": {"normal": Vec3(0, 1, 0), "origin": Vec3(0, length, 0)},
        "left": {"normal": Vec3(-1, 0, 0), "origin": Vec3(0, 0, 0)},
        "right": {"normal": Vec3(1, 0, 0), "origin": Vec3(width, 0, 0)}
    }
    
    wall_data = walls[wall]
    
    # Calculate opening corners based on wall
    if wall == "front" or wall == "back":
        # Opening on front/back wall (parallel to X axis)
        y = wall_data["origin"].y
        bottom_left = Vec3(position_along_wall, y, sill_height)
        bottom_right = Vec3(position_along_wall + opening_width, y, sill_height)
        top_left = Vec3(position_along_wall, y, sill_height + opening_height)
        top_right = Vec3(position_along_wall + opening_width, y, sill_height + opening_height)
    else:
        # Opening on left/right wall (parallel to Y axis)
        x = wall_data["origin"].x
        bottom_left = Vec3(x, position_along_wall, sill_height)
        bottom_right = Vec3(x, position_along_wall + opening_width, sill_height)
        top_left = Vec3(x, position_along_wall, sill_height + opening_height)
        top_right = Vec3(x, position_along_wall + opening_width, sill_height + opening_height)
    
    return {
        "bottom_left": bottom_left,
        "bottom_right": bottom_right,
        "top_left": top_left,
        "top_right": top_right,
        "center": Vec3.lerp(bottom_left, top_right, 0.5),
        "normal": wall_data["normal"]
    }
```

### Example 3: Creating Curved Elements

```python
def create_curved_beam(center, radius, start_angle, end_angle, height, segments=20):
    """Create a curved beam (like for a curved roof edge)"""
    points = []
    
    angle_step = (end_angle - start_angle) / segments
    
    for i in range(segments + 1):
        angle = start_angle + i * angle_step
        x = center.x + radius * math.cos(angle)
        y = center.y + radius * math.sin(angle)
        z = height
        
        points.append(Vec3(x, y, z))
    
    # Create line segments
    segments = []
    for i in range(len(points) - 1):
        segments.append(Line3(points[i], points[i + 1]))
    
    return points, segments
```

## Common Operations

### Distance and Intersection Calculations

```python
from src.geometry import Line3, Plane3, Box3

# Check if point is inside building
def point_in_building(point, building_width, building_length, building_height):
    building_box = Box3(
        Vec3(0, 0, 0),
        Vec3(building_width, building_length, building_height)
    )
    return building_box.contains(point)

# Find intersection of beam with plane
def beam_plane_intersection(beam_start, beam_end, plane):
    beam_line = Line3(beam_start, beam_end)
    intersection = plane.intersect_line(beam_line)
    
    if intersection:
        # Check if intersection is within beam segment
        t = Line3.closest_point_t(beam_line, intersection)
        if 0 <= t <= 1:
            return intersection
    
    return None

# Calculate minimum distance between elements
def min_distance_between_columns(col1_base, col1_top, col2_base, col2_top):
    line1 = Line3(col1_base, col1_top)
    line2 = Line3(col2_base, col2_top)
    
    return Line3.distance_between(line1, line2)
```

### Bounding Box Operations

```python
def calculate_building_bounds(components):
    """Calculate overall bounding box of building"""
    all_points = []
    
    for component in components:
        if hasattr(component, 'get_vertices'):
            all_points.extend(component.get_vertices())
        elif hasattr(component, 'position'):
            all_points.append(component.position)
    
    if not all_points:
        return None
    
    # Create bounding box from points
    min_point = Vec3(
        min(p.x for p in all_points),
        min(p.y for p in all_points),
        min(p.z for p in all_points)
    )
    
    max_point = Vec3(
        max(p.x for p in all_points),
        max(p.y for p in all_points),
        max(p.z for p in all_points)
    )
    
    return Box3(min_point, max_point)
```

## Geometry in Building Context

### Coordinate Conventions

```python
# Standard building component positions
class BuildingGeometry:
    @staticmethod
    def column_positions(width, length, bay_spacing):
        """Standard column positions for a building"""
        positions = []
        
        # Calculate number of bays
        num_bays_length = int(length / bay_spacing)
        
        for i in range(num_bays_length + 1):
            y = i * bay_spacing
            
            # Perimeter columns
            positions.extend([
                Vec3(0, y, 0),      # Left side
                Vec3(width, y, 0),  # Right side
            ])
        
        # End wall columns (if not already added)
        num_bays_width = int(width / bay_spacing)
        for i in range(1, num_bays_width):
            x = i * bay_spacing
            positions.extend([
                Vec3(x, 0, 0),      # Front wall
                Vec3(x, length, 0), # Back wall
            ])
        
        return positions
    
    @staticmethod
    def purlin_lines(width, length, eave_height, pitch_degrees, purlin_spacing):
        """Calculate purlin positions on a gable roof"""
        lines = []
        
        # Calculate ridge height
        ridge_height = eave_height + (width / 2) * math.tan(math.radians(pitch_degrees))
        ridge_line = Line3(
            Vec3(width / 2, 0, ridge_height),
            Vec3(width / 2, length, ridge_height)
        )
        
        # Left slope purlins
        left_run = width / 2
        left_rise = ridge_height - eave_height
        left_slope_length = math.sqrt(left_run**2 + left_rise**2)
        
        num_purlins = int(left_slope_length / purlin_spacing)
        
        for i in range(1, num_purlins):
            t = i / num_purlins
            x = t * width / 2
            z = eave_height + t * left_rise
            
            lines.append(Line3(
                Vec3(x, 0, z),
                Vec3(x, length, z)
            ))
        
        # Mirror for right slope
        for i in range(1, num_purlins):
            t = i / num_purlins
            x = width - (t * width / 2)
            z = eave_height + t * left_rise
            
            lines.append(Line3(
                Vec3(x, 0, z),
                Vec3(x, length, z)
            ))
        
        return lines, ridge_line
```

## Advanced Topics

### Custom Transformations

```python
def create_shear_transform(shear_xy, shear_xz, shear_yz):
    """Create a shear transformation matrix"""
    m = Mat4.identity()
    m.m[0][1] = shear_xy  # Shear X along Y
    m.m[0][2] = shear_xz  # Shear X along Z
    m.m[1][2] = shear_yz  # Shear Y along Z
    return m

def create_look_at(eye, target, up=Vec3.unit_z()):
    """Create view matrix looking from eye to target"""
    forward = (target - eye).normalize()
    right = Vec3.cross(forward, up).normalize()
    up = Vec3.cross(right, forward).normalize()
    
    m = Mat4.identity()
    m.m[0][0] = right.x
    m.m[0][1] = right.y
    m.m[0][2] = right.z
    m.m[1][0] = up.x
    m.m[1][1] = up.y
    m.m[1][2] = up.z
    m.m[2][0] = -forward.x
    m.m[2][1] = -forward.y
    m.m[2][2] = -forward.z
    m.m[3][0] = -Vec3.dot(right, eye)
    m.m[3][1] = -Vec3.dot(up, eye)
    m.m[3][2] = Vec3.dot(forward, eye)
    
    return m
```

### Performance Tips

1. **Reuse Transformations**
   ```python
   # Bad - recalculating each time
   for point in points:
       transformed = Mat4.create_rotation_z(angle) * point
   
   # Good - calculate once
   rotation = Mat4.create_rotation_z(angle)
   for point in points:
       transformed = rotation * point
   ```

2. **Use Appropriate Types**
   ```python
   # Use Vec2 for 2D operations
   floor_point = Vec2(x, y)  # More efficient than Vec3(x, y, 0)
   
   # Use Box3 for bounds checking
   bounds = Box3(min_point, max_point)
   if bounds.contains(test_point):  # Faster than manual checks
       # ...
   ```

3. **Batch Operations**
   ```python
   # Transform multiple points efficiently
   def transform_points(points, transform):
       return [transform.transform_position(p) for p in points]
   ```

## Summary

The geometry system in PyModel provides:
- Comprehensive 2D/3D vector mathematics
- Powerful transformation matrices
- Building-specific coordinate conventions
- Efficient geometric operations

Understanding these fundamentals allows you to:
- Position building components accurately
- Calculate complex roof geometries
- Handle openings and intersections
- Create custom building shapes

Next, explore [Understanding Materials](05_Understanding_Materials.md) to learn how geometry combines with materials to create complete building elements.