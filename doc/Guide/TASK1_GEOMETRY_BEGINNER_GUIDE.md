# Task 1: Mathematical Foundation - Complete Beginner's Guide

## Table of Contents
1. [Introduction](#introduction)
2. [Core Concepts](#core-concepts)
3. [Vec2 - 2D Vectors](#vec2---2d-vectors)
4. [Vec3 - 3D Vectors](#vec3---3d-vectors)
5. [Lines in Different Dimensions](#lines)
6. [Boxes and Boundaries](#boxes)
7. [Matrix Transformations](#matrix-transformations)
8. [Advanced Concepts](#advanced-concepts)
9. [Real-World Applications](#real-world-applications)

## Introduction

The geometry module is the mathematical heart of the BIM (Building Information Modeling) system. Think of it as the foundation that allows us to:
- Position things in 2D and 3D space
- Calculate distances and angles
- Transform objects (move, rotate, scale)
- Detect intersections and collisions
- Define boundaries and regions

### Why is this important for buildings?
When designing a carport or shed, we need to:
- Position columns at exact coordinates
- Calculate roof angles and rafter lengths
- Ensure beams intersect correctly
- Transform components into their final positions

## Core Concepts

### What is a Vector?
A vector is simply a point in space or a direction. Think of it like GPS coordinates:
- **Vec2(5, 3)** = 5 units right, 3 units forward (like a map)
- **Vec3(5, 3, 2)** = 5 units right, 3 units forward, 2 units up (like real world)

### Coordinate System
We use a right-handed coordinate system:
```
     Y (up)
     |
     |
     +-----> X (right)
    /
   /
  Z (forward/out of screen)
```

## Vec2 - 2D Vectors

Let's examine Vec2 line by line:

```python
@dataclass
class Vec2:
    """A 2D vector representing a point or direction in 2D space.
    
    This is like a point on a blueprint or map.
    """
    x: float  # Horizontal position (left/right)
    y: float  # Vertical position (up/down)
```

### Essential Methods Explained

#### 1. Creating Vectors
```python
# Different ways to create a Vec2
p1 = Vec2(3, 4)           # Point at x=3, y=4
p2 = Vec2.zero()          # Origin point (0, 0)
p3 = Vec2.from_angle(45)  # Point on unit circle at 45 degrees
```

#### 2. Length (Distance from Origin)
```python
def length(self) -> float:
    """Calculate how far this point is from (0,0).
    
    Uses Pythagorean theorem: √(x² + y²)
    
    Example: Vec2(3, 4).length() = 5
    Because: √(3² + 4²) = √(9 + 16) = √25 = 5
    """
    return math.sqrt(self.x * self.x + self.y * self.y)
```

#### 3. Distance Between Points
```python
@staticmethod
def distance(a: 'Vec2', b: 'Vec2') -> float:
    """How far apart are two points?
    
    Example: Distance from (1,1) to (4,5)
    dx = 4 - 1 = 3
    dy = 5 - 1 = 4
    distance = √(3² + 4²) = 5
    """
    dx = b.x - a.x
    dy = b.y - a.y
    return math.sqrt(dx * dx + dy * dy)
```

#### 4. Dot Product (Measuring Alignment)
```python
@staticmethod
def dot(a: 'Vec2', b: 'Vec2') -> float:
    """Measures how much two vectors point in the same direction.
    
    Result meanings:
    - Positive: vectors point similarly
    - Zero: vectors are perpendicular (90°)
    - Negative: vectors point opposite ways
    
    Formula: a.x * b.x + a.y * b.y
    """
    return a.x * b.x + a.y * b.y
```

#### 5. Angle Calculation
```python
def angle(self) -> float:
    """What angle does this vector make with the X-axis?
    
    Returns angle in radians using atan2.
    atan2 handles all quadrants correctly.
    
    Example: Vec2(1, 1).angle() = π/4 (45 degrees)
    """
    return math.atan2(self.y, self.x)
```

### Real Building Example
```python
# Calculating roof truss positions
building_width = 6.0  # 6 meters
truss_spacing = 1.2   # 1.2 meters between trusses

# Generate truss positions along the building
truss_positions = []
for i in range(int(building_width / truss_spacing) + 1):
    x = i * truss_spacing
    position = Vec2(x, 0)  # Trusses at ground level
    truss_positions.append(position)

# Result: Trusses at x = 0, 1.2, 2.4, 3.6, 4.8, 6.0
```

## Vec3 - 3D Vectors

Vec3 extends the concept to 3D space:

```python
@dataclass
class Vec3:
    """A 3D vector for real-world positioning."""
    x: float  # Left/Right
    y: float  # Up/Down (height)
    z: float  # Forward/Back (depth)
```

### Key 3D Operations

#### 1. Cross Product (Finding Perpendicular)
```python
@staticmethod
def cross(a: 'Vec3', b: 'Vec3') -> 'Vec3':
    """Find a vector perpendicular to both input vectors.
    
    Used for:
    - Calculating surface normals
    - Finding rotation axes
    
    The formula uses the determinant:
    | i   j   k  |
    | a.x a.y a.z |
    | b.x b.y b.z |
    """
    return Vec3(
        a.y * b.z - a.z * b.y,  # i component
        a.z * b.x - a.x * b.z,  # j component
        a.x * b.y - a.y * b.x   # k component
    )
```

#### 2. Building a Coordinate System
```python
@staticmethod
def make_basis(primary: 'Vec3', secondary: 'Vec3') -> Tuple['Vec3', 'Vec3', 'Vec3']:
    """Create a coordinate system from two directions.
    
    Example: Roof surface
    - primary = rafter direction
    - secondary = approximate up direction
    Returns: (rafter_dir, across_rafters, surface_normal)
    """
    x = Vec3.normal(primary)              # Normalize primary
    z = Vec3.normal(Vec3.cross(x, secondary))  # Perpendicular to both
    y = Vec3.cross(z, x)                  # Complete the system
    return (x, y, z)
```

### Real Building Example
```python
# Calculate roof rafter for a gable roof
span = 6.0      # Building width
height = 2.5    # Ridge height
eave_height = 2.4

# Rafter goes from eave to ridge
eave_point = Vec3(0, eave_height, 0)
ridge_point = Vec3(span/2, height, 0)

# Rafter vector and length
rafter_vector = ridge_point - eave_point
rafter_length = rafter_vector.length()

print(f"Rafter length: {rafter_length:.3f} meters")
```

## Lines

Lines represent infinite straight paths in space. We have three types:

### Line1 - Lines on a Number Line
```python
@dataclass
class Line1:
    """A line segment on a 1D number line.
    
    Used for ranges, like "posts from 0m to 9m"
    """
    a: float  # Start point
    b: float  # End point
    
    def length(self) -> float:
        """How long is this segment?"""
        return abs(self.b - self.a)
    
    def contains(self, t: float) -> bool:
        """Is the point t inside this range?"""
        return min(self.a, self.b) <= t <= max(self.a, self.b)
```

### Line2 - Lines in 2D
```python
@dataclass
class Line2:
    """A line in 2D space defined by two points."""
    p: Vec2  # Starting point
    q: Vec2  # Ending point
    
    def closest_point(self, point: Vec2) -> Vec2:
        """Find the nearest point on this line to the given point.
        
        Uses parametric form: point_on_line = p + t*(q-p)
        where t is clamped to [0,1] to stay on the segment.
        """
        v = self.q - self.p
        t = Vec2.dot(point - self.p, v) / Vec2.dot(v, v)
        t = max(0, min(1, t))  # Clamp to segment
        return self.p + v * t
```

### Line3 - Lines in 3D
```python
@dataclass  
class Line3:
    """A line in 3D space."""
    p: Vec3  # Starting point
    q: Vec3  # Ending point
    
    def intersect_plane(self, plane: 'Plane3') -> Optional[Vec3]:
        """Where does this line hit a plane?
        
        Used for:
        - Calculating where rafters meet walls
        - Finding roof/wall intersections
        """
        # Detailed intersection math...
```

## Boxes and Boundaries

Boxes define rectangular regions in 2D or 3D:

### Box2 - 2D Boundaries
```python
@dataclass
class Box2:
    """A 2D rectangular region (like a floor plan)."""
    min: Vec2  # Bottom-left corner
    max: Vec2  # Top-right corner
    
    def contains(self, point: Vec2) -> bool:
        """Is this point inside the rectangle?"""
        return (self.min.x <= point.x <= self.max.x and
                self.min.y <= point.y <= self.max.y)
    
    def area(self) -> float:
        """Calculate the area of this rectangle."""
        return self.width() * self.height()
```

### Box3 - 3D Boundaries
```python
@dataclass
class Box3:
    """A 3D box (like a building envelope)."""
    min: Vec3  # Minimum corner
    max: Vec3  # Maximum corner
    
    def volume(self) -> float:
        """Calculate volume of this box."""
        return self.width() * self.height() * self.depth()
```

### Real Building Example
```python
# Define building envelope
building = Box3(
    min=Vec3(0, 0, 0),        # Ground corner
    max=Vec3(6, 2.7, 9)       # Opposite corner
)

print(f"Building volume: {building.volume()} m³")
print(f"Footprint area: {building.width() * building.depth()} m²")
```

## Matrix Transformations

Mat4 handles all 3D transformations using 4x4 matrices:

```python
class Mat4:
    """4x4 transformation matrix for 3D operations.
    
    Why 4x4 for 3D? The extra dimension allows us to 
    combine rotation, scaling, and translation in one matrix.
    """
```

### Common Transformations

#### 1. Translation (Moving)
```python
@staticmethod
def translation(x: float, y: float, z: float) -> 'Mat4':
    """Create a matrix that moves objects.
    
    Matrix form:
    [1  0  0  x]
    [0  1  0  y]  
    [0  0  1  z]
    [0  0  0  1]
    """
```

#### 2. Rotation
```python
@staticmethod
def rotation_x(angle: float) -> 'Mat4':
    """Rotate around X-axis (tilt forward/back).
    
    Matrix uses cos and sin of angle:
    [1   0      0    0]
    [0   cos   -sin  0]
    [0   sin    cos  0]
    [0   0      0    1]
    """
```

#### 3. Scaling
```python
@staticmethod
def scale(sx: float, sy: float, sz: float) -> 'Mat4':
    """Scale along each axis.
    
    Matrix form:
    [sx  0   0   0]
    [0   sy  0   0]
    [0   0   sz  0]
    [0   0   0   1]
    """
```

### Combining Transformations
```python
# Position a roof truss
def position_truss(truss_index: int, building_length: float):
    spacing = 1.2  # 1.2m between trusses
    
    # Start with identity (no transformation)
    transform = Mat4.identity()
    
    # Move to correct position along building
    position = truss_index * spacing
    transform = transform * Mat4.translation(0, 0, position)
    
    # Rotate if needed (e.g., for hip roof)
    # transform = transform * Mat4.rotation_y(angle)
    
    return transform
```

## Advanced Concepts

### 1. Plane3 - Infinite Flat Surfaces
```python
@dataclass
class Plane3:
    """An infinite flat surface in 3D space.
    
    Defined by: ax + by + cz + d = 0
    where (a,b,c) is the normal vector.
    """
    normal: Vec3  # Direction perpendicular to plane
    d: float      # Distance from origin
    
    def distance_to(self, point: Vec3) -> float:
        """How far is a point from this plane?
        
        Positive = in front of plane
        Zero = on the plane
        Negative = behind plane
        """
        return Vec3.dot(self.normal, point) + self.d
```

### 2. Basis3 - Coordinate Systems
```python
@dataclass
class Basis3:
    """A local coordinate system in 3D space.
    
    Used for:
    - Defining roof slopes
    - Orienting building components
    - Converting between coordinate systems
    """
    x: Vec3  # Local X-axis (right)
    y: Vec3  # Local Y-axis (up)
    z: Vec3  # Local Z-axis (forward)
```

### 3. TriIndex - Triangle Definition
```python
@dataclass
class TriIndex:
    """Defines a triangle by three vertex indices.
    
    Used in 3D mesh generation for visualization.
    """
    i1: int  # First vertex index
    i2: int  # Second vertex index
    i3: int  # Third vertex index
```

## Real-World Applications

### 1. Calculating Roof Area
```python
def calculate_gable_roof_area(width: float, length: float, pitch_degrees: float):
    """Calculate total roof area for a gable roof."""
    
    # Convert pitch to radians
    pitch_rad = math.radians(pitch_degrees)
    
    # Calculate rafter length (from eave to ridge)
    rafter_length = (width / 2) / math.cos(pitch_rad)
    
    # Total area = 2 sides
    area = 2 * rafter_length * length
    
    return area

# Example: 6m x 9m building with 15° pitch
area = calculate_gable_roof_area(6.0, 9.0, 15)
print(f"Roof area: {area:.2f} m²")
```

### 2. Post Positioning
```python
def generate_post_positions(width: float, length: float, max_spacing: float):
    """Generate optimal post positions for a structure."""
    
    posts = []
    
    # Calculate number of bays
    bays_width = math.ceil(width / max_spacing)
    bays_length = math.ceil(length / max_spacing)
    
    # Actual spacing
    spacing_w = width / bays_width
    spacing_l = length / bays_length
    
    # Generate grid of posts
    for i in range(bays_width + 1):
        for j in range(bays_length + 1):
            x = i * spacing_w
            z = j * spacing_l
            posts.append(Vec3(x, 0, z))
    
    return posts
```

### 3. Beam Intersection Check
```python
def check_beam_collision(beam1_start: Vec3, beam1_end: Vec3,
                        beam2_start: Vec3, beam2_end: Vec3) -> bool:
    """Check if two beams intersect in 3D space."""
    
    line1 = Line3(beam1_start, beam1_end)
    line2 = Line3(beam2_start, beam2_end)
    
    # Check if lines are close enough to intersect
    # (accounting for beam thickness)
    closest = line1.closest_point_to_line(line2)
    return closest[2] < 0.1  # Within 10cm
```

## Summary

The geometry module provides:
1. **Basic Types**: Vec2, Vec3 for positions and directions
2. **Linear Objects**: Lines and line segments in 1D, 2D, 3D
3. **Regions**: Boxes for boundaries and collision detection
4. **Transformations**: Mat4 for moving, rotating, scaling
5. **Advanced**: Planes, coordinate systems, triangles

Each component builds on the previous, creating a complete mathematical framework for 3D building design and analysis.