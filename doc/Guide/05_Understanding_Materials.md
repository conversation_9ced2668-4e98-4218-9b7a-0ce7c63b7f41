# Understanding Materials - Complete Guide to PyModel Material System

This guide explains how materials work in PyModel, from basic concepts to advanced customization.

## Table of Contents
1. [Material System Overview](#material-system-overview)
2. [Material Types](#material-types)
3. [Working with Materials](#working-with-materials)
4. [Material Profiles](#material-profiles)
5. [Visual Properties](#visual-properties)
6. [Material Segments](#material-segments)
7. [Practical Examples](#practical-examples)
8. [Custom Materials](#custom-materials)

## Material System Overview

The PyModel material system defines:
- **What** something is made of (steel, timber, concrete)
- **How** it looks (color, finish, texture)
- **Shape** of the material (profiles like C-sections, tubes)
- **Properties** for engineering (weight, strength)

### Material Hierarchy

```
Material (Base)
├── FrameMaterial (Structural elements)
│   ├── Steel sections (UB, UC, PFC, SHS, CHS)
│   ├── Timber sections
│   └── Concrete elements
├── CladdingMaterial (Sheeting)
│   ├── Profiles (Corrugated, Trimdek, etc.)
│   └── Colors and finishes
├── ColorMaterial (Visual only)
│   └── Paint, powder coating
└── SpecialMaterial (Hardware, accessories)
```

## Material Types

### Frame Materials

Frame materials are used for structural components:

```python
from src.materials import FrameMaterial, SteelGrade

# Standard steel column
column_material = FrameMaterial(
    name="250UB31",
    material_type="steel",
    profile_type="UB",  # Universal Beam
    depth=248,          # mm
    width=124,          # mm
    web_thickness=5.0,  # mm
    flange_thickness=8.0, # mm
    grade=SteelGrade.G300,
    finish="galvanized"
)

# Square hollow section
shs_material = FrameMaterial(
    name="100x100x5SHS",
    material_type="steel",
    profile_type="SHS",
    width=100,
    height=100,
    thickness=5.0,
    grade=SteelGrade.G350
)

# Timber beam
timber_material = FrameMaterial(
    name="200x50_MGP10",
    material_type="timber",
    profile_type="rectangular",
    width=200,
    height=50,
    grade="MGP10",
    treatment="H2"
)
```

### Cladding Materials

Cladding materials define wall and roof sheeting:

```python
from src.materials import CladdingMaterial, CladdingProfile

# Standard roof cladding
roof_cladding = CladdingMaterial(
    name="Trimdek_Surfmist",
    profile=CladdingProfile.TRIMDEK,
    material="colorbond",
    color="surfmist",
    thickness=0.42,  # BMT in mm
    cover_width=762,  # mm
    rib_height=29     # mm
)

# Wall cladding with different color
wall_cladding = CladdingMaterial(
    name="Trimdek_Monument",
    profile=CladdingProfile.TRIMDEK,
    material="colorbond", 
    color="monument",
    thickness=0.42
)

# Custom profile
custom_cladding = CladdingMaterial(
    name="Custom_Profile",
    profile="custom_corrugated",
    material="zincalume",
    thickness=0.48,
    cover_width=800,
    rib_height=35,
    rib_spacing=200
)
```

### Color Materials

For non-structural colored elements:

```python
from src.materials import ColorMaterial

# Gutter color
gutter_color = ColorMaterial(
    name="Gutter_Monument",
    color="monument",
    finish="colorbond",
    gloss_level=20  # Percentage
)

# Powder coat finish
powder_coat = ColorMaterial(
    name="Frame_PowerderCoat",
    color="dulux_monument",
    finish="powder_coat",
    gloss_level=35,
    texture="smooth"
)
```

## Working with Materials

### Material Properties

```python
# Access material properties
material = FrameMaterial.get_standard("250UB31")

# Geometric properties
area = material.cross_sectional_area()  # mm²
weight = material.weight_per_meter()    # kg/m
ix = material.moment_of_inertia_x()     # mm⁴
iy = material.moment_of_inertia_y()     # mm⁴

# Engineering properties
yield_strength = material.yield_strength()  # MPa
elastic_modulus = material.elastic_modulus() # GPa

# Visual properties
color = material.get_color()
finish = material.get_finish()
```

### Material Assignment

```python
from src.bim import Column, Beam, WallPanel

# Assign material to components
column = Column(
    position=Vec3(0, 0, 0),
    height=3000,
    material=FrameMaterial.get_standard("200UC59"),
    rotation=0
)

beam = Beam(
    start=Vec3(0, 0, 3000),
    end=Vec3(6000, 0, 3000),
    material=FrameMaterial.get_standard("250UB31")
)

wall = WallPanel(
    bottom_left=Vec3(0, 0, 0),
    width=6000,
    height=3000,
    material=CladdingMaterial.get_standard("Trimdek_Monument")
)
```

## Material Profiles

### Standard Steel Profiles

```python
# Universal Beam (I-beam)
ub_profiles = [
    "150UB14", "150UB18",
    "200UB18", "200UB22", "200UB25", "200UB29",
    "250UB25", "250UB31", "250UB37",
    "310UB32", "310UB40", "310UB46",
    "360UB44", "360UB50", "360UB56"
]

# Universal Column (H-beam) 
uc_profiles = [
    "100UC14",
    "150UC23", "150UC30", "150UC37",
    "200UC46", "200UC52", "200UC59",
    "250UC72", "250UC89"
]

# Square Hollow Section
shs_profiles = [
    "50x50x3SHS", "65x65x3SHS", "75x75x3.5SHS",
    "100x100x4SHS", "100x100x5SHS", "100x100x6SHS",
    "125x125x5SHS", "150x150x5SHS", "200x200x6SHS"
]

# Circular Hollow Section
chs_profiles = [
    "60.3x3.6CHS", "76.1x3.6CHS", "88.9x4CHS",
    "114.3x4.5CHS", "139.7x5CHS", "168.3x4.8CHS"
]

# Parallel Flange Channel
pfc_profiles = [
    "75PFC", "100PFC", "125PFC", "150PFC",
    "180PFC", "200PFC", "230PFC", "250PFC", "300PFC"
]
```

### Creating Profile Cross-sections

```python
from src.materials.profiles import ProfileFactory

# Get profile geometry
profile = ProfileFactory.create_profile("250UB31")
cross_section = profile.get_cross_section()  # Returns list of Vec2 points

# Custom profile
custom_profile = ProfileFactory.create_custom(
    points=[
        Vec2(0, 0),
        Vec2(100, 0),
        Vec2(100, 10),
        Vec2(55, 10),
        Vec2(55, 190),
        Vec2(100, 190),
        Vec2(100, 200),
        Vec2(0, 200),
        Vec2(0, 190),
        Vec2(45, 190),
        Vec2(45, 10),
        Vec2(0, 10)
    ],
    thickness=5.0
)
```

## Visual Properties

### Color Systems

```python
from src.materials.visual import Color, ColorSystem

# Colorbond colors
colorbond_colors = {
    "surfmist": Color(232, 232, 227),
    "monument": Color(67, 66, 64),
    "woodland_grey": Color(130, 127, 121),
    "basalt": Color(89, 89, 89),
    "manor_red": Color(130, 42, 31),
    "cottage_green": Color(47, 75, 57),
    "deep_ocean": Color(38, 64, 89),
    "windspray": Color(167, 174, 168)
}

# Apply color to material
material.set_color(ColorSystem.COLORBOND, "monument")

# Custom color
custom_color = Color(128, 64, 32)  # RGB values
material.set_custom_color(custom_color)
```

### Textures and Finishes

```python
from src.materials.visual import Finish, Texture

# Standard finishes
finishes = {
    "mill": Finish(roughness=0.8, metallic=0.9),
    "galvanized": Finish(roughness=0.6, metallic=0.8),
    "painted": Finish(roughness=0.4, metallic=0.1),
    "powder_coated": Finish(roughness=0.3, metallic=0.2)
}

# Apply finish
material.set_finish("powder_coated", color="monument")

# Texture mapping for rendering
material.set_texture(
    diffuse="corrugated_diffuse.jpg",
    normal="corrugated_normal.jpg",
    scale=Vec2(1.0, 1.0),
    rotation=0
)
```

## Material Segments

Material segments allow complex assemblies with multiple materials:

```python
from src.materials.segments import MaterialSegment, SegmentedBeam

# Create a beam with end plates
segments = [
    # End plate
    MaterialSegment(
        start=0,
        end=10,
        material=FrameMaterial("10mm_plate", "steel", thickness=10),
        profile_override="rectangular"
    ),
    # Main beam
    MaterialSegment(
        start=10,
        end=5990,
        material=FrameMaterial.get_standard("250UB31")
    ),
    # End plate
    MaterialSegment(
        start=5990,
        end=6000,
        material=FrameMaterial("10mm_plate", "steel", thickness=10),
        profile_override="rectangular"
    )
]

segmented_beam = SegmentedBeam(
    start=Vec3(0, 0, 3000),
    end=Vec3(6000, 0, 3000),
    segments=segments
)
```

### Complex Assemblies

```python
# Portal frame knee connection
def create_knee_connection(column_material, rafter_material):
    segments = []
    
    # Column extension
    segments.append(MaterialSegment(
        start=0,
        end=300,
        material=column_material
    ))
    
    # Haunch plates
    segments.append(MaterialSegment(
        start=300,
        end=350,
        material=FrameMaterial("16mm_plate", "steel", thickness=16)
    ))
    
    # Transition
    segments.append(MaterialSegment(
        start=350,
        end=450,
        material=FrameMaterial("knee_stiffener", "steel"),
        profile_transition=True
    ))
    
    # Rafter connection
    segments.append(MaterialSegment(
        start=450,
        end=750,
        material=rafter_material
    ))
    
    return segments
```

## Practical Examples

### Example 1: Complete Building Materials

```python
def define_building_materials():
    """Define all materials for a standard shed"""
    
    materials = {
        # Structural frame
        "main_frame": {
            "columns": FrameMaterial.get_standard("250UC89"),
            "rafters": FrameMaterial.get_standard("360UB50"),
            "end_columns": FrameMaterial.get_standard("200UC59"),
            "purlins": FrameMaterial.get_standard("150PFC"),
            "girts": FrameMaterial.get_standard("150PFC")
        },
        
        # Cladding
        "cladding": {
            "roof": CladdingMaterial(
                profile=CladdingProfile.TRIMDEK,
                color="surfmist",
                thickness=0.42
            ),
            "walls": CladdingMaterial(
                profile=CladdingProfile.TRIMDEK,
                color="monument",
                thickness=0.42
            )
        },
        
        # Accessories
        "accessories": {
            "gutters": ColorMaterial("colorbond", "monument"),
            "downpipes": ColorMaterial("colorbond", "monument"),
            "flashings": ColorMaterial("colorbond", "surfmist"),
            "fasteners": ColorMaterial("zinc_plated", "silver")
        },
        
        # Openings
        "openings": {
            "roller_door": ColorMaterial("colorbond", "monument"),
            "pa_door_frame": FrameMaterial.get_standard("100x50x3RHS"),
            "windows": ColorMaterial("powder_coat", "monument")
        }
    }
    
    return materials
```

### Example 2: Material Calculations

```python
def calculate_material_quantities(building):
    """Calculate material quantities for a building"""
    
    quantities = {}
    
    # Steel frame weight
    total_steel = 0
    for component in building.get_frame_components():
        length = component.get_length()
        weight_per_m = component.material.weight_per_meter()
        total_steel += length * weight_per_m / 1000  # Convert to tonnes
    
    quantities["steel_tonnes"] = round(total_steel, 2)
    
    # Cladding area
    roof_area = 0
    wall_area = 0
    
    for panel in building.get_cladding_panels():
        area = panel.get_area() / 1_000_000  # Convert to m²
        if panel.panel_type == "roof":
            roof_area += area
        else:
            wall_area += area
    
    quantities["roof_cladding_m2"] = round(roof_area, 1)
    quantities["wall_cladding_m2"] = round(wall_area, 1)
    
    # Fixings estimate
    # Typically 5 screws per m² for walls, 4 per m² for roof
    quantities["wall_screws"] = int(wall_area * 5)
    quantities["roof_screws"] = int(roof_area * 4)
    
    return quantities
```

### Example 3: Material Optimization

```python
def optimize_purlin_spacing(span, load, material_options):
    """Find optimal purlin spacing for given load"""
    
    results = []
    
    for material in material_options:
        # Get section properties
        I = material.moment_of_inertia_x()
        Z = material.section_modulus_x()
        
        # Calculate capacity
        moment_capacity = material.yield_strength() * Z / 1.5  # Safety factor
        
        # Maximum spacing based on deflection (L/150)
        E = material.elastic_modulus() * 1000  # Convert to MPa
        max_spacing_deflection = ((span/150) * 384 * E * I / (5 * load * span**4)) ** 0.25
        
        # Maximum spacing based on strength
        max_spacing_strength = (8 * moment_capacity / (load * span**2)) ** 0.5
        
        # Use minimum
        max_spacing = min(max_spacing_deflection, max_spacing_strength)
        
        results.append({
            "material": material.name,
            "max_spacing": round(max_spacing, 0),
            "weight_per_m": material.weight_per_meter(),
            "cost_factor": material.get_cost_factor()
        })
    
    # Sort by efficiency (spacing * cost)
    results.sort(key=lambda x: x["max_spacing"] / x["cost_factor"], reverse=True)
    
    return results
```

## Custom Materials

### Creating Custom Materials

```python
from src.materials.base import CustomMaterial

class CompositeMaterial(CustomMaterial):
    """Custom composite material"""
    
    def __init__(self, name, fiber_type, resin_type, thickness):
        super().__init__(name)
        self.fiber_type = fiber_type
        self.resin_type = resin_type
        self.thickness = thickness
        
    def weight_per_area(self):
        """Calculate weight per m²"""
        densities = {
            "carbon": 1.8,  # g/cm³
            "glass": 2.5,
            "aramid": 1.4
        }
        
        fiber_density = densities.get(self.fiber_type, 2.0)
        # Assume 60% fiber, 40% resin
        composite_density = 0.6 * fiber_density + 0.4 * 1.2
        
        return composite_density * self.thickness  # kg/m²
    
    def get_strength_properties(self):
        """Return strength properties"""
        return {
            "tensile_strength": 600,  # MPa
            "compressive_strength": 400,
            "shear_strength": 80,
            "elastic_modulus": 45  # GPa
        }

# Use custom material
composite_panel = CompositeMaterial(
    name="carbon_composite",
    fiber_type="carbon",
    resin_type="epoxy",
    thickness=5.0  # mm
)
```

### Material Library Extension

```python
# Add custom materials to library
from src.materials import MaterialLibrary

# Register custom profiles
MaterialLibrary.register_profile(
    "custom_channel",
    profile_type="channel",
    dimensions={
        "depth": 200,
        "width": 75,
        "web_thickness": 6,
        "flange_thickness": 9
    }
)

# Register custom colors
MaterialLibrary.register_color(
    "custom_blue",
    Color(10, 50, 150),
    system="proprietary"
)

# Register material combinations
MaterialLibrary.register_assembly(
    "insulated_panel",
    layers=[
        {"material": "colorbond", "thickness": 0.42},
        {"material": "PIR_foam", "thickness": 50},
        {"material": "colorbond", "thickness": 0.42}
    ]
)
```

## Best Practices

1. **Use Standard Materials When Possible**
   ```python
   # Good - uses standard library
   material = FrameMaterial.get_standard("250UB31")
   
   # Avoid - custom definition of standard section
   material = FrameMaterial("250UB31", depth=248, width=124...)
   ```

2. **Cache Material Lookups**
   ```python
   # Cache frequently used materials
   _material_cache = {}
   
   def get_cached_material(name):
       if name not in _material_cache:
           _material_cache[name] = FrameMaterial.get_standard(name)
       return _material_cache[name]
   ```

3. **Validate Material Compatibility**
   ```python
   def validate_connection(material1, material2):
       # Check material compatibility
       if material1.material_type != material2.material_type:
           warnings.warn("Connecting different material types")
       
       # Check galvanic corrosion
       if material1.is_dissimilar_metal(material2):
           return False, "Galvanic corrosion risk"
       
       return True, "Compatible"
   ```

## Summary

The PyModel material system provides:
- Comprehensive material definitions
- Standard profile libraries
- Visual property management
- Material quantity calculations
- Extensibility for custom materials

Understanding materials allows you to:
- Select appropriate materials for components
- Calculate quantities and weights
- Ensure visual consistency
- Optimize material usage
- Create realistic renderings

Next, explore [Creating BIM Components](06_Creating_BIM_Components.md) to learn how materials combine with geometry to create building elements.