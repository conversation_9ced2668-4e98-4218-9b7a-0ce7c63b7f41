# Setup Guide - PyModel Development Environment

This guide will walk you through setting up your development environment for the PyModel project.

## Table of Contents
1. [System Requirements](#system-requirements)
2. [Python Installation](#python-installation)
3. [Project Setup](#project-setup)
4. [IDE Configuration](#ide-configuration)
5. [Verifying Installation](#verifying-installation)
6. [Common Issues](#common-issues)

## System Requirements

### Minimum Requirements
- Operating System: Windows 10/11, macOS 10.15+, or Linux (Ubuntu 20.04+)
- Python: 3.10 or higher
- RAM: 8GB minimum (16GB recommended)
- Disk Space: 2GB free space
- Internet connection for package downloads

### Recommended Development Tools
- Visual Studio Code or PyCharm
- Git for version control
- Postman or similar for API testing

## Python Installation

### Windows

1. Download Python from [python.org](https://www.python.org/downloads/)
2. Run the installer
3. **Important**: Check "Add Python to PATH"
4. Verify installation:
```bash
python --version
pip --version
```

### macOS

Using Homebrew:
```bash
brew install python@3.10
```

Or download from python.org and install.

### Linux (Ubuntu/Debian)

```bash
sudo apt update
sudo apt install python3.10 python3.10-venv python3-pip
```

## Project Setup

### 1. Clone the Repository

```bash
# Clone the repository (replace with actual URL)
git clone [repository-url]
cd PyModel
```

### 2. Create Virtual Environment

A virtual environment isolates project dependencies from your system Python.

```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate

# On macOS/Linux:
source venv/bin/activate
```

You should see `(venv)` in your terminal prompt when activated.

### 3. Install Dependencies

```bash
# Upgrade pip first
pip install --upgrade pip

# Install project dependencies
pip install -r requirements.txt

# Install project in development mode
pip install -e .
```

### 4. Install Development Tools

```bash
# Install code quality tools
pip install black isort flake8 mypy

# Install testing tools
pip install pytest pytest-cov pytest-asyncio
```

## IDE Configuration

### Visual Studio Code

1. Install Python extension by Microsoft
2. Create `.vscode/settings.json`:

```json
{
    "python.defaultInterpreterPath": "./venv/bin/python",
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": false,
    "python.linting.flake8Enabled": true,
    "python.formatting.provider": "black",
    "python.sortImports.args": ["--profile", "black"],
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": true
    },
    "python.testing.pytestEnabled": true,
    "python.testing.unittestEnabled": false
}
```

3. Select the virtual environment interpreter:
   - Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on macOS)
   - Type "Python: Select Interpreter"
   - Choose the interpreter from `./venv/`

### PyCharm

1. Open the project
2. Configure interpreter:
   - File → Settings → Project → Python Interpreter
   - Click gear icon → Add
   - Select "Existing environment"
   - Browse to `venv/bin/python` (or `venv\Scripts\python.exe` on Windows)

3. Enable code quality tools:
   - File → Settings → Tools → External Tools
   - Add configurations for black, isort, flake8

## Verifying Installation

### 1. Check Python Environment

```bash
# Should show Python 3.10+
python --version

# Should show path inside venv
which python  # On macOS/Linux
where python  # On Windows

# List installed packages
pip list
```

### 2. Run Basic Tests

```bash
# Run geometry tests
python -m pytest tests/geometry/test_primitives.py -v

# Run with coverage
python -m pytest tests/geometry/ --cov=src.geometry
```

### 3. Start the API Server

```bash
# Start FastAPI server
python run_api.py

# Or with auto-reload for development
python run_api.py --reload
```

Visit http://localhost:8000/docs to see the API documentation.

### 4. Test API Endpoint

```bash
# Test health check
curl http://localhost:8000/health

# Or using Python
python -c "import requests; print(requests.get('http://localhost:8000/health').json())"
```

## Common Issues

### Issue: "python" command not found

**Solution**: 
- On some systems, use `python3` instead of `python`
- Ensure Python is added to PATH
- Restart terminal after Python installation

### Issue: Permission denied when installing packages

**Solution**:
- Never use `sudo pip install`
- Always use virtual environment
- Check file permissions in project directory

### Issue: Import errors when running code

**Solution**:
```bash
# Ensure you're in virtual environment
# (venv) should appear in prompt

# Reinstall in development mode
pip install -e .

# Verify PYTHONPATH
python -c "import sys; print(sys.path)"
```

### Issue: API server won't start

**Solution**:
- Check if port 8000 is already in use
- Kill existing process: `lsof -ti:8000 | xargs kill -9` (macOS/Linux)
- Try different port: `python run_api.py --port 8001`

### Issue: Tests failing with geometry calculations

**Solution**:
- Ensure numpy is properly installed: `pip install numpy --upgrade`
- Check for 32-bit vs 64-bit Python (use 64-bit)
- Run accuracy tests: `python accuracy_tests/test_geometry_accuracy.py`

## Next Steps

Once your environment is set up:

1. Read [API Quick Start](02_API_Quick_Start.md) to start using the API
2. Follow [Building Your First Model](03_Building_First_Model.md) for a hands-on tutorial
3. Explore the codebase structure in [Architecture Guide](../Architecture_Guide.md)

## Getting Help

If you encounter issues:

1. Check the error message carefully
2. Search existing issues in the repository
3. Ask in the development chat/forum
4. Create a detailed issue with:
   - Python version
   - Operating system
   - Full error message
   - Steps to reproduce

Remember: Most setup issues are related to Python environment or missing dependencies. When in doubt, create a fresh virtual environment and reinstall.