# Task 2: Material System - Complete Beginner's Guide

## Table of Contents
1. [Introduction](#introduction)
2. [Understanding Building Materials](#understanding-building-materials)
3. [Frame Materials - The Skeleton](#frame-materials)
4. [Cladding Materials - The Skin](#cladding-materials)
5. [Fasteners and Connections](#fasteners)
6. [Footings and Foundations](#footings)
7. [Material Helpers and Catalogs](#helpers)
8. [Profile Generation](#profiles)
9. [Real-World Applications](#applications)

## Introduction

The material system defines all physical components used to construct a building. Think of it as a digital catalog of every beam, sheet, bolt, and bracket needed to build a carport or shed.

### Why is this important?
- **Accurate costing**: Know exactly how many sheets, beams, and bolts you need
- **Structural integrity**: Each material has specific strength properties
- **Compliance**: Materials must meet building codes
- **Visualization**: Generate realistic 3D models with correct materials

## Understanding Building Materials

In construction, materials are categorized by their function:

```
Building
├── Structure (Frame Materials)
│   ├── Columns (vertical supports)
│   ├── Rafters (roof supports)
│   ├── Purlins (horizontal roof members)
│   └── Braces (diagonal supports)
├── Cladding (Covering Materials)
│   ├── Roof sheets
│   └── Wall sheets
├── Connections (Fasteners)
│   ├── Bolts
│   └── Screws
└── Foundation (Footings)
    ├── Concrete blocks
    └── Bored piers
```

## Frame Materials - The Skeleton

Frame materials form the structural skeleton of the building. The most common are cold-formed steel sections.

### Material Types

#### C-Section (Channel)
```python
@dataclass
class FrameMaterial:
    """Structural frame member.
    
    Think of this like a steel beam with specific shape and properties.
    """
    name: str           # e.g., "C15024" 
    material_type: FrameMaterialType  # C, Z, TopHat, etc.
    width: float        # Flange width (mm)
    height: float       # Web height (mm)
    thickness: float    # Steel thickness (mm)
    lip: float         # Edge stiffener size (mm)
```

#### Understanding C-Section Naming
```
C15024 = C-section, 150mm high, 2.4mm thick

   Lip
    │
    ├─┐
    │ │← Flange (width)
    │ │
    │ │ Web (height)
    │ │
    │ │
    ├─┘
```

### Creating Frame Materials

```python
# Method 1: Using factory method (recommended)
c_section = FrameMaterial.create_c(
    name="C15024",
    section=150,        # Nominal size
    is_b2b=False,      # Single section (not back-to-back)
    web=152,           # Actual web height
    flange=64,         # Flange width
    lip=18.5,          # Lip size
    thickness=2.4,     # Steel thickness
    web_hole_centers=60  # Distance between web holes
)

# Method 2: Using catalog (most common)
from materials.helpers import FrameMaterialHelper
c_section = FrameMaterialHelper.get_frame_material("C15024")
```

### Material Properties Explained

1. **Web**: The vertical part that carries load
   - Taller web = stronger in bending
   - Example: 150mm web for medium spans

2. **Flange**: The horizontal parts at top/bottom
   - Wider flange = more stability
   - Example: 64mm flange for C150

3. **Thickness**: Steel gauge
   - Thicker = stronger but heavier
   - Common: 1.0mm to 3.0mm

4. **Lip**: Small return at flange edges
   - Prevents buckling
   - Typical: 10-30mm

### Back-to-Back Sections
For extra strength, two C-sections can be joined:

```python
# Single C-section
single = FrameMaterialHelper.get_frame_material("C15024")

# Double (back-to-back) C-section
double = FrameMaterialHelper.get_frame_material("2C15024")

# The double section has:
# - Same height and thickness
# - Double the width
# - Much higher load capacity
```

### Other Frame Types

#### Z-Section (Zed)
```python
z_section = FrameMaterial.create_z(
    name="Z15024",
    section=150,
    web=152,
    flange_f=66,    # Bottom flange
    flange_e=61,    # Top flange  
    lip=18.5,
    thickness=2.4,
    web_hole_centers=60
)
```
Z-sections are used for purlins (roof members) because they nest together.

#### TopHat Section
```python
tophat = FrameMaterial.create_th(
    name="TH064075",
    section=64,
    width=97,       # Overall width
    depth=64,       # Depth
    thickness=0.75
)
```
Used for battens and lighter structural members.

#### Square Hollow Section (SHS)
```python
shs = FrameMaterial.create_shs(
    name="SHS10010040",
    section=100,
    size=100,       # Square dimension
    thickness=4.0
)
```
Used for posts and columns - very strong in all directions.

## Cladding Materials - The Skin

Cladding materials cover the frame to provide weather protection.

```python
@dataclass
class CladdingMaterial:
    """Sheet material for walls and roofs."""
    name: str              # e.g., "TRIMDEK"
    design: str            # Profile name
    cover_width: float     # Effective width after overlaps
    rib_height: float      # Profile depth
    overlap: float         # Side overlap amount
    bmt: float            # Base metal thickness
    profile: List[Vec2]    # Cross-section shape
```

### Understanding Cladding Profiles

Cladding sheets have corrugated profiles for strength:

```
CORRUGATED:
 ╱╲    ╱╲    ╱╲
╱  ╲__╱  ╲__╱  ╲

TRAPEZOIDAL:
 ____    ____
|    |__|    |__
```

### Creating Cladding Profiles

```python
# Create corrugated profile
profile = CladdingProfileHelper.create_corrugated(
    profile_height=19,  # Peak to valley distance
    num_segments=8      # Number of waves
)

# Create from catalog
from materials.helpers import CladdingMaterialHelper
trimdek = CladdingMaterialHelper.get_cladding_material("TRIMDEK")
```

### Key Cladding Properties

1. **Cover Width**: Usable width after overlaps
   ```
   Sheet width: 850mm
   Overlap: 50mm each side
   Cover width: 800mm
   ```

2. **Rib Height**: Profile depth
   - Higher ribs = stronger sheet
   - Typical: 16-40mm

3. **BMT**: Base Metal Thickness
   - Standard: 0.42mm
   - Heavy duty: 0.48mm+

## Fasteners and Connections

Fasteners connect materials together:

```python
@dataclass
class FastenerMaterial:
    """Bolts, screws, and other connectors."""
    name: str
    material_type: FastenerMaterialType
    length: float              # Total length
    head_diameter: float       # Head size
    clearance_hole_diameter: float  # Hole size needed
```

### Common Fasteners

```python
# Roof screw
roof_screw = FastenerMaterial.create_bolt(
    name="M6.5x25",
    length=25,          # 25mm long
    head_diameter=13,   # 13mm hex head
    clearance_hole_diameter=7  # 7mm hole
)

# Structural bolt
bolt = FastenerMaterial.create_bolt(
    name="M12x50",
    length=50,
    head_diameter=19,
    clearance_hole_diameter=13
)
```

### Fastener Selection Rules
- **Length**: Must penetrate through materials + 3 threads
- **Diameter**: Based on load requirements
- **Type**: Self-drilling for steel, bolts for thick connections

## Footings and Foundations

Footings transfer building loads to the ground:

```python
@dataclass
class FootingMaterial:
    """Foundation elements."""
    footing_type: FootingMaterialType  # BLOCK or BORED
    width: float    # X dimension
    length: float   # Z dimension  
    depth: float    # Y dimension (into ground)
```

### Footing Types

#### Concrete Block
```python
block = FootingMaterialHelper.create_block(
    width=450,   # 450mm square
    length=450,
    depth=300    # 300mm deep
)

# Volume calculation
volume = (width/1000) * (length/1000) * (depth/1000)  # m³
```

#### Bored Pier
```python
pier = FootingMaterialHelper.create_bored(
    diameter=450,  # 450mm diameter
    depth=900      # 900mm deep
)

# Volume calculation (cylinder)
radius = diameter/2000  # Convert to meters
volume = math.pi * radius**2 * (depth/1000)  # m³
```

### Footing Design Factors
- **Load**: Heavier buildings need larger footings
- **Soil**: Soft soil needs bigger footings
- **Depth**: Must be below frost line

## Material Helpers and Catalogs

The system includes pre-defined material catalogs matching real products:

### Frame Material Catalog

```python
class FrameMaterialHelper:
    """Access to standard frame sections."""
    
    # Standard C-sections
    C_SECTIONS = {
        "C10010": C100 @ 1.0mm thick
        "C10012": C100 @ 1.2mm thick
        "C15024": C150 @ 2.4mm thick
        "C20019": C200 @ 1.9mm thick
        # ... 38 total C-sections
    }
    
    # Standard Z-sections
    Z_SECTIONS = {
        "Z10010": Z100 @ 1.0mm thick
        "Z15024": Z150 @ 2.4mm thick
        # ... 26 total Z-sections
    }
```

### Using the Catalog

```python
# Get specific material
c150 = FrameMaterialHelper.get_frame_material("C15024")

# Get all materials of a type
all_c_sections = FrameMaterialHelper.get_all_materials_by_type(
    FrameMaterialType.C
)

# Common aliases work too
c150 = FrameMaterialHelper.get_frame_material("C150")  # → C15024
```

## Profile Generation

Profiles define the 2D cross-section shape for 3D mesh generation:

### Mesh Profiles

```python
@dataclass
class MeshProfile:
    """2D profile for 3D extrusion."""
    items: List[MeshProfileItem]  # Profile segments
    punching_map: List[PunchingMap]  # Hole locations
```

### Creating Profiles

```python
# Generate C-section profile
c_material = FrameMaterialHelper.get_frame_material("C15024")
profile = MeshProfileHelper.create_mesh_profile(c_material)

# Profile contains:
# - Vertices defining the C-shape
# - Punching locations for web holes
```

### Profile Vertices Example
For a C-section, vertices trace the outline:
```
  1────2
  │    │
  │    3
  │    │
  │    4────5
  │         │
  │         │
  8─────────6
  │
  7
```

## Real-World Applications

### Example 1: Carport Frame Design

```python
def design_carport_frame(width: float, length: float, height: float):
    """Design frame materials for a carport."""
    
    # Select column material based on height
    if height <= 2.4:
        column_material = "C10019"  # Light duty
    elif height <= 3.0:
        column_material = "C15019"  # Medium duty
    else:
        column_material = "C20024"  # Heavy duty
    
    # Select rafter based on span
    span = width
    if span <= 3.0:
        rafter_material = "C10019"
    elif span <= 6.0:
        rafter_material = "C15024"
    else:
        rafter_material = "C20024"
    
    # Calculate quantities
    num_columns = 4  # Corner posts
    num_rafters = int(length / 1.2) + 1  # Every 1.2m
    
    return {
        "columns": (column_material, num_columns),
        "rafters": (rafter_material, num_rafters)
    }
```

### Example 2: Material Costing

```python
def calculate_material_cost(building):
    """Calculate total material cost."""
    
    costs = {
        "C10019": 45.00,   # $/meter
        "C15024": 62.00,   # $/meter
        "C20024": 85.00,   # $/meter
        "TRIMDEK": 18.50,  # $/m²
    }
    
    total = 0
    
    # Frame costs
    for member in building.frame_members:
        material = member.material
        length = member.length / 1000  # Convert to meters
        unit_cost = costs.get(material.name, 0)
        total += length * unit_cost
    
    # Cladding costs
    for sheet in building.cladding:
        area = sheet.area
        unit_cost = costs.get(sheet.material.name, 0)
        total += area * unit_cost
    
    return total
```

### Example 3: Load Calculations

```python
def check_beam_capacity(material: FrameMaterial, span: float, load: float):
    """Simple beam capacity check."""
    
    # Get section properties (simplified)
    if material.name == "C10019":
        moment_capacity = 2.5  # kNm
    elif material.name == "C15024":
        moment_capacity = 8.2  # kNm
    elif material.name == "C20024":
        moment_capacity = 15.6  # kNm
    else:
        return False
    
    # Calculate moment for uniform load
    moment = (load * span**2) / 8
    
    return moment <= moment_capacity
```

## Material System Architecture

The system is organized into layers:

1. **Base Materials** (`base.py`)
   - Core material classes
   - Properties and methods
   - Factory methods

2. **Helpers** (`helpers.py`)
   - Material catalogs
   - Lookup functions
   - Standard sizes

3. **Profiles** (`profiles.py`)
   - 2D shape generation
   - Mesh preparation
   - Punching patterns

4. **Segments** (`segments.py`)
   - Sheet layout optimization
   - Overlap calculations
   - Coverage analysis

## Best Practices

1. **Always use catalog materials when possible**
   ```python
   # Good
   material = FrameMaterialHelper.get_frame_material("C15024")
   
   # Avoid creating custom unless necessary
   ```

2. **Check material availability**
   ```python
   try:
       material = FrameMaterialHelper.get_frame_material(name)
   except ValueError:
       # Handle missing material
       print(f"Material {name} not in catalog")
   ```

3. **Consider material grades**
   - G250: Standard grade (250 MPa yield)
   - G350: High strength (350 MPa yield)
   - G450: Extra high strength (450 MPa yield)

4. **Account for waste**
   ```python
   # Add 10% for cutting waste
   required_length = calculated_length * 1.10
   ```

## Summary

The material system provides:
1. **Comprehensive catalog** of standard building materials
2. **Accurate properties** for structural calculations
3. **Profile generation** for 3D visualization
4. **Helper functions** for common operations
5. **Real-world compliance** with industry standards

Each material type serves a specific purpose in building construction, and the system models these accurately for both engineering analysis and cost estimation.