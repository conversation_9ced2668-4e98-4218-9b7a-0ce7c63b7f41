# Generating Output Files - IFC, GLB, DXF, and More

This guide explains how to generate various output formats from your BIM models in PyModel.

## Table of Contents
1. [Output Format Overview](#output-format-overview)
2. [IFC Generation](#ifc-generation)
3. [GLB/GLTF Generation](#glbgltf-generation)
4. [DXF Generation](#dxf-generation)
5. [CSV and Reports](#csv-and-reports)
6. [Batch Processing](#batch-processing)
7. [Customizing Output](#customizing-output)
8. [Performance Optimization](#performance-optimization)

## Output Format Overview

PyModel supports multiple output formats for different use cases:

| Format | Use Case | Features |
|--------|----------|----------|
| IFC | BIM exchange | Full building data, relationships, properties |
| GLB/GLTF | 3D visualization | Textures, materials, animations |
| DXF | 2D CAD drawings | Floor plans, elevations, sections |
| CSV | Data analysis | Quantities, schedules, reports |
| JSON | API integration | Structured data exchange |

### Output Pipeline

```python
from src.output import OutputService

# Initialize output service
output_service = OutputService()

# Register generators
output_service.register_generator("ifc", IFCGenerator())
output_service.register_generator("glb", GLBGenerator())
output_service.register_generator("dxf", DXFGenerator())
output_service.register_generator("csv", CSVGenerator())
```

## IFC Generation

### Basic IFC Export

```python
from src.output.ifc import IFCGenerator
from src.bim import Building

# Create building model
building = Building("My Warehouse")
# ... add components ...

# Initialize IFC generator
ifc_gen = IFCGenerator()

# Generate IFC file
ifc_gen.generate(
    building=building,
    output_path="warehouse.ifc",
    ifc_version="IFC2X3",  # or IFC4
    author="Your Name",
    organization="Your Company"
)
```

### IFC Configuration

```python
# Detailed IFC configuration
ifc_config = {
    "version": "IFC2X3",
    "schema": "Structural",
    "units": {
        "length": "MILLIMETER",
        "area": "SQUARE_METER",
        "volume": "CUBIC_METER",
        "angle": "DEGREE"
    },
    "project_info": {
        "name": "Warehouse Project",
        "description": "Steel frame warehouse with mezzanine",
        "phase": "Construction"
    },
    "site_info": {
        "name": "Industrial Park",
        "latitude": -27.4698,
        "longitude": 153.0251,
        "elevation": 10.0
    },
    "classification": {
        "system": "Uniclass",
        "edition": "2015"
    }
}

ifc_gen.generate_with_config(
    building=building,
    output_path="warehouse_detailed.ifc",
    config=ifc_config
)
```

### IFC Property Sets

```python
# Add custom property sets
def add_steel_properties(ifc_gen, component):
    """Add steel-specific properties to IFC"""
    
    properties = {
        "SteelGrade": component.material.grade,
        "YieldStrength": component.material.yield_strength(),
        "CoatingType": component.material.finish,
        "FireRating": "60 minutes",
        "Sustainability": {
            "RecycledContent": 0.25,
            "Recyclable": True
        }
    }
    
    ifc_gen.add_property_set(
        component=component,
        pset_name="Pset_SteelStructural",
        properties=properties
    )

# Add quantities
def add_quantities(ifc_gen, beam):
    """Add quantity information"""
    
    quantities = {
        "Length": beam.get_length(),
        "CrossSectionArea": beam.material.cross_sectional_area(),
        "Weight": beam.get_weight(),
        "SurfaceArea": beam.get_surface_area()
    }
    
    ifc_gen.add_quantity_set(
        component=beam,
        qset_name="Qto_BeamBaseQuantities",
        quantities=quantities
    )
```

### IFC Relationships

```python
# Define spatial structure
ifc_gen.create_spatial_structure(
    building=building,
    stories=[
        {"name": "Ground Floor", "elevation": 0},
        {"name": "Mezzanine", "elevation": 2700}
    ]
)

# Assign components to stories
ifc_gen.assign_to_story("Ground Floor", ground_components)
ifc_gen.assign_to_story("Mezzanine", mezzanine_components)

# Create assemblies
ifc_gen.create_assembly(
    name="Portal Frame 1",
    components=[left_column, right_column, left_rafter, right_rafter],
    assembly_type="RIGID_FRAME"
)

# Define connections
ifc_gen.create_connection(
    connection_type="BOLTED",
    connected_elements=[column, base_plate],
    connection_geometry=bolt_group
)
```

## GLB/GLTF Generation

### Basic GLB Export

```python
from src.output.gltf import GLBGenerator

# Initialize GLB generator
glb_gen = GLBGenerator()

# Generate GLB file
glb_gen.generate(
    building=building,
    output_path="warehouse.glb",
    include_textures=True,
    optimize_geometry=True
)
```

### Advanced GLB Options

```python
# Detailed GLB configuration
glb_config = {
    "format": "GLB",  # or "GLTF" for separate files
    "geometry": {
        "merge_meshes": True,
        "instance_duplicates": True,
        "compression": "DRACO",
        "position_quantization": 14,
        "normal_quantization": 10
    },
    "materials": {
        "pbr_metallic_roughness": True,
        "texture_format": "WEBP",
        "texture_size": 2048,
        "generate_ambient_occlusion": True
    },
    "scene": {
        "up_axis": "Z",
        "unit_scale": 0.001,  # mm to meters
        "include_lights": True,
        "include_cameras": True
    },
    "optimization": {
        "remove_unused_materials": True,
        "merge_materials": True,
        "simplify_geometry": False
    }
}

glb_gen.generate_with_config(
    building=building,
    output_path="warehouse_optimized.glb",
    config=glb_config
)
```

### Material Mapping for GLB

```python
# Custom material mapping
def map_materials_for_rendering(building):
    """Map BIM materials to PBR materials"""
    
    material_mapping = {}
    
    for component in building.get_all_components():
        bim_material = component.material
        
        # Create PBR material
        pbr_material = {
            "name": bim_material.name,
            "pbrMetallicRoughness": {
                "baseColorFactor": get_color_factor(bim_material),
                "metallicFactor": get_metallic_factor(bim_material),
                "roughnessFactor": get_roughness_factor(bim_material)
            }
        }
        
        # Add textures if available
        if hasattr(bim_material, 'texture'):
            pbr_material["pbrMetallicRoughness"]["baseColorTexture"] = {
                "index": create_texture(bim_material.texture)
            }
        
        material_mapping[bim_material.name] = pbr_material
    
    return material_mapping

def get_metallic_factor(material):
    """Determine metallic factor based on material type"""
    if material.material_type == "steel":
        return 0.9 if material.finish == "galvanized" else 0.95
    elif material.material_type == "aluminum":
        return 0.9
    else:
        return 0.0

def get_roughness_factor(material):
    """Determine roughness factor based on finish"""
    finish_roughness = {
        "mill": 0.8,
        "galvanized": 0.6,
        "painted": 0.4,
        "powder_coated": 0.3
    }
    return finish_roughness.get(material.finish, 0.5)
```

### Adding Metadata to GLB

```python
# Add metadata for web viewers
glb_gen.add_metadata({
    "generator": "PyModel BIM System",
    "version": "1.0.0",
    "copyright": "Your Company",
    "building": {
        "type": "Warehouse",
        "area": building.get_floor_area(),
        "volume": building.get_volume()
    },
    "components": {
        "count": len(building.get_all_components()),
        "types": building.get_component_types()
    }
})

# Add component tags for interactivity
for component in building.get_all_components():
    glb_gen.add_component_extra(component, {
        "id": component.id,
        "name": component.name,
        "material": component.material.name,
        "properties": component.get_properties()
    })
```

## DXF Generation

### 2D Floor Plans

```python
from src.output.dxf import DXFGenerator, DXFLayer

# Initialize DXF generator
dxf_gen = DXFGenerator()

# Define layers
layers = [
    DXFLayer("GRID", color=8),          # Grey
    DXFLayer("COLUMNS", color=7),       # White
    DXFLayer("WALLS", color=3),         # Green
    DXFLayer("DOORS", color=1),         # Red
    DXFLayer("DIMENSIONS", color=2),    # Yellow
    DXFLayer("TEXT", color=7)           # White
]

# Generate floor plan
dxf_gen.generate_floor_plan(
    building=building,
    output_path="warehouse_plan.dxf",
    elevation=0,  # Ground level
    layers=layers,
    scale=1.0,
    include_dimensions=True,
    include_grid=True
)
```

### Elevations and Sections

```python
# Generate elevations
elevations = ["north", "south", "east", "west"]

for direction in elevations:
    dxf_gen.generate_elevation(
        building=building,
        output_path=f"warehouse_{direction}_elevation.dxf",
        view_direction=direction,
        include_hidden_lines=True,
        hidden_line_layer="HIDDEN",
        hidden_line_style="DASHED"
    )

# Generate cross-section
dxf_gen.generate_section(
    building=building,
    output_path="warehouse_section_aa.dxf",
    cut_plane=Plane3(
        point=Vec3(6000, 0, 0),
        normal=Vec3(0, 1, 0)
    ),
    view_direction="north",
    section_depth=1000,  # How far to look beyond cut
    hatch_cut_elements=True
)
```

### Detail Drawings

```python
# Connection detail
dxf_gen.generate_detail(
    components=[column, base_plate, anchor_bolts],
    output_path="base_plate_detail.dxf",
    view_box=Box3(
        Vec3(-500, -500, -100),
        Vec3(500, 500, 300)
    ),
    scale=10,  # 1:10 scale
    include_dimensions=True,
    dimension_style="architectural"
)

# Create drawing sheet with multiple views
sheet = dxf_gen.create_sheet(
    size="A1",  # A1, A2, A3, A4
    orientation="landscape"
)

# Add views to sheet
sheet.add_view(
    view=floor_plan,
    position=(50, 50),
    scale=100  # 1:100
)

sheet.add_view(
    view=north_elevation,
    position=(50, 400),
    scale=100
)

sheet.add_view(
    view=detail,
    position=(600, 400),
    scale=10
)

# Add title block
sheet.add_title_block({
    "project": "Warehouse Project",
    "drawing": "General Arrangement",
    "number": "S-101",
    "revision": "A",
    "date": datetime.now().strftime("%Y-%m-%d"),
    "drawn_by": "Designer",
    "checked_by": "Checker"
})

sheet.save("warehouse_ga.dxf")
```

## CSV and Reports

### Material Schedules

```python
from src.output.csv import CSVGenerator

csv_gen = CSVGenerator()

# Generate material schedule
material_schedule = csv_gen.generate_material_schedule(
    building=building,
    output_path="material_schedule.csv",
    group_by="material_type",
    include_fields=[
        "component_id",
        "component_type",
        "material_name",
        "length",
        "weight",
        "surface_area",
        "quantity"
    ]
)

# Generate cut list
cut_list = csv_gen.generate_cut_list(
    building=building,
    output_path="steel_cut_list.csv",
    material_filter="steel",
    optimization="minimize_waste",
    stock_lengths=[6000, 9000, 12000]  # Available lengths
)
```

### Quantity Reports

```python
# Bill of materials
bom = csv_gen.generate_bom(
    building=building,
    output_path="bill_of_materials.csv",
    include_pricing=True,
    price_database="current_prices.db",
    format={
        "headers": ["Item", "Description", "Unit", "Quantity", "Unit Price", "Total"],
        "grouping": ["category", "material_type"],
        "subtotals": True,
        "grand_total": True
    }
)

# Fastener schedule
fastener_schedule = csv_gen.generate_fastener_schedule(
    building=building,
    output_path="fastener_schedule.csv",
    include_fields=[
        "location",
        "connection_type",
        "fastener_type",
        "diameter",
        "length",
        "grade",
        "quantity",
        "torque_spec"
    ]
)
```

### Custom Reports

```python
# Create custom report
def generate_engineering_report(building, output_path):
    """Generate engineering summary report"""
    
    report_data = []
    
    # Structural summary
    total_steel_weight = sum(
        c.get_weight() for c in building.get_components_by_material("steel")
    )
    
    report_data.append({
        "Category": "Structural Steel",
        "Metric": "Total Weight",
        "Value": f"{total_steel_weight:.2f}",
        "Unit": "kg"
    })
    
    # Load summary
    roof_area = building.get_roof_area()
    design_loads = building.get_design_loads()
    
    report_data.append({
        "Category": "Loads",
        "Metric": "Roof Live Load",
        "Value": design_loads["roof_live"],
        "Unit": "kPa"
    })
    
    # Connection summary
    connections = building.get_all_connections()
    connection_types = {}
    for conn in connections:
        conn_type = conn.connection_type
        connection_types[conn_type] = connection_types.get(conn_type, 0) + 1
    
    for conn_type, count in connection_types.items():
        report_data.append({
            "Category": "Connections",
            "Metric": f"{conn_type} Connections",
            "Value": count,
            "Unit": "count"
        })
    
    # Save report
    csv_gen.save_report(report_data, output_path)
```

## Batch Processing

### Multiple Format Export

```python
from src.output import BatchExporter

# Initialize batch exporter
batch_exporter = BatchExporter()

# Define export jobs
export_jobs = [
    {
        "format": "ifc",
        "output_path": "outputs/warehouse.ifc",
        "config": ifc_config
    },
    {
        "format": "glb",
        "output_path": "outputs/warehouse.glb",
        "config": glb_config
    },
    {
        "format": "dxf",
        "output_path": "outputs/warehouse_plan.dxf",
        "config": {"view": "plan", "elevation": 0}
    },
    {
        "format": "csv",
        "output_path": "outputs/material_schedule.csv",
        "config": {"report_type": "material_schedule"}
    }
]

# Execute batch export
results = batch_exporter.export_all(
    building=building,
    jobs=export_jobs,
    parallel=True,
    on_progress=lambda job, progress: print(f"{job['format']}: {progress}%")
)

# Check results
for result in results:
    if result["success"]:
        print(f"✓ {result['format']} exported to {result['output_path']}")
    else:
        print(f"✗ {result['format']} failed: {result['error']}")
```

### Automated Drawing Sets

```python
# Generate complete drawing set
def generate_drawing_set(building, project_info):
    """Generate complete set of construction drawings"""
    
    drawings = []
    
    # Cover sheet
    drawings.append({
        "type": "cover",
        "number": "G-001",
        "title": "Cover Sheet and Index"
    })
    
    # Site plan
    drawings.append({
        "type": "site_plan",
        "number": "C-101",
        "title": "Site Plan",
        "scale": 500
    })
    
    # Floor plans
    for i, level in enumerate(building.levels):
        drawings.append({
            "type": "floor_plan",
            "number": f"A-{101 + i}",
            "title": f"{level.name} Floor Plan",
            "scale": 100,
            "elevation": level.elevation
        })
    
    # Elevations
    for i, direction in enumerate(["North", "South", "East", "West"]):
        drawings.append({
            "type": "elevation",
            "number": f"A-{201 + i}",
            "title": f"{direction} Elevation",
            "scale": 100,
            "view_direction": direction.lower()
        })
    
    # Sections
    drawings.append({
        "type": "section",
        "number": "A-301",
        "title": "Building Section A-A",
        "scale": 100
    })
    
    # Structural plans
    drawings.append({
        "type": "structural_plan",
        "number": "S-101",
        "title": "Foundation Plan",
        "scale": 100
    })
    
    drawings.append({
        "type": "structural_plan",
        "number": "S-201",
        "title": "Roof Framing Plan",
        "scale": 100
    })
    
    # Details
    detail_scales = [20, 10, 5]
    detail_number = 1
    
    for detail in building.get_typical_details():
        drawings.append({
            "type": "detail",
            "number": f"D-{detail_number:03d}",
            "title": detail.title,
            "scale": detail.recommended_scale
        })
        detail_number += 1
    
    # Generate all drawings
    drawing_generator = DrawingSetGenerator()
    drawing_generator.generate_set(
        building=building,
        drawings=drawings,
        output_folder="drawings",
        project_info=project_info
    )
```

## Customizing Output

### Custom IFC Entities

```python
# Create custom IFC entity
def create_custom_shed_entity(ifc_gen, shed_component):
    """Create custom IFC entity for shed-specific components"""
    
    # Create custom property set
    custom_pset = ifc_gen.create_property_set("Pset_ShedCustom")
    
    # Add properties
    custom_pset.add_property("BayNumber", shed_component.bay_number)
    custom_pset.add_property("WindPressureCoeff", shed_component.wind_coeff)
    custom_pset.add_property("SnowLoadCapacity", shed_component.snow_capacity)
    
    # Create custom type
    custom_type = ifc_gen.create_element_type(
        "IfcBuildingElementProxyType",
        name="SHED_COMPONENT",
        description="Custom shed building component"
    )
    
    # Assign to component
    ifc_entity = ifc_gen.create_element(
        shed_component,
        ifc_type=custom_type,
        property_sets=[custom_pset]
    )
    
    return ifc_entity
```

### Custom GLB Extensions

```python
# Add custom extensions to GLB
def add_interaction_extension(glb_gen, building):
    """Add interactivity extension for web viewers"""
    
    # Define clickable components
    for component in building.get_interactive_components():
        glb_gen.add_extension(
            component,
            "EXT_mesh_gpu_instancing",
            {
                "attributes": {
                    "CLICKABLE": True,
                    "TOOLTIP": component.get_tooltip(),
                    "URL": component.get_info_url()
                }
            }
        )
    
    # Add animation for doors
    for door in building.get_doors():
        if door.is_animated:
            glb_gen.add_animation(
                door,
                animation_type="rotation",
                pivot=door.get_hinge_position(),
                axis=Vec3(0, 0, 1),
                angle_range=(0, 90),
                duration=2.0
            )
```

## Performance Optimization

### Streaming Large Models

```python
# Stream large IFC files
def stream_large_ifc(building, output_path, chunk_size=1000):
    """Stream IFC generation for large models"""
    
    with IFCStreamer(output_path) as streamer:
        # Write header
        streamer.write_header(building.get_project_info())
        
        # Stream components in chunks
        components = building.get_all_components()
        
        for i in range(0, len(components), chunk_size):
            chunk = components[i:i + chunk_size]
            streamer.write_components(chunk)
            
            # Progress callback
            progress = (i + len(chunk)) / len(components) * 100
            print(f"IFC Export: {progress:.1f}%")
        
        # Write relationships
        streamer.write_relationships(building.get_all_relationships())
        
        # Finalize
        streamer.finalize()
```

### Parallel Processing

```python
import multiprocessing
from concurrent.futures import ProcessPoolExecutor

def parallel_dxf_generation(building, views):
    """Generate multiple DXF views in parallel"""
    
    def generate_single_view(view_config):
        dxf_gen = DXFGenerator()
        return dxf_gen.generate_view(building, **view_config)
    
    with ProcessPoolExecutor() as executor:
        futures = []
        
        for view in views:
            future = executor.submit(generate_single_view, view)
            futures.append((view["name"], future))
        
        results = {}
        for name, future in futures:
            try:
                results[name] = future.result()
                print(f"✓ Generated {name}")
            except Exception as e:
                print(f"✗ Failed to generate {name}: {e}")
                results[name] = None
        
        return results
```

### Memory-Efficient Export

```python
# Memory-efficient GLB generation
def generate_glb_low_memory(building, output_path):
    """Generate GLB with minimal memory usage"""
    
    with GLBLowMemoryGenerator(output_path) as gen:
        # Process geometry in batches
        for component_batch in building.iter_components(batch_size=100):
            # Generate geometry
            geometry = gen.process_geometry(component_batch)
            
            # Write immediately
            gen.write_geometry(geometry)
            
            # Clear cache
            gen.clear_cache()
        
        # Write materials separately
        for material in building.get_unique_materials():
            gen.write_material(material)
        
        # Finalize
        gen.finalize()
```

## Best Practices

1. **Validate Before Export**
   ```python
   # Always validate model before export
   validation_results = building.validate()
   if not validation_results.is_valid:
       print("Model validation failed:")
       for error in validation_results.errors:
           print(f"  - {error}")
       return
   ```

2. **Use Appropriate Formats**
   ```python
   # Choose format based on use case
   if purpose == "visualization":
       use_format = "glb"
   elif purpose == "bim_exchange":
       use_format = "ifc"
   elif purpose == "fabrication":
       use_format = "dxf"
   ```

3. **Optimize File Sizes**
   ```python
   # Configure for file size vs quality
   if priority == "file_size":
       config["compression"] = "high"
       config["texture_size"] = 1024
   else:
       config["compression"] = "none"
       config["texture_size"] = 4096
   ```

## Summary

PyModel's output generation provides:
- Multiple format support (IFC, GLB, DXF, CSV)
- Configurable export options
- Batch processing capabilities
- Performance optimization
- Extensibility for custom formats

You can now:
- Export models to any required format
- Configure output for specific use cases
- Process large models efficiently
- Create custom export pipelines

Next, explore [Extending for New Building Types](08_Extending_Building_Types.md) to learn how to add support for new building types beyond carports and sheds.