# Immediate Action Plan for BIM Backend

## 🚨 Critical Issues to Fix (Priority Order)

### 1. Fix All Import Errors (2 hours)

#### Problem
29 out of 40 modules fail to import due to relative import errors.

#### Solution
Create a proper `__init__.py` structure and fix imports:

```python
# src/__init__.py
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

# src/geometry/__init__.py
from .primitives import Vec2, Vec3
from .matrix import Mat4
from .lines import Line1, Line2, Line3
from .boxes import Box2, Box3, Rect
from .plane import Plane3
from .basis import Basis3

__all__ = ['Vec2', 'Vec3', 'Mat4', 'Line1', 'Line2', 'Line3', 
           'Box2', 'Box3', 'Rect', 'Plane3', 'Basis3']
```

#### Fix all module imports:
```python
# In src/materials/base.py
# Change from:
from ..geometry import Vec2, Vec3

# To:
from src.geometry.primitives import Vec2, Vec3
```

### 2. Install Missing Dependencies (30 minutes)

#### Create proper requirements.txt:
```text
numpy==1.25.2
scipy==1.11.4
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
httpx==0.25.2
cryptography==41.0.8
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
triangle==20230923
shapely==2.0.2
pyclipper==1.3.0.post5
ifcopenshell==0.7.0
ezdxf==1.1.0
```

#### Install command:
```bash
pip install -r requirements.txt
```

### 3. Complete Missing Method Implementations (4 hours)

#### Vec2.add method:
```python
# In src/geometry/primitives.py
def add(self, other: 'Vec2') -> 'Vec2':
    """Add two vectors."""
    return Vec2(self.x + other.x, self.y + other.y)
```

#### Vec3.dist method:
```python
# In src/geometry/primitives.py
def dist(self, other: 'Vec3') -> float:
    """Calculate distance to another point (alias for distance)."""
    return self.distance(other)
```

#### Mat4.to_list method:
```python
# In src/geometry/matrix.py
def to_list(self) -> list:
    """Get the matrix as a flat list (column-major order for GLTF)."""
    return [
        self.m11, self.m21, self.m31, self.m41,
        self.m12, self.m22, self.m32, self.m42,
        self.m13, self.m23, self.m33, self.m43,
        self.m14, self.m24, self.m34, self.m44
    ]
```

#### Complete FrameMaterial.get_profile_points:
```python
# In src/materials/base.py
def get_profile_points(self) -> List[Vec2]:
    """Get 2D profile points for the frame cross-section."""
    if self.material_type == FrameMaterialType.C:
        # C-channel profile
        points = []
        # Bottom flange
        points.append(Vec2(0, 0))
        points.append(Vec2(self.width, 0))
        # Right lip
        points.append(Vec2(self.width, self.lip))
        points.append(Vec2(self.width - self.thickness, self.lip))
        # Right side up
        points.append(Vec2(self.width - self.thickness, self.thickness))
        # Top
        points.append(Vec2(self.width - self.thickness, self.height - self.thickness))
        points.append(Vec2(self.width, self.height - self.thickness))
        # Top lip
        points.append(Vec2(self.width, self.height - self.lip))
        points.append(Vec2(self.width, self.height))
        # Left side
        points.append(Vec2(0, self.height))
        points.append(Vec2(0, self.thickness))
        points.append(Vec2(self.thickness, self.thickness))
        return points
        
    elif self.material_type == FrameMaterialType.SHS:
        # Square hollow section
        outer = [
            Vec2(0, 0),
            Vec2(self.width, 0),
            Vec2(self.width, self.height),
            Vec2(0, self.height)
        ]
        inner = [
            Vec2(self.thickness, self.thickness),
            Vec2(self.width - self.thickness, self.thickness),
            Vec2(self.width - self.thickness, self.height - self.thickness),
            Vec2(self.thickness, self.height - self.thickness)
        ]
        return outer + inner
        
    elif self.material_type == FrameMaterialType.TH:
        # Top hat profile
        points = []
        # Base
        points.append(Vec2(0, 0))
        points.append(Vec2(self.width, 0))
        # Right side up
        points.append(Vec2(self.width, self.height))
        # Top
        points.append(Vec2(self.width - self.lip, self.height))
        # Inner right
        points.append(Vec2(self.width - self.lip, self.thickness))
        # Inner bottom
        points.append(Vec2(self.lip, self.thickness))
        # Inner left
        points.append(Vec2(self.lip, self.height))
        # Top left
        points.append(Vec2(0, self.height))
        return points
        
    elif self.material_type == FrameMaterialType.Z:
        # Z-section profile
        points = []
        # Bottom flange (left)
        points.append(Vec2(0, 0))
        points.append(Vec2(self.width, 0))
        points.append(Vec2(self.width, self.thickness))
        # Web
        points.append(Vec2(self.thickness, self.thickness))
        points.append(Vec2(self.thickness, self.height - self.thickness))
        # Top flange (right)
        points.append(Vec2(0, self.height - self.thickness))
        points.append(Vec2(0, self.height))
        points.append(Vec2(self.width, self.height))
        return points
        
    else:
        # Default rectangle for unknown types
        return [
            Vec2(0, 0),
            Vec2(self.width, 0),
            Vec2(self.width, self.height),
            Vec2(0, self.height)
        ]
```

### 4. Create Critical Path Tests (4 hours)

#### Test structure setup:
```python
# tests/conftest.py
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

import pytest

@pytest.fixture
def test_material():
    from src.materials.base import FrameMaterial, FrameMaterialType
    return FrameMaterial(
        name="C10015",
        material_type=FrameMaterialType.C,
        width=102,
        height=51,
        thickness=1.5,
        lip=13.5
    )

@pytest.fixture
def test_building_input():
    from src.business.building_input import BuildingInput, BuildingType
    return BuildingInput(
        building_type=BuildingType.CARPORT,
        span=6000,
        length=6000,
        height=2700
    )
```

#### Core geometry tests:
```python
# tests/test_geometry_core.py
import pytest
import math
from src.geometry.primitives import Vec2, Vec3
from src.geometry.matrix import Mat4

class TestCoreGeometry:
    def test_vec2_operations(self):
        v1 = Vec2(3, 4)
        v2 = Vec2(1, 2)
        
        # Test length
        assert v1.length() == 5.0
        
        # Test operations
        assert v1.add(v2) == Vec2(4, 6)
        assert (v1 - v2) == Vec2(2, 2)
        assert (v1 * 2) == Vec2(6, 8)
        
        # Test dot product
        assert Vec2.dot(v1, v2) == 11  # 3*1 + 4*2
    
    def test_vec3_operations(self):
        v1 = Vec3(1, 0, 0)
        v2 = Vec3(0, 1, 0)
        
        # Test cross product
        cross = Vec3.cross(v1, v2)
        assert cross == Vec3(0, 0, 1)
        
        # Test distance
        assert v1.dist(v2) == math.sqrt(2)
    
    def test_mat4_operations(self):
        # Test identity
        m = Mat4.identity()
        v = Vec3(1, 2, 3)
        assert m.transform_position(v) == v
        
        # Test to_list
        matrix_list = m.to_list()
        assert len(matrix_list) == 16
        assert matrix_list[0] == 1  # m11
        assert matrix_list[15] == 1  # m44
```

#### Material tests:
```python
# tests/test_materials_core.py
import pytest
from src.materials.base import FrameMaterial, FrameMaterialType

class TestMaterials:
    def test_frame_material_properties(self, test_material):
        assert test_material.web == 51
        assert test_material.flange == 102
        assert test_material.id == "C10015"
    
    def test_profile_generation(self, test_material):
        profile = test_material.get_profile_points()
        assert len(profile) > 0
        assert all(hasattr(p, 'x') and hasattr(p, 'y') for p in profile)
    
    def test_different_profiles(self):
        # Test each profile type
        for material_type in [FrameMaterialType.C, FrameMaterialType.SHS, 
                             FrameMaterialType.TH, FrameMaterialType.Z]:
            material = FrameMaterial(
                name=f"Test{material_type.value}",
                material_type=material_type,
                width=100,
                height=50,
                thickness=2.0
            )
            profile = material.get_profile_points()
            assert len(profile) > 3  # At least a triangle
```

#### Business logic tests:
```python
# tests/test_business_core.py
import pytest
from src.business.building_input import BuildingInput, BuildingType
from src.business.structure_builder import CarportBuilder

class TestBusinessLogic:
    def test_building_input_validation(self, test_building_input):
        # Valid input
        assert test_building_input.validate() == True
        
        # Invalid span
        test_building_input.span = 15000
        assert test_building_input.validate() == False
    
    def test_carport_creation(self, test_building_input):
        carport = CarportBuilder.create_carport(test_building_input)
        
        assert carport is not None
        assert carport.main is not None
        assert hasattr(carport.main, 'side_left')
        assert hasattr(carport.main, 'side_right')
```

### 5. Fix API and Service Tests (2 hours)

#### API endpoint test:
```python
# tests/test_api_core.py
import pytest
import json
from fastapi.testclient import TestClient

def test_api_imports():
    from src.api.main import app
    client = TestClient(app)
    
    # Test health endpoint
    response = client.get("/health")
    assert response.status_code == 200

def test_encryption_service():
    from src.services.encryption import AESEncryption
    
    aes = AESEncryption("test-key")
    plaintext = "Hello, World!"
    
    encrypted = aes.encrypt(plaintext)
    decrypted = aes.decrypt(encrypted)
    
    assert decrypted == plaintext
```

### 6. Create Test Runner Script (1 hour)

```python
# run_tests.py
#!/usr/bin/env python3
import subprocess
import sys
import os

def run_tests():
    """Run all tests with coverage report."""
    # Ensure we're in the right directory
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    # Install dependencies if needed
    print("Checking dependencies...")
    subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
    
    # Run tests with coverage
    print("\nRunning tests...")
    result = subprocess.run([
        sys.executable, "-m", "pytest",
        "-v",
        "--cov=src",
        "--cov-report=term-missing",
        "--cov-report=html",
        "tests/"
    ])
    
    if result.returncode == 0:
        print("\n✅ All tests passed!")
        print("Coverage report generated in htmlcov/index.html")
    else:
        print("\n❌ Tests failed!")
        sys.exit(1)

if __name__ == "__main__":
    run_tests()
```

## 📋 Execution Checklist

### Day 1 (Today)
- [ ] Fix all import statements (2 hours)
- [ ] Install all dependencies (30 min)
- [ ] Implement missing methods (4 hours)
- [ ] Create basic test structure (1 hour)

### Day 2
- [ ] Write core geometry tests (2 hours)
- [ ] Write material tests (2 hours)
- [ ] Write business logic tests (2 hours)
- [ ] Fix failing tests (2 hours)

### Day 3
- [ ] Write API tests (2 hours)
- [ ] Write integration tests (3 hours)
- [ ] Create performance tests (1 hour)
- [ ] Document test procedures (2 hours)

## 🎯 Success Metrics

### Immediate Goals (48 hours)
- Import success rate: 100%
- Test coverage: > 50%
- Core functionality working

### Week 1 Goals
- Test coverage: > 80%
- All critical paths tested
- Performance validated

### Week 2 Goals
- Full integration tests
- Security testing complete
- Production ready

## 🔧 Quick Commands

```bash
# Fix imports in all files
find src -name "*.py" -exec sed -i 's/from \.\./from src/g' {} \;

# Run specific test category
pytest tests/test_geometry_core.py -v

# Run with coverage
pytest --cov=src --cov-report=html

# Check import issues
python3 -c "import src.geometry.primitives; print('✓ Imports working')"

# Generate test report
pytest --junit-xml=test-results.xml
```

## ⚡ Emergency Fixes

If you need to get something working quickly:

1. **Quick Import Fix**:
   ```python
   import sys
   sys.path.insert(0, '/path/to/PyModel/src')
   ```

2. **Skip Failing Tests**:
   ```python
   @pytest.mark.skip(reason="Pending implementation")
   def test_complex_feature():
       pass
   ```

3. **Mock External Dependencies**:
   ```python
   @patch('src.services.engineering.EngineeringService')
   def test_with_mock(mock_service):
       mock_service.return_value.validate.return_value = True
   ```

This action plan provides a clear path to fix all critical issues and get the codebase to a production-ready state.