# Complete Testing Guide for BIM Backend

## Overview

This guide provides a session-by-session approach to comprehensively test the BIM Backend implementation. Each session focuses on a specific component and includes setup, test cases, and validation criteria.

## Pre-requisites

### Install Dependencies
```bash
pip install -r requirements.txt
```

### Fix Import Issues
Add to the top of each test file:
```python
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))
```

## Session 1: Geometry Foundation Testing

### Duration: 2 hours
### Focus: Core mathematical operations

#### Test Files
- `tests/geometry/test_primitives.py`
- `tests/geometry/test_matrix.py`
- `tests/geometry/test_lines.py`
- `tests/geometry/test_boxes.py`

#### Test Cases

```python
# test_primitives.py
import pytest
from geometry.primitives import Vec2, Vec3

class TestVec2:
    def test_creation(self):
        v = Vec2(3, 4)
        assert v.x == 3
        assert v.y == 4
    
    def test_length(self):
        v = Vec2(3, 4)
        assert v.length() == 5.0
    
    def test_dot_product(self):
        v1 = Vec2(1, 0)
        v2 = Vec2(0, 1)
        assert Vec2.dot(v1, v2) == 0
    
    def test_operations(self):
        v1 = Vec2(1, 2)
        v2 = Vec2(3, 4)
        assert (v1 + v2) == Vec2(4, 6)
        assert (v1 - v2) == Vec2(-2, -2)
        assert (v1 * 2) == Vec2(2, 4)
    
    def test_intersection(self):
        # Test line intersection
        result = Vec2.inters(
            Vec2(0, 0), Vec2(10, 10),
            Vec2(0, 10), Vec2(10, 0)
        )
        assert result == Vec2(5, 5)

class TestVec3:
    def test_cross_product(self):
        v1 = Vec3(1, 0, 0)
        v2 = Vec3(0, 1, 0)
        result = Vec3.cross(v1, v2)
        assert result == Vec3(0, 0, 1)
    
    def test_distance(self):
        v1 = Vec3(0, 0, 0)
        v2 = Vec3(3, 4, 0)
        assert v1.distance(v2) == 5.0

class TestMat4:
    def test_identity(self):
        m = Mat4.identity()
        v = Vec3(1, 2, 3)
        assert m.transform_position(v) == v
    
    def test_translation(self):
        m = Mat4.translation(1, 2, 3)
        v = Vec3(0, 0, 0)
        assert m.transform_position(v) == Vec3(1, 2, 3)
    
    def test_rotation(self):
        m = Mat4.rotation_z(math.pi / 2)  # 90 degrees
        v = Vec3(1, 0, 0)
        result = m.transform_vector(v)
        assert abs(result.x) < 0.001
        assert abs(result.y - 1) < 0.001
    
    def test_inverse(self):
        m = Mat4.translation(1, 2, 3)
        inv = m.get_inverse()
        identity = m * inv
        # Check if result is identity
        assert abs(identity.m11 - 1) < 0.001
        assert abs(identity.m12) < 0.001
```

#### Validation Criteria
- All vector operations match expected mathematical results
- Matrix transformations preserve expected properties
- Intersection algorithms return correct results
- Performance: Each test completes in < 100ms

## Session 2: Materials System Testing

### Duration: 2 hours
### Focus: Material definitions and validation

#### Test Files
- `tests/materials/test_base.py`
- `tests/materials/test_profiles.py`
- `tests/materials/test_helpers.py`

#### Test Cases

```python
# test_base.py
import pytest
from materials.base import (
    FrameMaterial, FrameMaterialType,
    BracketMaterial, CladdingMaterial
)

class TestFrameMaterial:
    def test_c_channel_creation(self):
        material = FrameMaterial(
            name="C10015",
            material_type=FrameMaterialType.C,
            width=102,
            height=51,
            thickness=1.5,
            lip=13.5
        )
        assert material.web == 51
        assert material.flange == 102
        assert material.id == "C10015"
    
    def test_profile_generation(self):
        material = FrameMaterial(
            name="C10015",
            material_type=FrameMaterialType.C,
            width=102,
            height=51,
            thickness=1.5,
            lip=13.5
        )
        profile = material.get_profile_points()
        assert len(profile) == 12  # C-channel has 12 points
        # Verify profile shape
        assert profile[0] == Vec2(0, 0)
        assert profile[-1] == Vec2(0, 0)  # Closed profile
    
    def test_shs_profile(self):
        material = FrameMaterial(
            name="SHS100",
            material_type=FrameMaterialType.SHS,
            width=100,
            height=100,
            thickness=3.0
        )
        profile = material.get_profile_points()
        assert len(profile) == 8  # Square has 8 points (with thickness)
    
    def test_material_validation(self):
        # Test invalid thickness
        with pytest.raises(ValueError):
            FrameMaterial(
                name="Invalid",
                material_type=FrameMaterialType.C,
                width=100,
                height=50,
                thickness=-1  # Invalid
            )

class TestBracketMaterial:
    def test_bracket_mesh_loading(self):
        bracket = BracketMaterial(
            name="Bracket1",
            mesh_name="standard_bracket"
        )
        mesh = bracket.get_bracket_mesh()
        assert mesh is not None
        assert len(mesh.vertices) > 0
        assert len(mesh.triangles) > 0
```

#### Validation Criteria
- Materials can be created with valid parameters
- Invalid parameters raise appropriate exceptions
- Profile generation produces correct geometry
- Material IDs are unique and consistent

## Session 3: BIM Data Model Testing

### Duration: 3 hours
### Focus: Complex data structures and relationships

#### Test Files
- `tests/bim/test_shed_bim.py`
- `tests/bim/test_components.py`
- `tests/bim/test_wall_roof.py`

#### Test Cases

```python
# test_shed_bim.py
import pytest
from bim.shed_bim import (
    ShedBim, ShedBimPartMain, ShedBimSide,
    ShedBimRoof, ShedBimColumn, ShedBimRafter
)

class TestShedBim:
    def test_basic_structure(self):
        # Create minimal shed structure
        shed = ShedBim(
            main=ShedBimPartMain(
                roof_type="Gable",
                roof_left=ShedBimRoof(),
                roof_right=ShedBimRoof(),
                side_left=ShedBimSide(),
                side_right=ShedBimSide()
            )
        )
        assert shed.main is not None
        assert len(shed.main.get_roofs()) == 2
        assert len(shed.main.get_sides()) == 2
    
    def test_component_hierarchy(self):
        # Test parent-child relationships
        column = ShedBimColumn(
            name="Column1",
            material=FrameMaterial("C10015", FrameMaterialType.C, 100, 50, 1.5),
            bottom=Vec3(0, 0, 0),
            top=Vec3(0, 0, 3000)
        )
        
        side = ShedBimSide()
        side.columns.append(column)
        
        assert len(side.columns) == 1
        assert side.columns[0].name == "Column1"
    
    def test_serialization(self):
        # Test conversion to/from dict
        shed = create_test_shed()
        data = shed.to_dict()
        
        # Verify structure
        assert "main" in data
        assert "roof_type" in data["main"]
        
        # Test deserialization
        shed2 = ShedBim.from_dict(data)
        assert shed2.main.roof_type == shed.main.roof_type

class TestComponents:
    def test_column_properties(self):
        column = ShedBimColumn(
            name="C1",
            material=create_test_material(),
            bottom=Vec3(0, 0, 0),
            top=Vec3(0, 0, 3000)
        )
        assert column.height == 3000
        assert column.direction == Vec3(0, 0, 1)
    
    def test_rafter_properties(self):
        rafter = ShedBimRafter(
            name="R1",
            material=create_test_material(),
            start=Vec3(0, 0, 3000),
            end=Vec3(3000, 0, 3500)
        )
        assert rafter.length > 3000
        assert rafter.slope > 0
```

#### Validation Criteria
- Data structures maintain referential integrity
- Parent-child relationships work correctly
- Serialization preserves all data
- Computed properties return correct values

## Session 4: Business Logic Testing

### Duration: 3 hours
### Focus: Construction algorithms and validation

#### Test Files
- `tests/business/test_building_input.py`
- `tests/business/test_structure_builder.py`
- `tests/business/test_engineering.py`

#### Test Cases

```python
# test_building_input.py
import pytest
from business.building_input import BuildingInput, BuildingType

class TestBuildingInput:
    def test_validation_rules(self):
        # Test span limits
        input_data = BuildingInput(
            building_type=BuildingType.CARPORT,
            span=3000,  # Minimum
            length=6000,
            height=2400
        )
        assert input_data.validate() == True
        
        # Test invalid span
        input_data.span = 15000  # Too large
        assert input_data.validate() == False
    
    def test_overhang_limits(self):
        input_data = BuildingInput(
            span=3600,
            roof_type=CarportRoofType.FLAT
        )
        input_data.overhang = 1000  # Too much for this span
        assert input_data.overhang == 600  # Should be clamped
    
    def test_bay_calculation(self):
        input_data = BuildingInput(
            length=9000,
            bays=3
        )
        assert input_data.bay_size == 3000

# test_structure_builder.py
class TestCarportBuilder:
    def test_14_step_pipeline(self):
        input_data = create_test_input()
        builder = CarportBuilder(input_data)
        
        # Test each step
        builder._initialize_bim()
        assert builder.bim is not None
        
        builder._find_slab_structure()
        assert builder.bim.main.slab is not None
        
        builder._find_columns_and_footings_structure()
        assert len(builder.bim.main.side_left.columns) > 0
        
        # Continue for all 14 steps...
    
    def test_engineering_integration(self):
        input_data = create_test_input()
        eng_data = EngData(
            eng_rafter="C15019",
            eng_column="C10019",
            eng_purlinsize="C10015",
            eng_purlinrow=3
        )
        
        builder = CarportBuilder(input_data, eng_data)
        carport = builder.create_carport()
        
        # Verify engineering data applied
        rafter = carport.main.roof_left.rafters[0]
        assert rafter.material.name == "C15019"
```

#### Validation Criteria
- Input validation catches all invalid configurations
- Builder creates structurally valid models
- Engineering data properly overrides defaults
- All 14 construction steps complete successfully

## Session 5: API Layer Testing

### Duration: 2 hours
### Focus: REST endpoints and encryption

#### Test Files
- `tests/api/test_endpoints.py`
- `tests/api/test_encryption.py`
- `tests/api/test_models.py`

#### Test Cases

```python
# test_endpoints.py
import pytest
from fastapi.testclient import TestClient
from api.main import app
from services.encryption import AESEncryption

client = TestClient(app)

class TestCarportEndpoint:
    def test_health_check(self):
        response = client.get("/health")
        assert response.status_code == 200
        assert response.json()["status"] == "healthy"
    
    def test_create_carport_encrypted(self):
        # Prepare encrypted request
        input_data = {
            "building_type": "Carport",
            "span": 6000,
            "length": 6000,
            "height": 2700
        }
        
        aes = AESEncryption("test-key")
        encrypted = aes.encrypt(json.dumps(input_data))
        
        response = client.post(
            "/api/carport/create",
            json={"encrypted_data": encrypted}
        )
        
        assert response.status_code == 200
        assert response.json()["success"] == True
        assert "file_url" in response.json()
    
    def test_invalid_encryption(self):
        response = client.post(
            "/api/carport/create",
            json={"encrypted_data": "invalid-base64"}
        )
        assert response.status_code == 400
    
    def test_download_file(self):
        # First create a carport
        create_response = create_test_carport()
        file_url = create_response.json()["file_url"]
        
        # Download the file
        response = client.get(file_url)
        assert response.status_code == 200
        assert len(response.content) > 0

# test_encryption.py
class TestEncryption:
    def test_round_trip(self):
        aes = AESEncryption("secret-key")
        plaintext = "Hello, World!"
        
        encrypted = aes.encrypt(plaintext)
        decrypted = aes.decrypt(encrypted)
        
        assert decrypted == plaintext
    
    def test_different_keys(self):
        aes1 = AESEncryption("key1")
        aes2 = AESEncryption("key2")
        
        encrypted = aes1.encrypt("test")
        
        with pytest.raises(Exception):
            aes2.decrypt(encrypted)
```

#### Validation Criteria
- All endpoints return correct status codes
- Encryption/decryption works correctly
- Error handling returns appropriate messages
- File downloads work properly

## Session 6: Output Generation Testing

### Duration: 3 hours
### Focus: File format generation

#### Test Files
- `tests/output/test_gltf.py`
- `tests/output/test_dxf.py`
- `tests/output/test_ifc.py`

#### Test Cases

```python
# test_gltf.py
import pytest
import json
from output.gltf.gltf_generator import GLTFGenerator

class TestGLTFGenerator:
    def test_basic_generation(self):
        carport = create_test_carport()
        generator = GLTFGenerator()
        
        gltf_data = generator.generate(carport)
        
        # Validate GLTF structure
        assert gltf_data["asset"]["version"] == "2.0"
        assert len(gltf_data["nodes"]) > 0
        assert len(gltf_data["meshes"]) > 0
    
    def test_material_generation(self):
        carport = create_test_carport()
        generator = GLTFGenerator()
        
        gltf_data = generator.generate(carport)
        
        # Check materials
        materials = gltf_data["materials"]
        assert len(materials) > 0
        assert "pbrMetallicRoughness" in materials[0]
    
    def test_geometry_accuracy(self):
        # Create simple column
        column = create_test_column()
        generator = GLTFGenerator()
        
        mesh_data = generator._generate_column_mesh(column)
        
        # Verify vertex count (8 for a box)
        assert len(mesh_data["vertices"]) == 8 * 3  # 8 vertices * 3 coords
        
        # Verify indices (12 triangles for a box)
        assert len(mesh_data["indices"]) == 12 * 3

# test_ifc.py
class TestIFCGenerator:
    def test_ifc_generation(self):
        import ifcopenshell
        
        carport = create_test_carport()
        generator = IFCGenerator()
        
        ifc_file = generator.generate(carport)
        
        # Validate IFC structure
        assert ifc_file.schema == "IFC4"
        
        # Check for required entities
        project = ifc_file.by_type("IfcProject")
        assert len(project) == 1
        
        columns = ifc_file.by_type("IfcColumn")
        assert len(columns) > 0
        
        beams = ifc_file.by_type("IfcBeam")
        assert len(beams) > 0
```

#### Validation Criteria
- Generated files are valid according to format specs
- All structural elements are included
- Materials and properties are preserved
- File sizes are reasonable

## Session 7: Integration Testing

### Duration: 4 hours
### Focus: End-to-end workflows

#### Test Files
- `tests/integration/test_full_workflow.py`
- `tests/integration/test_error_scenarios.py`

#### Test Cases

```python
# test_full_workflow.py
import pytest
import asyncio
from api.main import app
from fastapi.testclient import TestClient

class TestFullWorkflow:
    def test_complete_carport_generation(self):
        # 1. Create input
        input_data = {
            "building_type": "Carport",
            "name": "Test Carport",
            "roof_type": "Gable",
            "span": 6000,
            "length": 9000,
            "height": 2700,
            "bays": 3,
            "wind_speed": 32,
            "pitch": 10,
            "validate_engineering": False
        }
        
        # 2. Encrypt request
        encrypted_request = encrypt_input(input_data)
        
        # 3. Call API
        response = client.post("/api/carport/create", json=encrypted_request)
        assert response.status_code == 200
        
        # 4. Download GLTF
        file_url = response.json()["file_url"]
        gltf_response = client.get(file_url)
        assert gltf_response.status_code == 200
        
        # 5. Validate GLTF
        gltf_data = json.loads(gltf_response.content)
        assert validate_gltf(gltf_data)
        
        # 6. Check for expected components
        assert count_nodes_by_type(gltf_data, "column") == 8  # 4 per side
        assert count_nodes_by_type(gltf_data, "rafter") > 0
        assert count_nodes_by_type(gltf_data, "purlin") > 0
    
    def test_engineering_validation_flow(self):
        # Test with engineering validation enabled
        input_data = create_test_input()
        input_data["validate_engineering"] = True
        
        # Mock engineering service response
        with mock_engineering_service():
            response = generate_carport(input_data)
            
            # Verify engineering data was applied
            assert response.status_code == 200
            carport = load_generated_carport(response)
            assert carport.main.roof_left.rafters[0].material.name == "C15019"

# test_error_scenarios.py
class TestErrorScenarios:
    def test_invalid_span(self):
        input_data = create_test_input()
        input_data["span"] = 20000  # Too large
        
        response = generate_carport(input_data)
        assert response.status_code == 400
        assert "span" in response.json()["error"]
    
    def test_missing_required_fields(self):
        input_data = {"building_type": "Carport"}  # Missing required fields
        
        response = generate_carport(input_data)
        assert response.status_code == 400
    
    def test_engineering_service_failure(self):
        input_data = create_test_input()
        input_data["validate_engineering"] = True
        
        with mock_engineering_failure():
            response = generate_carport(input_data)
            # Should still succeed but without engineering optimization
            assert response.status_code == 200
```

#### Validation Criteria
- Complete workflows execute without errors
- Error scenarios are handled gracefully
- Performance meets requirements (< 5s for typical carport)
- Output files are valid and complete

## Session 8: Performance and Security Testing

### Duration: 3 hours
### Focus: Non-functional requirements

#### Test Files
- `tests/performance/test_load.py`
- `tests/security/test_security.py`

#### Test Cases

```python
# test_load.py
import pytest
import time
import concurrent.futures

class TestPerformance:
    def test_single_request_performance(self):
        start = time.time()
        response = generate_test_carport()
        duration = time.time() - start
        
        assert response.status_code == 200
        assert duration < 5.0  # Should complete in 5 seconds
    
    def test_concurrent_requests(self):
        # Test 10 concurrent requests
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = []
            for i in range(10):
                future = executor.submit(generate_test_carport)
                futures.append(future)
            
            results = [f.result() for f in futures]
            
        # All should succeed
        assert all(r.status_code == 200 for r in results)
    
    def test_large_carport_performance(self):
        # Test with maximum size carport
        input_data = {
            "span": 12000,
            "length": 30000,
            "bays": 10
        }
        
        start = time.time()
        response = generate_carport(input_data)
        duration = time.time() - start
        
        assert response.status_code == 200
        assert duration < 10.0  # Larger models take longer

# test_security.py
class TestSecurity:
    def test_sql_injection(self):
        # Try SQL injection in name field
        input_data = create_test_input()
        input_data["name"] = "'; DROP TABLE users; --"
        
        response = generate_carport(input_data)
        assert response.status_code in [200, 400]  # Should handle safely
    
    def test_xss_prevention(self):
        input_data = create_test_input()
        input_data["name"] = "<script>alert('xss')</script>"
        
        response = generate_carport(input_data)
        
        # Check that script tags are escaped in response
        if response.status_code == 200:
            assert "<script>" not in response.text
    
    def test_encryption_strength(self):
        # Test that encrypted data can't be tampered with
        encrypted = create_encrypted_request()
        
        # Modify one byte
        tampered = encrypted[:-1] + 'X'
        
        response = client.post("/api/carport/create", 
                              json={"encrypted_data": tampered})
        assert response.status_code == 400
```

#### Validation Criteria
- Single requests complete in < 5 seconds
- System handles 10+ concurrent requests
- Security vulnerabilities are mitigated
- Encryption prevents tampering

## Running All Tests

### Sequential Execution
```bash
# Run all tests in order
python run_all_tests.py
```

### Parallel Execution
```bash
# Run test sessions in parallel
pytest -n 8 tests/
```

### Generate Coverage Report
```bash
pytest --cov=src --cov-report=html tests/
```

## Test Data Helpers

```python
# tests/helpers.py
def create_test_input():
    return BuildingInput(
        building_type=BuildingType.CARPORT,
        name="Test Carport",
        roof_type=CarportRoofType.GABLE,
        span=6000,
        length=6000,
        height=2700,
        bays=2,
        wind_speed=32
    )

def create_test_material():
    return FrameMaterial(
        name="C10015",
        material_type=FrameMaterialType.C,
        width=102,
        height=51,
        thickness=1.5
    )

def create_test_carport():
    input_data = create_test_input()
    return CarportBuilder.create_carport(input_data)
```

## Success Criteria

### Coverage Goals
- Unit test coverage: > 80%
- Integration test coverage: > 70%
- API endpoint coverage: 100%

### Performance Goals
- Average response time: < 2 seconds
- 95th percentile: < 5 seconds
- Concurrent request handling: 50+ requests

### Quality Goals
- Zero critical bugs
- All edge cases handled
- Comprehensive error messages

## Continuous Integration

```yaml
# .github/workflows/test.yml
name: Test Suite

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.9
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install pytest pytest-cov
    - name: Run tests
      run: |
        pytest --cov=src --cov-report=xml
    - name: Upload coverage
      uses: codecov/codecov-action@v1
```

This comprehensive testing guide ensures all aspects of the BIM Backend are thoroughly tested and validated.