"""Tests for FrameMaterial class.

Tests align with C# FrameMaterial functionality in Materials.cs.
"""

import pytest
from materials import FrameMaterial, FrameMaterialType


class TestFrameMaterial:
    """Test FrameMaterial creation and properties.
    
    C# Ref: Materials.cs lines 275-463
    """
    
    def test_create_c_section(self):
        """Test creating C-section frame material.
        
        C# Ref: Materials.cs lines 369-384 - CreateC
        """
        c_section = FrameMaterial.create_c(
            name="C15015",
            section=150,
            is_b2b=False,
            web=150.0,
            flange=50.0,
            lip=15.0,
            thickness=1.5,
            web_hole_centers=60.0
        )
        
        assert c_section.name == "C15015"
        assert c_section.material_type == FrameMaterialType.C
        assert c_section.section == 150
        assert c_section.width == 50.0  # flange
        assert c_section.height == 150.0  # web
        assert c_section.thickness == 1.5
        assert c_section.is_b2b == False
        assert c_section.lip == 15.0
        assert c_section.web_hole_centers == 60.0
        assert c_section.flipped == False
        
        # Test properties
        assert c_section.web == 150.0
        assert c_section.flange == 50.0
        assert c_section.flange_single == 50.0  # Not B2B
        assert c_section.depth == 150.0
        assert c_section.id == "C15015"
    
    def test_create_c_section_b2b(self):
        """Test creating back-to-back C-section."""
        c_b2b = FrameMaterial.create_c(
            name="C15015 B2B",
            section=150,
            is_b2b=True,
            web=150.0,
            flange=100.0,  # Double flange for B2B
            lip=15.0,
            thickness=1.5,
            web_hole_centers=60.0
        )
        
        assert c_b2b.is_b2b == True
        assert c_b2b.flange == 100.0
        assert c_b2b.flange_single == 50.0  # Half of flange for B2B
    
    def test_create_z_section(self):
        """Test creating Z-section frame material.
        
        C# Ref: Materials.cs lines 386-402 - CreateZ
        """
        z_section = FrameMaterial.create_z(
            name="Z15012",
            section=150,
            web=150.0,
            flange_f=62.0,  # Broad flange
            flange_e=50.0,  # Narrow flange
            lip=12.0,
            thickness=1.2,
            web_hole_centers=60.0
        )
        
        assert z_section.name == "Z15012"
        assert z_section.material_type == FrameMaterialType.Z
        assert z_section.section == 150
        assert z_section.height == 150.0  # web
        assert z_section.thickness == 1.2
        assert z_section.z_flange_f == 62.0
        assert z_section.z_flange_e == 50.0
        assert z_section.lip == 12.0
        assert z_section.web_hole_centers == 60.0
        
        # Width calculation: flangeF + flangeE - thickness
        expected_width = round(62.0 + 50.0 - 1.2, 1)
        assert z_section.width == expected_width
    
    def test_create_th_section(self):
        """Test creating TopHat (TH) frame material.
        
        C# Ref: Materials.cs lines 404-416 - CreateTH
        """
        th_section = FrameMaterial.create_th(
            name="TH10010",
            section=100,
            width=100.0,
            depth=50.0,
            thickness=1.0
        )
        
        assert th_section.name == "TH10010"
        assert th_section.material_type == FrameMaterialType.TH
        assert th_section.section == 100
        assert th_section.width == 100.0
        assert th_section.height == 50.0  # depth becomes height
        assert th_section.thickness == 1.0
        assert th_section.depth == 50.0  # Property returns height
    
    def test_create_shs_section(self):
        """Test creating Square Hollow Section (SHS) frame material.
        
        C# Ref: Materials.cs lines 418-430 - CreateSHS
        """
        shs_section = FrameMaterial.create_shs(
            name="SHS10010",
            section=100,
            size=100.0,
            thickness=3.0
        )
        
        assert shs_section.name == "SHS10010"
        assert shs_section.material_type == FrameMaterialType.SHS
        assert shs_section.section == 100
        assert shs_section.width == 100.0
        assert shs_section.height == 100.0  # Square section
        assert shs_section.thickness == 3.0
    
    def test_create_pad_stile(self):
        """Test creating Personal Access Door stile frame material.
        
        C# Ref: Materials.cs lines 432-447 - CreatePadStile
        """
        pad_stile = FrameMaterial.create_pad_stile(
            name="PAD Stile",
            section=75,
            internal_width=50.0,
            height=75.0,
            thickness=1.5,
            rebate_width=10.0,
            rebate_height=15.0,
            rebate_tail=5.0
        )
        
        assert pad_stile.name == "PAD Stile"
        assert pad_stile.material_type == FrameMaterialType.PAD
        assert pad_stile.section == 75
        assert pad_stile.width == 53.0  # internal_width + thickness * 2
        assert pad_stile.height == 75.0
        assert pad_stile.thickness == 1.5
        assert pad_stile.pad_rebate_width == 10.0
        assert pad_stile.pad_rebate_height == 15.0
        assert pad_stile.pad_rebate_tail == 5.0
    
    def test_create_srdj(self):
        """Test creating Side Roller Door Jamb frame material.
        
        C# Ref: Materials.cs lines 449-462 - CreateSideRollerDoorJamb
        """
        srdj = FrameMaterial.create_side_roller_door_jamb(
            name="SRDJ",
            section=100,
            width=100.0,
            height=50.0,
            tail=25.0,
            thickness=2.0
        )
        
        assert srdj.name == "SRDJ"
        assert srdj.material_type == FrameMaterialType.SRDJ
        assert srdj.section == 100
        assert srdj.width == 100.0
        assert srdj.height == 50.0
        assert srdj.thickness == 2.0
        assert srdj.srdj_tail == 25.0
    
    def test_orientation_methods(self):
        """Test orientation methods.
        
        C# Ref: Materials.cs lines 352-367
        """
        material = FrameMaterial.create_c(
            name="C15015",
            section=150,
            is_b2b=False,
            web=150.0,
            flange=50.0,
            lip=15.0,
            thickness=1.5,
            web_hole_centers=60.0
        )
        
        # Test normal orientation (should return same object)
        normal = material.to_orientation_normal()
        assert normal is material  # Same object since already normal
        assert normal.flipped == False
        
        # Test flipped orientation
        flipped = material.to_orientation_flipped()
        assert flipped is not material  # New object
        assert flipped.flipped == True
        assert flipped.name == "C15015"  # Name unchanged
        assert flipped.id == "C15015 (flipped)"  # ID includes flipped
        
        # Test toggle
        toggled = material.to_orientation_toggle()
        assert toggled.flipped == True  # Was False, now True
        
        toggled_again = toggled.to_orientation_toggle()
        assert toggled_again.flipped == False  # Back to False
    
    def test_string_representation(self):
        """Test string representation.
        
        C# Ref: Materials.cs lines 342-345 - ToString
        """
        material = FrameMaterial(name="Test", material_type=FrameMaterialType.C)
        assert str(material) == "Test"
        
        flipped = material.to_orientation_flipped()
        assert str(flipped) == "Test (flipped)"
    
    def test_hash_code(self):
        """Test hash code generation.
        
        C# Ref: Materials.cs lines 347-350 - GetHashCode
        """
        material1 = FrameMaterial(name="Test", material_type=FrameMaterialType.C)
        material2 = FrameMaterial(name="Test", material_type=FrameMaterialType.C)
        material3 = material1.to_orientation_flipped()
        
        # Same ID should have same hash
        assert hash(material1) == hash(material2)
        
        # Different ID (flipped) should have different hash
        assert hash(material1) != hash(material3)
    
    def test_default_values(self):
        """Test default values for FrameMaterial."""
        material = FrameMaterial()
        
        assert material.name == ""
        assert material.material_type == FrameMaterialType.UNKNOWN
        assert material.flipped == False
        assert material.section == 0
        assert material.width == 0.0
        assert material.height == 0.0
        assert material.thickness == 0.0
        assert material.is_b2b == False
        assert material.lip == 0.0
        assert material.z_flange_f == 0.0
        assert material.z_flange_e == 0.0
        assert material.web_hole_centers == 0.0
        assert material.pad_rebate_width == 0.0
        assert material.pad_rebate_height == 0.0
        assert material.pad_rebate_tail == 0.0
        assert material.srdj_tail == 0.0