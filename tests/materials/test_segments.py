"""
Test suite for material segmentation.

Tests cladding and lining segmentation functionality.
"""

import pytest
from src.materials.segments import (
    MaterialSegmentHelper, MaterialSegment, MaterialSegmentData,
    ShedBimCladdingSegment, ShedBimLiningSegment, ShedBimCladdingHole
)
from src.materials.base import Cladding<PERSON>aterial, LiningMaterial
from src.geometry.primitives import Vec2, Vec3, Box3


class TestMaterialSegmentData:
    """Test MaterialSegmentData functionality."""
    
    def test_creation(self):
        """Test segment data creation."""
        outline = [Vec3(0, 0, 0), Vec3(1, 0, 0), Vec3(1, 1, 0), Vec3(0, 1, 0)]
        data = MaterialSegmentData(
            full_outline=outline,
            effective_outline=outline,
            sheet_width=762,
            full_length=3000,
            full_width=10000,
            is_full_width=True
        )
        
        assert len(data.full_outline) == 4
        assert data.sheet_width == 762
        assert data.full_length == 3000
        assert data.full_width == 10000
        assert data.is_full_width == True
    
    def test_default_is_full_width(self):
        """Test default is_full_width value."""
        data = MaterialSegmentData(
            full_outline=[],
            effective_outline=[],
            sheet_width=100,
            full_length=200,
            full_width=300
        )
        assert data.is_full_width == True


class TestMaterialSegment:
    """Test MaterialSegment functionality."""
    
    def test_creation_defaults(self):
        """Test segment creation with defaults."""
        segment = MaterialSegment()
        assert segment.full_outline == []
        assert segment.effective_outline == []
        assert segment.sheet_width == 0.0
        assert segment.full_length == 0.0
        assert segment.full_width == 0.0
    
    def test_creation_with_data(self):
        """Test segment creation with data."""
        outline = [Vec3(0, 0, 0), Vec3(1, 0, 0)]
        segment = MaterialSegment(
            full_outline=outline,
            effective_outline=outline,
            sheet_width=500,
            full_length=1000,
            full_width=2000
        )
        
        assert len(segment.full_outline) == 2
        assert segment.sheet_width == 500
        assert segment.full_length == 1000
        assert segment.full_width == 2000


class TestShedBimCladdingHole:
    """Test ShedBimCladdingHole functionality."""
    
    def test_creation(self):
        """Test hole creation."""
        outline = [Vec3(10, 10, 0), Vec3(20, 10, 0), Vec3(20, 20, 0), Vec3(10, 20, 0)]
        hole = ShedBimCladdingHole(outline=outline)
        assert len(hole.outline) == 4
        assert hole.outline[0] == Vec3(10, 10, 0)


class TestShedBimCladdingSegment:
    """Test ShedBimCladdingSegment functionality."""
    
    def test_creation(self):
        """Test cladding segment creation."""
        segment = ShedBimCladdingSegment()
        assert segment.full_outline == []
        assert segment.effective_outline == []
        assert segment.sheet_width == 0.0
        assert segment.full_length == 0.0
        assert segment.full_width == 0.0
        assert segment.holes == []
    
    def test_with_holes(self):
        """Test cladding segment with holes."""
        hole1 = ShedBimCladdingHole(outline=[Vec3(0, 0, 0)])
        hole2 = ShedBimCladdingHole(outline=[Vec3(1, 1, 0)])
        
        segment = ShedBimCladdingSegment(
            sheet_width=762,
            holes=[hole1, hole2]
        )
        
        assert len(segment.holes) == 2
        assert segment.sheet_width == 762


class TestShedBimLiningSegment:
    """Test ShedBimLiningSegment functionality."""
    
    def test_creation(self):
        """Test lining segment creation."""
        material = LiningMaterial(
            name="Test Lining",
            material_type="Fibreglass",
            width_per_roll=1200,
            thickness=50,
            overlap=50
        )
        
        segment = ShedBimLiningSegment(
            material=material,
            sheet_width=1100,
            full_length=3000
        )
        
        assert segment.material == material
        assert segment.sheet_width == 1100
        assert segment.full_length == 3000


class TestMaterialSegmentHelper:
    """Test MaterialSegmentHelper functionality."""
    
    def test_get_nearest_full_segment_from_skylight(self):
        """Test finding nearest full segment to skylight."""
        # Create test segments
        segment1 = MaterialSegmentData(
            full_outline=[],
            effective_outline=[],
            sheet_width=762,
            full_length=3000,
            full_width=10000,
            is_full_width=True
        )
        
        segment2 = MaterialSegmentData(
            full_outline=[],
            effective_outline=[],
            sheet_width=500,
            full_length=3000,
            full_width=10000,
            is_full_width=False  # Not full width
        )
        
        segment3 = MaterialSegmentData(
            full_outline=[],
            effective_outline=[],
            sheet_width=762,
            full_length=3000,
            full_width=10000,
            is_full_width=True
        )
        
        # Create centroid dictionary
        centroid_dict = {
            Vec3(100, 0, 0): segment1,
            Vec3(200, 0, 0): segment2,
            Vec3(300, 0, 0): segment3
        }
        
        # Skylight at position
        skylight_pos = Vec3(110, 0, 0)
        
        # Should find segment1 (closest full-width segment)
        nearest = MaterialSegmentHelper.get_nearest_full_segment_from_skylight(
            skylight_pos, centroid_dict
        )
        
        assert nearest == segment1
    
    def test_get_nearest_full_segment_no_full_width(self):
        """Test when no full-width segments exist."""
        segment = MaterialSegmentData(
            full_outline=[],
            effective_outline=[],
            sheet_width=500,
            full_length=3000,
            full_width=10000,
            is_full_width=False
        )
        
        centroid_dict = {Vec3(100, 0, 0): segment}
        skylight_pos = Vec3(110, 0, 0)
        
        nearest = MaterialSegmentHelper.get_nearest_full_segment_from_skylight(
            skylight_pos, centroid_dict
        )
        
        assert nearest is None
    
    def test_is_overlapping(self):
        """Test outline overlap detection."""
        # Two overlapping rectangles
        outline1 = [
            Vec3(0, 0, 0),
            Vec3(10, 0, 0),
            Vec3(10, 10, 0),
            Vec3(0, 10, 0)
        ]
        
        outline2 = [
            Vec3(5, 5, 0),
            Vec3(15, 5, 0),
            Vec3(15, 15, 0),
            Vec3(5, 15, 0)
        ]
        
        assert MaterialSegmentHelper._is_overlapping(outline1, outline2) == True
        
        # Two non-overlapping rectangles
        outline3 = [
            Vec3(20, 20, 0),
            Vec3(30, 20, 0),
            Vec3(30, 30, 0),
            Vec3(20, 30, 0)
        ]
        
        assert MaterialSegmentHelper._is_overlapping(outline1, outline3) == False
    
    def test_merge_outlines(self):
        """Test outline merging."""
        # Target outline
        target = [
            Vec3(0, 0, 0),
            Vec3(10, 0, 0),
            Vec3(10, 10, 0),
            Vec3(0, 10, 0)
        ]
        
        # Outline to merge
        to_merge = [
            Vec3(10, 0, 0),
            Vec3(20, 0, 0),
            Vec3(20, 10, 0),
            Vec3(10, 10, 0)
        ]
        
        MaterialSegmentHelper._merge_outlines(target, to_merge)
        
        # Should update points 2 and 3
        assert target[2] == Vec3(20, 10, 0)
        assert target[3] == Vec3(10, 10, 0)
    
    def test_get_roof_segments_from_outline_simple(self):
        """Test roof segment generation from simple outline."""
        # Simple rectangular roof outline
        outline = [
            Vec3(0, 0, 0),    # Bottom left
            Vec3(0, 3000, 0), # Top left
            Vec3(1524, 3000, 0), # Top right (2 sheets wide)
            Vec3(1524, 0, 0)  # Bottom right
        ]
        
        cover_width = 762  # Standard sheet width
        overlap = 50       # 100mm total overlap (50 each side)
        
        segments = MaterialSegmentHelper._get_roof_segments_from_outline(
            outline, cover_width, overlap
        )
        
        # Should create 2 segments for 1524mm width
        assert len(segments) == 2
        
        # Check first segment
        first = segments[0]
        assert len(first.full_outline) == 4
        assert len(first.effective_outline) == 4
        assert first.full_length == 3000
        
        # Check dimensions
        assert first.sheet_width == cover_width + 2 * overlap
    
    def test_get_roof_segments_from_outline_exact_fit(self):
        """Test when roof width exactly matches sheet coverage."""
        # Roof exactly 762mm wide (one sheet)
        outline = [
            Vec3(0, 0, 0),
            Vec3(0, 3000, 0),
            Vec3(762, 3000, 0),
            Vec3(762, 0, 0)
        ]
        
        cover_width = 762
        overlap = 50
        
        segments = MaterialSegmentHelper._get_roof_segments_from_outline(
            outline, cover_width, overlap
        )
        
        # Should create 1 segment
        assert len(segments) == 1
        
        segment = segments[0]
        assert segment.full_width == cover_width + 2 * overlap
    
    def test_get_roof_segments_effective_outline(self):
        """Test effective outline calculation with overlaps."""
        outline = [
            Vec3(0, 0, 0),
            Vec3(0, 1000, 0),
            Vec3(1000, 1000, 0),
            Vec3(1000, 0, 0)
        ]
        
        cover_width = 500
        overlap = 50
        
        segments = MaterialSegmentHelper._get_roof_segments_from_outline(
            outline, cover_width, overlap
        )
        
        # Check first segment effective outline
        first = segments[0]
        
        # First segment should not have left overlap
        assert first.effective_outline[0].x == first.full_outline[0].x
        assert first.effective_outline[1].x == first.full_outline[1].x
        
        # But should have right overlap
        assert first.effective_outline[2].x < first.full_outline[2].x
        assert first.effective_outline[3].x < first.full_outline[3].x
    
    def test_get_roof_segments_partial_last_sheet(self):
        """Test when last sheet is partial width."""
        # Roof width that doesn't divide evenly
        outline = [
            Vec3(0, 0, 0),
            Vec3(0, 3000, 0),
            Vec3(1000, 3000, 0),  # Not exact multiple of sheet width
            Vec3(1000, 0, 0)
        ]
        
        cover_width = 762
        overlap = 50
        
        segments = MaterialSegmentHelper._get_roof_segments_from_outline(
            outline, cover_width, overlap
        )
        
        # Last segment should be partial width
        last = segments[-1]
        
        # Sheet width should be less than full sheet
        assert last.sheet_width < (cover_width + 2 * overlap)
    
    def test_segment_dimensions(self):
        """Test segment dimension calculations."""
        outline = [
            Vec3(0, 0, 0),
            Vec3(0, 2500, 0),
            Vec3(3000, 2500, 0),
            Vec3(3000, 0, 0)
        ]
        
        segments = MaterialSegmentHelper._get_roof_segments_from_outline(
            outline, 762, 50
        )
        
        # All segments should have same length
        for segment in segments:
            assert segment.full_length == 2500
        
        # Total covered width should match roof width
        total_coverage = sum(s.sheet_width for s in segments)
        # Account for overlaps between sheets
        overlaps = (len(segments) - 1) * 50
        effective_coverage = total_coverage - overlaps
        
        # Should cover the full width (within overlap tolerance)
        assert abs(effective_coverage - 3000) < 100


if __name__ == "__main__":
    pytest.main([__file__, "-v"])