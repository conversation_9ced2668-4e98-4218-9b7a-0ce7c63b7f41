"""Tests for Mesh3d and visual properties.

Tests mesh functionality and visual property management.
"""

import pytest
import math
from geometry import Vec3, Box3, TriIndex, Mat4
from materials import Mesh3d, TextureMapping, MaterialAppearance, MaterialProfile


class TestMesh3d:
    """Test Mesh3d functionality.
    
    C# Ref: MeshHelper.cs lines 443-449
    """
    
    def test_empty_mesh(self):
        """Test empty mesh creation."""
        mesh = Mesh3d()
        
        assert len(mesh.vertexes) == 0
        assert len(mesh.indexes) == 0
        assert mesh.get_vertex_count() == 0
        assert mesh.get_triangle_count() == 0
        assert not mesh.is_valid()
    
    def test_create_box_mesh(self):
        """Test creating a box mesh."""
        box = Box3(Vec3(0, 0, 0), Vec3(10, 10, 10))
        mesh = Mesh3d.create_box(box)
        
        # Should have 8 vertices (box corners)
        assert mesh.get_vertex_count() == 8
        
        # Should have 12 triangles (2 per face, 6 faces)
        assert mesh.get_triangle_count() == 12
        
        # Should be valid
        assert mesh.is_valid()
        
        # Bounds should match input box
        assert mesh.bounds == box
    
    def test_calculate_bounds(self):
        """Test bounds calculation from vertices."""
        mesh = Mesh3d()
        
        # Add some vertices
        mesh.vertexes = [
            Vec3(0, 0, 0),
            Vec3(10, 0, 0),
            Vec3(0, 20, 0),
            Vec3(0, 0, 30),
        ]
        
        # Add a triangle
        mesh.indexes = [TriIndex(0, 1, 2)]
        
        # Calculate bounds
        mesh.calculate_bounds()
        
        expected_bounds = Box3(Vec3(0, 0, 0), Vec3(10, 20, 30))
        assert mesh.bounds == expected_bounds
    
    def test_mesh_validation(self):
        """Test mesh validation."""
        mesh = Mesh3d()
        
        # Empty mesh is invalid
        assert not mesh.is_valid()
        
        # Mesh with vertices but no indices is invalid
        mesh.vertexes = [Vec3(0, 0, 0), Vec3(1, 0, 0), Vec3(0, 1, 0)]
        assert not mesh.is_valid()
        
        # Valid mesh
        mesh.indexes = [TriIndex(0, 1, 2)]
        assert mesh.is_valid()
        
        # Invalid index
        mesh.indexes = [TriIndex(0, 1, 5)]  # Index 5 doesn't exist
        assert not mesh.is_valid()
    
    def test_mesh_transform(self):
        """Test mesh transformation."""
        # Create a simple triangle mesh
        mesh = Mesh3d()
        mesh.vertexes = [
            Vec3(0, 0, 0),
            Vec3(1, 0, 0),
            Vec3(0, 1, 0),
        ]
        mesh.indexes = [TriIndex(0, 1, 2)]
        mesh.calculate_bounds()
        
        # Create a translation matrix
        transform = Mat4.create_translation(10, 20, 30)
        
        # Transform the mesh
        transformed = mesh.transform(transform)
        
        # Check transformed vertices
        assert transformed.vertexes[0] == Vec3(10, 20, 30)
        assert transformed.vertexes[1] == Vec3(11, 20, 30)
        assert transformed.vertexes[2] == Vec3(10, 21, 30)
        
        # Indices should remain the same
        assert transformed.indexes == mesh.indexes
        
        # Bounds should be updated
        assert transformed.bounds == Box3(Vec3(10, 20, 30), Vec3(11, 21, 30))
    
    def test_mesh_merge(self):
        """Test merging two meshes."""
        # Create first triangle
        mesh1 = Mesh3d()
        mesh1.vertexes = [
            Vec3(0, 0, 0),
            Vec3(1, 0, 0),
            Vec3(0, 1, 0),
        ]
        mesh1.indexes = [TriIndex(0, 1, 2)]
        
        # Create second triangle
        mesh2 = Mesh3d()
        mesh2.vertexes = [
            Vec3(2, 0, 0),
            Vec3(3, 0, 0),
            Vec3(2, 1, 0),
        ]
        mesh2.indexes = [TriIndex(0, 1, 2)]
        
        # Merge meshes
        merged = mesh1.merge(mesh2)
        
        # Should have 6 vertices total
        assert merged.get_vertex_count() == 6
        
        # Should have 2 triangles
        assert merged.get_triangle_count() == 2
        
        # Check second triangle indices are offset
        assert merged.indexes[0] == TriIndex(0, 1, 2)  # First triangle unchanged
        assert merged.indexes[1] == TriIndex(3, 4, 5)  # Second triangle offset by 3


class TestTextureMapping:
    """Test texture mapping functionality."""
    
    def test_default_texture_mapping(self):
        """Test default texture mapping values."""
        mapping = TextureMapping()
        
        assert mapping.u_scale == 1.0
        assert mapping.v_scale == 1.0
        assert mapping.u_offset == 0.0
        assert mapping.v_offset == 0.0
        assert mapping.rotation == 0.0
    
    def test_apply_scale(self):
        """Test texture scaling."""
        mapping = TextureMapping(u_scale=2.0, v_scale=0.5)
        
        u, v = mapping.apply_to_uv(1.0, 1.0)
        assert u == 2.0  # Scaled by 2
        assert v == 0.5  # Scaled by 0.5
    
    def test_apply_offset(self):
        """Test texture offset."""
        mapping = TextureMapping(u_offset=0.5, v_offset=-0.25)
        
        u, v = mapping.apply_to_uv(1.0, 1.0)
        assert u == 1.5   # 1.0 + 0.5
        assert v == 0.75  # 1.0 - 0.25
    
    def test_apply_rotation(self):
        """Test texture rotation."""
        mapping = TextureMapping(rotation=math.pi / 2)  # 90 degrees
        
        u, v = mapping.apply_to_uv(1.0, 0.0)
        assert abs(u - 0.0) < 1e-10  # Rotated to (0, 1)
        assert abs(v - 1.0) < 1e-10
    
    def test_combined_transform(self):
        """Test combined scale, rotation, and offset."""
        mapping = TextureMapping(
            u_scale=2.0,
            v_scale=2.0,
            rotation=math.pi / 4,  # 45 degrees
            u_offset=0.5,
            v_offset=0.5
        )
        
        u, v = mapping.apply_to_uv(1.0, 0.0)
        # After scale: (2, 0)
        # After 45° rotation: (√2, √2)
        # After offset: (√2 + 0.5, √2 + 0.5)
        expected = math.sqrt(2)
        assert abs(u - (expected + 0.5)) < 1e-10
        assert abs(v - (expected + 0.5)) < 1e-10


class TestMaterialAppearance:
    """Test material appearance properties."""
    
    def test_default_appearance(self):
        """Test default material appearance."""
        appearance = MaterialAppearance()
        
        assert appearance.base_color is None
        assert appearance.metallic == 0.0
        assert appearance.roughness == 0.5
        assert appearance.opacity == 1.0
        assert appearance.emissive_color is None
        assert appearance.emissive_intensity == 0.0
        assert appearance.texture_mapping is None
        
        assert not appearance.is_transparent()
        assert not appearance.is_emissive()
    
    def test_transparent_material(self):
        """Test transparent material detection."""
        appearance = MaterialAppearance(opacity=0.5)
        assert appearance.is_transparent()
        
        appearance.opacity = 1.0
        assert not appearance.is_transparent()
    
    def test_emissive_material(self):
        """Test emissive material detection."""
        from materials import ColorMaterial
        
        # Not emissive without color
        appearance = MaterialAppearance(emissive_intensity=1.0)
        assert not appearance.is_emissive()
        
        # Not emissive without intensity
        appearance = MaterialAppearance(
            emissive_color=ColorMaterial.from_rgb("Glow", "", 255, 0, 0)
        )
        assert not appearance.is_emissive()
        
        # Emissive with both
        appearance = MaterialAppearance(
            emissive_color=ColorMaterial.from_rgb("Glow", "", 255, 0, 0),
            emissive_intensity=0.5
        )
        assert appearance.is_emissive()


class TestMaterialProfile:
    """Test material profile functionality."""
    
    def test_empty_profile(self):
        """Test empty profile."""
        profile = MaterialProfile()
        
        assert len(profile.points) == 0
        assert profile.is_closed == True
        assert profile.thickness == 0.0
        
        # Empty profile has zero bounds
        assert profile.get_bounding_box() == (0, 0, 0, 0)
        assert profile.get_width() == 0
        assert profile.get_height() == 0
    
    def test_profile_bounds(self):
        """Test profile bounding box calculation."""
        profile = MaterialProfile(
            points=[
                (0, 0),
                (10, 0),
                (10, 5),
                (5, 5),
                (5, 3),
                (0, 3),
            ],
            thickness=0.5
        )
        
        min_x, min_y, max_x, max_y = profile.get_bounding_box()
        assert min_x == 0
        assert min_y == 0
        assert max_x == 10
        assert max_y == 5
        
        assert profile.get_width() == 10
        assert profile.get_height() == 5


class TestMaterialVisualizer:
    """Test material visualizer utilities."""
    
    def test_get_material_appearance(self):
        """Test getting default appearances for material types."""
        from materials import MaterialVisualizer
        
        # Test metal appearance
        metal = MaterialVisualizer.get_material_appearance("metal")
        assert metal.metallic == 0.8
        assert metal.roughness == 0.3
        assert metal.base_color is not None
        
        # Test glass appearance
        glass = MaterialVisualizer.get_material_appearance("glass")
        assert glass.metallic == 0.0
        assert glass.roughness == 0.1
        assert glass.opacity == 0.8
        assert glass.is_transparent()
        
        # Test concrete appearance
        concrete = MaterialVisualizer.get_material_appearance("concrete")
        assert concrete.metallic == 0.0
        assert concrete.roughness == 0.8
        
        # Test unknown type
        unknown = MaterialVisualizer.get_material_appearance("unknown_material")
        assert unknown.base_color is None  # Default appearance
    
    def test_interpolate_color(self):
        """Test color interpolation."""
        from materials import ColorMaterial, MaterialVisualizer
        
        color1 = ColorMaterial.from_rgb("Start", "CB", 0, 0, 0)
        color2 = ColorMaterial.from_rgb("End", "CB", 255, 255, 255)
        
        # Test 0% interpolation
        result = MaterialVisualizer.interpolate_color(color1, color2, 0.0)
        assert result.r == 0
        assert result.g == 0
        assert result.b == 0
        
        # Test 50% interpolation
        result = MaterialVisualizer.interpolate_color(color1, color2, 0.5)
        assert result.r == 127
        assert result.g == 127
        assert result.b == 127
        
        # Test 100% interpolation
        result = MaterialVisualizer.interpolate_color(color1, color2, 1.0)
        assert result.r == 255
        assert result.g == 255
        assert result.b == 255
        
        # Test clamping
        result = MaterialVisualizer.interpolate_color(color1, color2, 1.5)
        assert result.r == 255  # Clamped to 1.0