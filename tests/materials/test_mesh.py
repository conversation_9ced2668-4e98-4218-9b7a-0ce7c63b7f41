"""
Test suite for 3D mesh representation.

Tests the Mesh3d class and related functionality.
"""

import pytest
from src.materials.mesh import Mesh3d
from src.geometry.primitives import Vec3, Box3, TriIndex
from src.geometry.matrix import Mat4


class TestMesh3d:
    """Test Mesh3d functionality."""
    
    def test_creation(self):
        """Test mesh creation with default values."""
        mesh = Mesh3d()
        assert mesh.vertexes == []
        assert mesh.indexes == []
        assert mesh.bounds == Box3.empty()
    
    def test_creation_with_data(self):
        """Test mesh creation with vertices and indices."""
        vertices = [
            Vec3(0, 0, 0),
            Vec3(1, 0, 0),
            Vec3(0, 1, 0)
        ]
        indices = [TriIndex(0, 1, 2)]
        
        mesh = Mesh3d(vertexes=vertices, indexes=indices)
        assert len(mesh.vertexes) == 3
        assert len(mesh.indexes) == 1
        assert mesh.vertexes[0] == Vec3(0, 0, 0)
        assert mesh.indexes[0] == TriIndex(0, 1, 2)
    
    def test_calculate_bounds(self):
        """Test bounding box calculation."""
        vertices = [
            Vec3(0, 0, 0),
            Vec3(1, 0, 0),
            Vec3(0, 1, 0),
            Vec3(0, 0, 1)
        ]
        mesh = Mesh3d(vertexes=vertices)
        
        mesh.calculate_bounds()
        
        assert mesh.bounds.min == Vec3(0, 0, 0)
        assert mesh.bounds.max == Vec3(1, 1, 1)
    
    def test_calculate_bounds_empty(self):
        """Test bounds calculation for empty mesh."""
        mesh = Mesh3d()
        mesh.calculate_bounds()
        assert mesh.bounds == Box3.empty()
    
    def test_calculate_bounds_negative_coords(self):
        """Test bounds calculation with negative coordinates."""
        vertices = [
            Vec3(-1, -2, -3),
            Vec3(4, 5, 6),
            Vec3(-5, 2, 0)
        ]
        mesh = Mesh3d(vertexes=vertices)
        
        mesh.calculate_bounds()
        
        assert mesh.bounds.min == Vec3(-5, -2, -3)
        assert mesh.bounds.max == Vec3(4, 5, 6)
    
    def test_get_triangle_count(self):
        """Test triangle count retrieval."""
        indices = [
            TriIndex(0, 1, 2),
            TriIndex(1, 2, 3),
            TriIndex(2, 3, 4)
        ]
        mesh = Mesh3d(indexes=indices)
        assert mesh.get_triangle_count() == 3
    
    def test_get_vertex_count(self):
        """Test vertex count retrieval."""
        vertices = [Vec3(i, i, i) for i in range(10)]
        mesh = Mesh3d(vertexes=vertices)
        assert mesh.get_vertex_count() == 10
    
    def test_is_valid_empty(self):
        """Test validity check for empty mesh."""
        mesh = Mesh3d()
        assert mesh.is_valid() == False
    
    def test_is_valid_no_indices(self):
        """Test validity check for mesh with vertices but no indices."""
        vertices = [Vec3(0, 0, 0), Vec3(1, 0, 0)]
        mesh = Mesh3d(vertexes=vertices)
        assert mesh.is_valid() == False
    
    def test_is_valid_no_vertices(self):
        """Test validity check for mesh with indices but no vertices."""
        indices = [TriIndex(0, 1, 2)]
        mesh = Mesh3d(indexes=indices)
        assert mesh.is_valid() == False
    
    def test_is_valid_correct(self):
        """Test validity check for valid mesh."""
        vertices = [
            Vec3(0, 0, 0),
            Vec3(1, 0, 0),
            Vec3(0, 1, 0)
        ]
        indices = [TriIndex(0, 1, 2)]
        mesh = Mesh3d(vertexes=vertices, indexes=indices)
        assert mesh.is_valid() == True
    
    def test_is_valid_invalid_indices(self):
        """Test validity check with out-of-range indices."""
        vertices = [
            Vec3(0, 0, 0),
            Vec3(1, 0, 0),
            Vec3(0, 1, 0)
        ]
        # Index 5 is out of range
        indices = [TriIndex(0, 1, 5)]
        mesh = Mesh3d(vertexes=vertices, indexes=indices)
        assert mesh.is_valid() == False
    
    def test_is_valid_negative_indices(self):
        """Test validity check with negative indices."""
        vertices = [Vec3(0, 0, 0), Vec3(1, 0, 0), Vec3(0, 1, 0)]
        indices = [TriIndex(-1, 1, 2)]
        mesh = Mesh3d(vertexes=vertices, indexes=indices)
        assert mesh.is_valid() == False
    
    def test_merge_meshes(self):
        """Test merging two meshes."""
        # First mesh: triangle at origin
        mesh1 = Mesh3d(
            vertexes=[
                Vec3(0, 0, 0),
                Vec3(1, 0, 0),
                Vec3(0, 1, 0)
            ],
            indexes=[TriIndex(0, 1, 2)]
        )
        
        # Second mesh: triangle offset
        mesh2 = Mesh3d(
            vertexes=[
                Vec3(2, 0, 0),
                Vec3(3, 0, 0),
                Vec3(2, 1, 0)
            ],
            indexes=[TriIndex(0, 1, 2)]
        )
        
        # Merge
        merged = mesh1.merge(mesh2)
        
        # Check vertices
        assert merged.get_vertex_count() == 6
        assert merged.vertexes[0] == Vec3(0, 0, 0)  # From mesh1
        assert merged.vertexes[3] == Vec3(2, 0, 0)  # From mesh2
        
        # Check indices
        assert merged.get_triangle_count() == 2
        assert merged.indexes[0] == TriIndex(0, 1, 2)  # Original from mesh1
        assert merged.indexes[1] == TriIndex(3, 4, 5)  # Offset from mesh2
        
        # Check bounds
        assert merged.bounds.min == Vec3(0, 0, 0)
        assert merged.bounds.max == Vec3(3, 1, 0)
    
    def test_merge_empty_meshes(self):
        """Test merging with empty meshes."""
        mesh1 = Mesh3d()
        mesh2 = Mesh3d(
            vertexes=[Vec3(0, 0, 0)],
            indexes=[TriIndex(0, 0, 0)]
        )
        
        # Merge empty with non-empty
        merged = mesh1.merge(mesh2)
        assert merged.get_vertex_count() == 1
        assert merged.get_triangle_count() == 1
        
        # Merge non-empty with empty
        merged2 = mesh2.merge(mesh1)
        assert merged2.get_vertex_count() == 1
        assert merged2.get_triangle_count() == 1
    
    def test_create_box(self):
        """Test creating a box mesh."""
        box = Box3(Vec3(0, 0, 0), Vec3(1, 1, 1))
        mesh = Mesh3d.create_box(box)
        
        # Should have 8 vertices (box corners)
        assert mesh.get_vertex_count() == 8
        
        # Should have 12 triangles (2 per face, 6 faces)
        assert mesh.get_triangle_count() == 12
        
        # Check bounds match input box
        assert mesh.bounds == box
        
        # Verify all vertices are at corners
        corners = [
            Vec3(0, 0, 0), Vec3(1, 0, 0), Vec3(1, 1, 0), Vec3(0, 1, 0),
            Vec3(0, 0, 1), Vec3(1, 0, 1), Vec3(1, 1, 1), Vec3(0, 1, 1)
        ]
        for vertex in mesh.vertexes:
            assert vertex in corners
    
    def test_create_box_non_unit(self):
        """Test creating a non-unit box mesh."""
        box = Box3(Vec3(-1, -2, -3), Vec3(4, 5, 6))
        mesh = Mesh3d.create_box(box)
        
        assert mesh.get_vertex_count() == 8
        assert mesh.get_triangle_count() == 12
        assert mesh.bounds == box
        
        # Check that vertices span the correct range
        x_coords = [v.x for v in mesh.vertexes]
        y_coords = [v.y for v in mesh.vertexes]
        z_coords = [v.z for v in mesh.vertexes]
        
        assert min(x_coords) == -1 and max(x_coords) == 4
        assert min(y_coords) == -2 and max(y_coords) == 5
        assert min(z_coords) == -3 and max(z_coords) == 6
    
    def test_create_box_triangulation(self):
        """Test that box triangulation is valid."""
        box = Box3(Vec3(0, 0, 0), Vec3(1, 1, 1))
        mesh = Mesh3d.create_box(box)
        
        # Verify mesh is valid
        assert mesh.is_valid()
        
        # Check specific triangles for bottom face
        # Bottom face should use vertices 0, 1, 2, 3
        bottom_triangles = [mesh.indexes[0], mesh.indexes[1]]
        
        # Verify indices are in correct range
        for tri in bottom_triangles:
            assert 0 <= tri.i1 <= 3
            assert 0 <= tri.i2 <= 3
            assert 0 <= tri.i3 <= 3
    
    def test_mesh_independence(self):
        """Test that merged meshes are independent."""
        mesh1 = Mesh3d(
            vertexes=[Vec3(0, 0, 0), Vec3(1, 0, 0)],
            indexes=[TriIndex(0, 1, 0)]
        )
        mesh2 = Mesh3d(
            vertexes=[Vec3(2, 0, 0), Vec3(3, 0, 0)],
            indexes=[TriIndex(0, 1, 0)]
        )
        
        merged = mesh1.merge(mesh2)
        
        # Modify original meshes
        mesh1.vertexes[0] = Vec3(10, 10, 10)
        mesh2.indexes[0] = TriIndex(1, 0, 1)
        
        # Merged mesh should be unaffected
        assert merged.vertexes[0] == Vec3(0, 0, 0)
        assert merged.indexes[0] == TriIndex(0, 1, 0)
    
    def test_bounds_after_merge(self):
        """Test that bounds are correctly calculated after merge."""
        mesh1 = Mesh3d(
            vertexes=[
                Vec3(0, 0, 0),
                Vec3(1, 0, 0),
                Vec3(0, 1, 0)
            ],
            indexes=[TriIndex(0, 1, 2)]
        )
        mesh1.calculate_bounds()
        
        mesh2 = Mesh3d(
            vertexes=[
                Vec3(5, 5, 5),
                Vec3(6, 5, 5),
                Vec3(5, 6, 5)
            ],
            indexes=[TriIndex(0, 1, 2)]
        )
        mesh2.calculate_bounds()
        
        merged = mesh1.merge(mesh2)
        
        # Bounds should encompass both meshes
        assert merged.bounds.min == Vec3(0, 0, 0)
        assert merged.bounds.max == Vec3(6, 6, 5)
    
    def test_triangle_winding_order(self):
        """Test that triangle winding order is preserved."""
        vertices = [
            Vec3(0, 0, 0),
            Vec3(1, 0, 0),
            Vec3(0, 1, 0),
            Vec3(1, 1, 0)
        ]
        indices = [
            TriIndex(0, 1, 2),  # Counter-clockwise
            TriIndex(1, 3, 2)   # Counter-clockwise
        ]
        
        mesh = Mesh3d(vertexes=vertices, indexes=indices)
        
        # Verify indices are preserved exactly
        assert mesh.indexes[0].i1 == 0
        assert mesh.indexes[0].i2 == 1
        assert mesh.indexes[0].i3 == 2
        
        assert mesh.indexes[1].i1 == 1
        assert mesh.indexes[1].i2 == 3
        assert mesh.indexes[1].i3 == 2


if __name__ == "__main__":
    pytest.main([__file__, "-v"])