"""
Test suite for BIM opening components.

Tests doors, windows, roof openings, and related structures.
"""

import pytest
from src.bim.openings import (
    ShedBimOpenBay, ShedBimOpening, ShedBimRoofOpening,
    OpeningInfo, OpeningInfoDesign, RoofOpeningInfo, RoofOpeningInfoDesign,
    MezzanineStairsInfo, MezzanineStairsOrientation, IndustrialSlidingDoorDesign
)
from src.bim.components import (
    ShedBimSection, ShedBimColumn, ShedBimBracket, ColorMaterial
)
from src.bim.accessories import ShedBimFlashing, ShedBimRollerDoorCylinder
from src.geometry.primitives import Vec3


class TestShedBimOpenBay:
    """Test ShedBimOpenBay functionality."""
    
    def test_creation_defaults(self):
        """Test open bay creation with defaults."""
        bay = ShedBimOpenBay()
        assert bay.bay_num == 0
        assert bay.frame_num == 0
        assert bay.points == []
    
    def test_creation_with_data(self):
        """Test open bay creation with data."""
        points = [
            Vec3(0, 0, 0),
            Vec3(3000, 0, 0),
            Vec3(3000, 0, 3000),
            Vec3(0, 0, 3000)
        ]
        
        bay = ShedBimOpenBay(
            bay_num=2,
            frame_num=3,
            points=points
        )
        
        assert bay.bay_num == 2
        assert bay.frame_num == 3
        assert len(bay.points) == 4
        assert bay.points[0] == Vec3(0, 0, 0)
        assert bay.points[2] == Vec3(3000, 0, 3000)


class TestOpeningInfoDesign:
    """Test OpeningInfoDesign enum."""
    
    def test_enum_values(self):
        """Test opening design enum values."""
        assert OpeningInfoDesign.NONE.value == 0
        assert OpeningInfoDesign.ROLLER_DOOR.value == 1
        assert OpeningInfoDesign.PA_DOOR.value == 2
        assert OpeningInfoDesign.WINDOW.value == 3
        assert OpeningInfoDesign.GLASS_SLIDING_DOOR.value == 4
        assert OpeningInfoDesign.INDUSTRIAL_SLIDING_DOOR.value == 5


class TestOpeningInfo:
    """Test OpeningInfo functionality."""
    
    def test_creation_defaults(self):
        """Test opening info creation with defaults."""
        info = OpeningInfo()
        assert info.id == ""
        assert info.design == OpeningInfoDesign.NONE
        assert info.description == ""
        assert info.color is None
        assert info.width == 0.0
        assert info.height == 0.0
        assert info.opening_width == 0.0
        assert info.opening_height == 0.0
        assert info.rd_has_wind_lock == False
        assert info.pa_is_double == False
        assert info.pa_is_stable == False
        assert info.pa_is_swing_inward == False
        assert info.pa_is_swing_right == False
        assert info.wi_is_barn_window == False
        assert info.wi_barn_window_pitch_deg == 0.0
        assert info.ind_design is None
        assert info.ind_bay_slide == 0
        assert info.ind_track == 0
        assert info.ind_cladding_offset == 0.0
        assert info.ind_is_apex_sliding_door == False
    
    def test_roller_door_info(self):
        """Test roller door opening info."""
        info = OpeningInfo(
            id="RD-01",
            design=OpeningInfoDesign.ROLLER_DOOR,
            description="3m x 3m Roller Door",
            width=3000,
            height=3000,
            opening_width=2900,
            opening_height=2900,
            rd_has_wind_lock=True
        )
        
        assert info.design == OpeningInfoDesign.ROLLER_DOOR
        assert info.rd_has_wind_lock == True
        assert info.width == 3000
        assert info.height == 3000
    
    def test_pa_door_info(self):
        """Test personal access door info."""
        info = OpeningInfo(
            id="PA-01",
            design=OpeningInfoDesign.PA_DOOR,
            description="Double PA Door",
            width=1800,
            height=2100,
            pa_is_double=True,
            pa_is_stable=False,
            pa_is_swing_inward=True,
            pa_is_swing_right=False
        )
        
        assert info.design == OpeningInfoDesign.PA_DOOR
        assert info.pa_is_double == True
        assert info.pa_is_swing_inward == True
        assert info.pa_is_swing_right == False
    
    def test_window_info(self):
        """Test window opening info."""
        info = OpeningInfo(
            id="WIN-01",
            design=OpeningInfoDesign.WINDOW,
            description="Barn Window",
            width=1200,
            height=900,
            wi_is_barn_window=True,
            wi_barn_window_pitch_deg=15.0
        )
        
        assert info.design == OpeningInfoDesign.WINDOW
        assert info.wi_is_barn_window == True
        assert info.wi_barn_window_pitch_deg == 15.0
    
    def test_industrial_sliding_door_info(self):
        """Test industrial sliding door info."""
        info = OpeningInfo(
            id="ISD-01",
            design=OpeningInfoDesign.INDUSTRIAL_SLIDING_DOOR,
            description="Heavy Duty Sliding Door",
            width=6000,
            height=4000,
            ind_design=IndustrialSlidingDoorDesign.HEAVY_DUTY,
            ind_bay_slide=2,
            ind_track=1,
            ind_cladding_offset=150.0,
            ind_is_apex_sliding_door=True
        )
        
        assert info.design == OpeningInfoDesign.INDUSTRIAL_SLIDING_DOOR
        assert info.ind_design == IndustrialSlidingDoorDesign.HEAVY_DUTY
        assert info.ind_bay_slide == 2
        assert info.ind_cladding_offset == 150.0
        assert info.ind_is_apex_sliding_door == True


class TestShedBimOpening:
    """Test ShedBimOpening functionality."""
    
    def test_creation_defaults(self):
        """Test opening creation with defaults."""
        opening = ShedBimOpening()
        assert opening.info is None
        assert opening.outline == []
        assert opening.normal is None
        assert opening.jamb1 is None
        assert opening.jamb2 is None
        assert opening.header is None
        assert opening.header_bracket1 is None
        assert opening.header_bracket2 is None
        assert opening.sill is None
        assert opening.strong_jamb1 is None
        assert opening.strong_jamb2 is None
        assert opening.strong_header is None
        assert opening.strong_header_bracket1 is None
        assert opening.strong_header_bracket2 is None
        assert opening.stiffener1 is None
        assert opening.stiffener2 is None
        assert opening.v_slider is None
        assert opening.opening_brackets == []
        assert opening.roller_door_cylinder is None
    
    def test_opening_with_outline(self):
        """Test opening with outline geometry."""
        outline = [
            Vec3(1000, 0, 0),
            Vec3(4000, 0, 0),
            Vec3(4000, 0, 3000),
            Vec3(1000, 0, 3000)
        ]
        normal = Vec3(0, 1, 0)  # Facing Y direction
        
        opening = ShedBimOpening(
            outline=outline,
            normal=normal
        )
        
        assert len(opening.outline) == 4
        assert opening.normal == normal
        
        # Calculate opening dimensions
        width = opening.outline[1].x - opening.outline[0].x
        height = opening.outline[2].z - opening.outline[1].z
        assert width == 3000
        assert height == 3000
    
    def test_opening_with_jambs_and_header(self):
        """Test opening with structural elements."""
        # Create jambs
        jamb1 = ShedBimColumn(
            column=ShedBimSection(
                start_pos=Vec3(1000, 0, 0),
                end_pos=Vec3(1000, 0, 3000),
                tag="JAMB-01"
            )
        )
        
        jamb2 = ShedBimColumn(
            column=ShedBimSection(
                start_pos=Vec3(4000, 0, 0),
                end_pos=Vec3(4000, 0, 3000),
                tag="JAMB-02"
            )
        )
        
        # Create header
        header = ShedBimSection(
            start_pos=Vec3(1000, 0, 3000),
            end_pos=Vec3(4000, 0, 3000),
            tag="HEADER-01"
        )
        
        opening = ShedBimOpening(
            jamb1=jamb1,
            jamb2=jamb2,
            header=header
        )
        
        assert opening.jamb1.column.tag == "JAMB-01"
        assert opening.jamb2.column.tag == "JAMB-02"
        assert opening.header.tag == "HEADER-01"
    
    def test_roller_door_opening(self):
        """Test roller door specific opening."""
        info = OpeningInfo(
            design=OpeningInfoDesign.ROLLER_DOOR,
            width=3000,
            height=3000
        )
        
        cylinder = ShedBimRollerDoorCylinder(
            start_pos=Vec3(1000, 100, 3100),
            end_pos=Vec3(4000, 100, 3100),
            drum_radius=150
        )
        
        opening = ShedBimOpening(
            info=info,
            roller_door_cylinder=cylinder
        )
        
        assert opening.info.design == OpeningInfoDesign.ROLLER_DOOR
        assert opening.roller_door_cylinder.drum_radius == 150
    
    def test_opening_with_brackets(self):
        """Test opening with brackets."""
        opening = ShedBimOpening()
        
        # Add header brackets
        bracket1 = ShedBimBracket(location="HEADER_LEFT")
        bracket2 = ShedBimBracket(location="HEADER_RIGHT")
        
        opening.header_bracket1 = bracket1
        opening.header_bracket2 = bracket2
        
        # Add general opening brackets
        for i in range(4):
            bracket = ShedBimBracket(location=f"CORNER_{i}")
            opening.opening_brackets.append(bracket)
        
        assert opening.header_bracket1.location == "HEADER_LEFT"
        assert opening.header_bracket2.location == "HEADER_RIGHT"
        assert len(opening.opening_brackets) == 4


class TestRoofOpeningInfoDesign:
    """Test RoofOpeningInfoDesign enum."""
    
    def test_enum_values(self):
        """Test roof opening design enum values."""
        assert RoofOpeningInfoDesign.SKYLIGHT.value == 0
        assert RoofOpeningInfoDesign.ROOF_VENT.value == 1


class TestRoofOpeningInfo:
    """Test RoofOpeningInfo functionality."""
    
    def test_creation_defaults(self):
        """Test roof opening info creation with defaults."""
        info = RoofOpeningInfo()
        assert info.design == RoofOpeningInfoDesign.SKYLIGHT
        assert info.color is None
        assert info.rv_head_size == 0.0
        assert info.rv_head_depth == 0.0
        assert info.rv_neck_size == 0.0
    
    def test_skylight_info(self):
        """Test skylight roof opening info."""
        color = ColorMaterial(name="Clear", rgb=(200, 200, 255))
        
        info = RoofOpeningInfo(
            design=RoofOpeningInfoDesign.SKYLIGHT,
            color=color
        )
        
        assert info.design == RoofOpeningInfoDesign.SKYLIGHT
        assert info.color.name == "Clear"
    
    def test_roof_vent_info(self):
        """Test roof vent opening info."""
        info = RoofOpeningInfo(
            design=RoofOpeningInfoDesign.ROOF_VENT,
            rv_head_size=600,
            rv_head_depth=300,
            rv_neck_size=450
        )
        
        assert info.design == RoofOpeningInfoDesign.ROOF_VENT
        assert info.rv_head_size == 600
        assert info.rv_head_depth == 300
        assert info.rv_neck_size == 450


class TestShedBimRoofOpening:
    """Test ShedBimRoofOpening functionality."""
    
    def test_creation_defaults(self):
        """Test roof opening creation with defaults."""
        opening = ShedBimRoofOpening()
        assert opening.info is None
        assert opening.outline == []
        assert opening.normal is None
    
    def test_skylight_opening(self):
        """Test skylight roof opening."""
        info = RoofOpeningInfo(design=RoofOpeningInfoDesign.SKYLIGHT)
        outline = [
            Vec3(1000, 1000, 0),
            Vec3(1600, 1000, 0),
            Vec3(1600, 2800, 0),
            Vec3(1000, 2800, 0)
        ]
        normal = Vec3(0, 0, 1)  # Pointing up
        
        opening = ShedBimRoofOpening(
            info=info,
            outline=outline,
            normal=normal
        )
        
        assert opening.info.design == RoofOpeningInfoDesign.SKYLIGHT
        assert len(opening.outline) == 4
        assert opening.normal.z == 1


class TestMezzanineStairsOrientation:
    """Test MezzanineStairsOrientation enum."""
    
    def test_enum_values(self):
        """Test stairs orientation enum values."""
        assert MezzanineStairsOrientation.BACK_TO_FRONT.value == 0
        assert MezzanineStairsOrientation.FRONT_TO_BACK.value == 1


class TestMezzanineStairsInfo:
    """Test MezzanineStairsInfo functionality."""
    
    def test_creation_defaults(self):
        """Test stairs info creation with defaults."""
        info = MezzanineStairsInfo()
        assert info.description == ""
        assert info.orientation == MezzanineStairsOrientation.BACK_TO_FRONT
        assert info.height == 0.0
        assert info.length == 0.0
        assert info.width == 0.0
        assert info.num_treads == 0
        assert info.balustrade_left == False
        assert info.balustrade_right == False
    
    def test_stairs_configuration(self):
        """Test stairs configuration."""
        info = MezzanineStairsInfo(
            description="Main Mezzanine Stairs",
            orientation=MezzanineStairsOrientation.FRONT_TO_BACK,
            height=2700,
            length=3500,
            width=1200,
            num_treads=15,
            balustrade_left=True,
            balustrade_right=True
        )
        
        assert info.description == "Main Mezzanine Stairs"
        assert info.orientation == MezzanineStairsOrientation.FRONT_TO_BACK
        assert info.height == 2700
        assert info.length == 3500
        assert info.width == 1200
        assert info.num_treads == 15
        assert info.balustrade_left == True
        assert info.balustrade_right == True
    
    def test_get_length_with_clearance(self):
        """Test calculating total length with clearance."""
        info = MezzanineStairsInfo(
            length=3500,
            width=1200
        )
        
        # Total length = length + 2 * width (clearance at both ends)
        total = info.get_length_with_clearance()
        assert total == 3500 + 2 * 1200
        assert total == 5900
    
    def test_tread_calculations(self):
        """Test tread-related calculations."""
        info = MezzanineStairsInfo(
            height=2700,
            length=3500,
            num_treads=15
        )
        
        # Calculate rise per tread
        if info.num_treads > 0:
            rise_per_tread = info.height / info.num_treads
            assert rise_per_tread == 180.0  # 2700 / 15
        
        # Calculate run per tread (treads = risers - 1 typically)
        if info.num_treads > 1:
            run_per_tread = info.length / (info.num_treads - 1)
            assert run_per_tread == pytest.approx(250.0)  # 3500 / 14


class TestIndustrialSlidingDoorDesign:
    """Test IndustrialSlidingDoorDesign enum."""
    
    def test_enum_values(self):
        """Test industrial door design enum values."""
        assert IndustrialSlidingDoorDesign.STANDARD.value == 0
        assert IndustrialSlidingDoorDesign.HEAVY_DUTY.value == 1


if __name__ == "__main__":
    pytest.main([__file__, "-v"])