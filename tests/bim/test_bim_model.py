"""Tests for BIM data model implementation."""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.bim import *
from src.geometry import Vec3, Line1
from src.materials import FrameMaterial, FootingMaterial, FrameMaterialType
from src.materials.helpers import FootingMaterialHelper


def test_shed_bim_creation():
    """Test creating a basic ShedBim structure."""
    print("Testing ShedBim creation...")
    
    # Create a basic shed model
    shed = ShedBim()
    assert shed.main is None
    assert shed.leanto_left is None
    assert shed.leanto_right is None
    assert len(shed.outline_frames) == 0
    assert len(shed.component_tags) == 0
    
    print("✓ ShedBim creation successful")


def test_main_part_creation():
    """Test creating main building part."""
    print("\nTesting ShedBimPartMain creation...")
    
    main_part = ShedBimPartMain()
    main_part.roof_type = "GABLE"
    main_part.wall_span_extents = Line1(0, 6000)  # 6m span
    main_part.wall_length_extents = Line1(0, 9000)  # 9m length
    
    # Create sides
    main_part.side_left = ShedBimSide(wall_height=2400)
    main_part.side_right = ShedBimSide(wall_height=2400)
    
    # Create roofs
    main_part.roof_left = ShedBimRoof()
    main_part.roof_right = ShedBimRoof()
    
    # Test get methods
    sides = main_part.get_sides()
    assert len(sides) == 2
    assert sides[0].wall_height == 2400
    
    roofs = main_part.get_roofs()
    assert len(roofs) == 2
    
    print("✓ ShedBimPartMain creation successful")


def test_structural_components():
    """Test creating structural components."""
    print("\nTesting structural components...")
    
    # Create a column
    material = FrameMaterial(
        name="C15024",
        material_type=FrameMaterialType.C,
        width=64,
        height=152,
        thickness=2.4
    )
    
    section = ShedBimSection(
        start_pos=Vec3(0, 0, 0),
        end_pos=Vec3(0, 2400, 0),
        rotation=0,
        material=material,
        tag="COLUMN-1"
    )
    
    footing = ShedBimFooting(
        pos=Vec3(0, 0, 0),
        footing=FootingMaterialHelper.create_block(450, 450, 300),
        tag="F1"
    )
    
    column = ShedBimColumn(
        column=section,
        footing=footing
    )
    
    assert column.column.material.name == "C15024"
    assert column.footing.pos.y == 0
    
    print("✓ Structural components creation successful")


def test_wall_structure():
    """Test creating wall structure."""
    print("\nTesting wall structure...")
    
    wall = ShedBimWall()
    
    # Add girt bays
    girt_bay = ShedBimWallGirtBay()
    girt_material = FrameMaterial(
        name="C10019",
        material_type=FrameMaterialType.C,
        width=51,
        height=102,
        thickness=1.9
    )
    
    # Create girts
    for i in range(3):
        girt = ShedBimSection(
            start_pos=Vec3(0, 600 + i * 600, 0),
            end_pos=Vec3(3000, 600 + i * 600, 0),
            material=girt_material,
            tag=f"GIRT-{i+1}"
        )
        girt_bay.girts.append(girt)
    
    wall.girt_bays.append(girt_bay)
    
    assert len(wall.girt_bays) == 1
    assert len(wall.girt_bays[0].girts) == 3
    
    print("✓ Wall structure creation successful")


def test_opening_creation():
    """Test creating openings."""
    print("\nTesting opening creation...")
    
    # Create a roller door opening
    opening_info = OpeningInfo(
        id="RD1",
        design=OpeningInfoDesign.ROLLER_DOOR,
        description="3.0m x 2.1m Roller Door",
        width=3000,
        height=2100,
        opening_width=3000,
        opening_height=2100,
        rd_has_wind_lock=True
    )
    
    opening = ShedBimOpening(
        info=opening_info,
        outline=[
            Vec3(1500, 0, 0),
            Vec3(4500, 0, 0),
            Vec3(4500, 2100, 0),
            Vec3(1500, 2100, 0)
        ],
        normal=Vec3(0, 0, 1)
    )
    
    assert opening.info.design == OpeningInfoDesign.ROLLER_DOOR
    assert len(opening.outline) == 4
    
    print("✓ Opening creation successful")


def test_complete_building():
    """Test creating a complete building structure."""
    print("\nTesting complete building structure...")
    
    # Create the BIM model
    shed = ShedBim()
    
    # Create main part
    shed.main = ShedBimPartMain(
        roof_type="GABLE",
        wall_span_extents=Line1(0, 6000),
        wall_length_extents=Line1(0, 9000)
    )
    
    # Create left side with columns
    left_side = ShedBimSide(wall_height=2400)
    
    # Add 4 columns (one every 3m)
    for i in range(4):
        pos_x = i * 3000
        column = create_column(pos_x, 0, 0, 2400, "C15024", f"COL-L{i+1}")
        left_side.columns.append(column)
    
    shed.main.side_left = left_side
    
    # Create right side
    right_side = ShedBimSide(wall_height=2400)
    for i in range(4):
        pos_x = i * 3000
        column = create_column(pos_x, 6000, 0, 2400, "C15024", f"COL-R{i+1}")
        right_side.columns.append(column)
    
    shed.main.side_right = right_side
    
    # Add component tags
    shed.component_tags.append(
        ShedBimComponentTag(
            tag="FRAME",
            member="COLUMN",
            component="C15024",
            spacing="3000"
        )
    )
    
    # Verify structure
    assert shed.main is not None
    assert len(shed.main.side_left.columns) == 4
    assert len(shed.main.side_right.columns) == 4
    assert len(shed.component_tags) == 1
    
    print("✓ Complete building structure successful")


def create_column(x: float, z: float, y_start: float, y_end: float, 
                  material_name: str, tag: str) -> ShedBimColumn:
    """Helper to create a column."""
    material = FrameMaterial(
        name=material_name,
        material_type=FrameMaterialType.C,
        width=64,
        height=152,
        thickness=2.4
    )
    
    section = ShedBimSection(
        start_pos=Vec3(x, y_start, z),
        end_pos=Vec3(x, y_end, z),
        rotation=0,
        material=material,
        tag=tag
    )
    
    footing = ShedBimFooting(
        pos=Vec3(x, 0, z),
        footing=FootingMaterialHelper.create_block(450, 450, 300),
        tag=f"F-{tag}"
    )
    
    return ShedBimColumn(column=section, footing=footing)


def test_hierarchy_relationships():
    """Test hierarchical relationships in BIM model."""
    print("\nTesting hierarchy relationships...")
    
    # Create BIM with parts
    shed = ShedBim()
    shed.main = ShedBimPartMain()
    
    # Add ends to main part
    for i in range(2):
        end = ShedBimEnd(frame_num=i)
        end.wall = ShedBimWall()
        shed.main.ends.append(end)
    
    # Add mezzanine
    shed.main.mezz = ShedBimMezz()
    shed.main.mezz.bearers = []
    shed.main.mezz.joists = []
    
    # Add slab
    shed.main.slab = ShedBimSlab()
    shed.main.slab.middle = ShedBimSlabPiece(
        thickness=100,
        bottom_left=Vec3(0, 0, 0),
        bottom_right=Vec3(6000, 0, 0),
        top_left=Vec3(0, 0, 9000),
        top_right=Vec3(6000, 0, 9000)
    )
    
    # Verify hierarchy
    assert len(shed.main.ends) == 2
    assert shed.main.mezz is not None
    assert shed.main.slab.middle.is_slab is True
    assert shed.main.slab.middle.thickness == 100
    
    print("✓ Hierarchy relationships successful")


def run_all_tests():
    """Run all BIM model tests."""
    print("=" * 50)
    print("Running BIM Data Model Tests")
    print("=" * 50)
    
    test_shed_bim_creation()
    test_main_part_creation()
    test_structural_components()
    test_wall_structure()
    test_opening_creation()
    test_complete_building()
    test_hierarchy_relationships()
    
    print("\n" + "=" * 50)
    print("All BIM model tests passed! ✓")
    print("=" * 50)


if __name__ == "__main__":
    run_all_tests()