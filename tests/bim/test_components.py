"""
Test suite for BIM structural components.

Tests sections, columns, footings, brackets, and related components.
"""

import pytest
from src.bim.components import (
    ShedBimSection, ShedBimSectionCut, ShedBimColumn,
    ShedBimColumnCompositeSection, ShedBimFooting, ShedBimBracket,
    ShedBimFastener, ShedBimPair, BracketAttachment, ColorMaterial, Punching
)
from src.geometry.primitives import Vec3, Mat4, Box3, Basis3
from src.materials.base import FrameMaterial, FootingMaterial, BracketMaterial


class TestShedBimSection:
    """Test ShedBimSection functionality."""
    
    def test_creation_defaults(self):
        """Test section creation with defaults."""
        section = ShedBimSection()
        assert section.start_pos is None
        assert section.end_pos is None
        assert section.rotation == 0.0
        assert section.material is None
        assert section.tag == ""
        assert section.punchings == []
        assert section.section_cuts == []
        assert section.color is None
    
    def test_creation_with_geometry(self):
        """Test section creation with geometry data."""
        start = Vec3(0, 0, 0)
        end = Vec3(3000, 0, 0)
        material = FrameMaterial(
            name="C10010",
            material_type="C",
            width=102,
            height=51,
            thickness=1.0
        )
        
        section = ShedBimSection(
            start_pos=start,
            end_pos=end,
            rotation=45.0,
            material=material,
            tag="COL-01"
        )
        
        assert section.start_pos == start
        assert section.end_pos == end
        assert section.rotation == 45.0
        assert section.material == material
        assert section.tag == "COL-01"
    
    def test_add_punchings(self):
        """Test adding punchings to section."""
        section = ShedBimSection()
        
        # Add punchings
        punching1 = Punching(position=500.0, diameter=18.0, where="WEB")
        punching2 = Punching(position=1500.0, diameter=18.0, where="FLANGE_TOP")
        
        section.punchings.extend([punching1, punching2])
        assert len(section.punchings) == 2
        assert section.punchings[0].position == 500.0
        assert section.punchings[1].where == "FLANGE_TOP"
    
    def test_add_section_cuts(self):
        """Test adding cuts to section."""
        section = ShedBimSection()
        
        # Add section cut
        transform = Mat4.translation(100, 0, 0)
        box = Box3(Vec3(-50, -50, -50), Vec3(50, 50, 50))
        cut = ShedBimSectionCut(transform=transform, box=box)
        
        section.section_cuts.append(cut)
        assert len(section.section_cuts) == 1
        assert section.section_cuts[0].transform == transform
    
    def test_clone_shallow(self):
        """Test shallow cloning of section."""
        # Create section with data
        section = ShedBimSection(
            start_pos=Vec3(0, 0, 0),
            end_pos=Vec3(3000, 0, 0),
            rotation=90.0,
            tag="ORIGINAL"
        )
        section.punchings.append(Punching(position=1000.0))
        
        # Clone
        clone = section.clone_shallow()
        
        # Check values are copied
        assert clone.start_pos == section.start_pos
        assert clone.rotation == 90.0
        assert clone.tag == "ORIGINAL"
        
        # Check it's a shallow copy (lists are same object)
        assert clone.punchings is section.punchings
        
        # Modify original list affects clone
        section.punchings.append(Punching(position=2000.0))
        assert len(clone.punchings) == 2
    
    def test_section_length(self):
        """Test calculating section length."""
        section = ShedBimSection(
            start_pos=Vec3(0, 0, 0),
            end_pos=Vec3(3000, 4000, 0)
        )
        
        # Calculate length
        if section.start_pos and section.end_pos:
            length = (section.end_pos - section.start_pos).length()
            assert length == 5000.0  # 3-4-5 triangle


class TestShedBimSectionCut:
    """Test ShedBimSectionCut functionality."""
    
    def test_creation_defaults(self):
        """Test cut creation with defaults."""
        cut = ShedBimSectionCut()
        assert cut.transform is None
        assert cut.box is None
    
    def test_creation_with_data(self):
        """Test cut creation with data."""
        transform = Mat4.rotation_z(45)
        box = Box3(Vec3(-10, -10, -10), Vec3(10, 10, 10))
        
        cut = ShedBimSectionCut(transform=transform, box=box)
        assert cut.transform == transform
        assert cut.box == box
    
    def test_string_representation(self):
        """Test string representation."""
        cut = ShedBimSectionCut()
        assert str(cut) == "Transform:None, Box:None"
        
        # With data
        transform = Mat4.identity()
        box = Box3(Vec3(0, 0, 0), Vec3(100, 100, 100))
        cut2 = ShedBimSectionCut(transform=transform, box=box)
        
        result = str(cut2)
        assert "Transform:" in result
        assert "Box:" in result


class TestShedBimColumn:
    """Test ShedBimColumn functionality."""
    
    def test_creation_defaults(self):
        """Test column creation with defaults."""
        column = ShedBimColumn()
        assert column.column is None
        assert column.footing is None
        assert column.base_brackets is None
        assert column.top_brackets is None
    
    def test_creation_with_components(self):
        """Test column creation with all components."""
        # Create column section
        section = ShedBimSection(
            start_pos=Vec3(0, 0, 0),
            end_pos=Vec3(0, 0, 3000),
            tag="COL-01"
        )
        
        # Create footing
        footing = ShedBimFooting(
            pos=Vec3(0, 0, -600),
            tag="FTG-01"
        )
        
        # Create brackets
        base_bracket = ShedBimBracket(location="BASE")
        top_bracket = ShedBimBracket(location="TOP")
        
        base_pair = ShedBimPair[ShedBimBracket]()
        base_pair.add(base_bracket)
        
        top_pair = ShedBimPair[ShedBimBracket]()
        top_pair.add(top_bracket)
        
        # Create column
        column = ShedBimColumn(
            column=section,
            footing=footing,
            base_brackets=base_pair,
            top_brackets=top_pair
        )
        
        assert column.column == section
        assert column.footing == footing
        assert column.base_brackets.item1 == base_bracket
        assert column.top_brackets.item1 == top_bracket


class TestShedBimColumnCompositeSection:
    """Test ShedBimColumnCompositeSection functionality."""
    
    def test_creation_defaults(self):
        """Test composite section creation with defaults."""
        composite = ShedBimColumnCompositeSection()
        assert composite.section1 is None
        assert composite.section2 is None
    
    def test_creation_with_sections(self):
        """Test composite section with two sections."""
        section1 = ShedBimSection(tag="SEC-01")
        section2 = ShedBimSection(tag="SEC-02")
        
        composite = ShedBimColumnCompositeSection(
            section1=section1,
            section2=section2
        )
        
        assert composite.section1 == section1
        assert composite.section2 == section2


class TestShedBimFooting:
    """Test ShedBimFooting functionality."""
    
    def test_creation_defaults(self):
        """Test footing creation with defaults."""
        footing = ShedBimFooting()
        assert footing.pos is None
        assert footing.footing is None
        assert footing.tag == ""
    
    def test_creation_with_data(self):
        """Test footing creation with data."""
        pos = Vec3(1500, 2000, -600)
        material = FootingMaterial(
            footing_type="BORED",
            width=450,
            length=450,
            depth=600
        )
        
        footing = ShedBimFooting(
            pos=pos,
            footing=material,
            tag="FTG-05"
        )
        
        assert footing.pos == pos
        assert footing.footing == material
        assert footing.tag == "FTG-05"


class TestShedBimBracket:
    """Test ShedBimBracket functionality."""
    
    def test_creation_defaults(self):
        """Test bracket creation with defaults."""
        bracket = ShedBimBracket()
        assert bracket.material is None
        assert bracket.position == Vec3(0, 0, 0)
        assert bracket.basis == Basis3.unit_xyz()
        assert bracket.attachment is not None  # Created in __post_init__
        assert bracket.fasteners == []
        assert bracket.location == ""
    
    def test_post_init_attachment(self):
        """Test that attachment is created in post_init."""
        bracket = ShedBimBracket()
        assert isinstance(bracket.attachment, BracketAttachment)
        assert bracket.attachment.position == Vec3(0, 0, 0)
    
    def test_creation_with_data(self):
        """Test bracket creation with data."""
        material = BracketMaterial(
            name="APB11100-UF",
            mesh_name="Apex_Brackets.APB11100-UF"
        )
        position = Vec3(100, 200, 300)
        basis = Basis3.from_z(Vec3(0, 0, 1))
        
        bracket = ShedBimBracket(
            material=material,
            position=position,
            basis=basis,
            location="APEX"
        )
        
        assert bracket.material == material
        assert bracket.position == position
        assert bracket.basis == basis
        assert bracket.location == "APEX"
    
    def test_add_fasteners(self):
        """Test adding fasteners to bracket."""
        bracket = ShedBimBracket()
        
        # Add fasteners
        fastener1 = ShedBimFastener(name="M12x30")
        fastener2 = ShedBimFastener(name="M16x45")
        
        bracket.fasteners.extend([fastener1, fastener2])
        assert len(bracket.fasteners) == 2
        assert bracket.fasteners[0].name == "M12x30"
    
    def test_get_fastener_placement(self):
        """Test fastener placement calculation."""
        bracket = ShedBimBracket()
        placement = bracket.get_fastener_placement()
        assert placement == Vec3(0, 0, 0)


class TestShedBimFastener:
    """Test ShedBimFastener functionality."""
    
    def test_creation_default(self):
        """Test fastener creation with default."""
        fastener = ShedBimFastener()
        assert fastener.name == ""
    
    def test_creation_with_name(self):
        """Test fastener creation with name."""
        fastener = ShedBimFastener(name="M16x45")
        assert fastener.name == "M16x45"


class TestShedBimPair:
    """Test ShedBimPair generic container."""
    
    def test_creation_empty(self):
        """Test pair creation empty."""
        pair = ShedBimPair[ShedBimBracket]()
        assert pair.item1 is None
        assert pair.item2 is None
    
    def test_add_first_item(self):
        """Test adding first item to pair."""
        pair = ShedBimPair[ShedBimBracket]()
        bracket = ShedBimBracket(location="LEFT")
        
        pair.add(bracket)
        assert pair.item1 == bracket
        assert pair.item2 is None
    
    def test_add_second_item(self):
        """Test adding second item to pair."""
        pair = ShedBimPair[ShedBimBracket]()
        bracket1 = ShedBimBracket(location="LEFT")
        bracket2 = ShedBimBracket(location="RIGHT")
        
        pair.add(bracket1)
        pair.add(bracket2)
        
        assert pair.item1 == bracket1
        assert pair.item2 == bracket2
    
    def test_add_to_full_pair(self):
        """Test adding to full pair raises error."""
        pair = ShedBimPair[ShedBimBracket]()
        pair.add(ShedBimBracket())
        pair.add(ShedBimBracket())
        
        with pytest.raises(RuntimeError, match="Pair is full"):
            pair.add(ShedBimBracket())
    
    def test_add_none_raises_error(self):
        """Test adding None raises error."""
        pair = ShedBimPair[ShedBimBracket]()
        
        with pytest.raises(ValueError, match="Item cannot be None"):
            pair.add(None)
    
    def test_pair_with_different_types(self):
        """Test pair can hold different types."""
        # Pair of sections
        section_pair = ShedBimPair[ShedBimSection]()
        section_pair.add(ShedBimSection(tag="S1"))
        section_pair.add(ShedBimSection(tag="S2"))
        
        assert section_pair.item1.tag == "S1"
        assert section_pair.item2.tag == "S2"


class TestBracketAttachment:
    """Test BracketAttachment functionality."""
    
    def test_creation_default(self):
        """Test attachment creation with default."""
        attachment = BracketAttachment()
        assert attachment.position == Vec3(0, 0, 0)
    
    def test_creation_with_position(self):
        """Test attachment creation with position."""
        pos = Vec3(100, 200, 300)
        attachment = BracketAttachment(position=pos)
        assert attachment.position == pos


class TestColorMaterial:
    """Test ColorMaterial functionality."""
    
    def test_creation_defaults(self):
        """Test color creation with defaults."""
        color = ColorMaterial()
        assert color.name == ""
        assert color.rgb == (255, 255, 255)
    
    def test_creation_with_data(self):
        """Test color creation with data."""
        color = ColorMaterial(
            name="Monument",
            rgb=(50, 50, 51)
        )
        assert color.name == "Monument"
        assert color.rgb == (50, 50, 51)


class TestPunching:
    """Test Punching functionality."""
    
    def test_creation_defaults(self):
        """Test punching creation with defaults."""
        punching = Punching()
        assert punching.position == 0.0
        assert punching.diameter == 0.0
        assert punching.where == "WEB"
    
    def test_creation_with_data(self):
        """Test punching creation with data."""
        punching = Punching(
            position=1500.0,
            diameter=18.0,
            where="FLANGE_TOP"
        )
        assert punching.position == 1500.0
        assert punching.diameter == 18.0
        assert punching.where == "FLANGE_TOP"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])