"""
Test suite for BIM cladding and lining components.

Tests cladding, lining, skylight, and related components.
"""

import pytest
from src.bim.cladding import (
    MaterialSegmentData, ShedBimCladding, ShedBimLiningSegment,
    ShedBimCladdingSegment, ShedBimCladdingHole, ShedBimSkylight
)
from src.geometry.primitives import Vec3
from src.materials.base import CladdingMaterial, LiningMaterial, ColorMaterial


class TestMaterialSegmentData:
    """Test MaterialSegmentData base class."""
    
    def test_creation_defaults(self):
        """Test segment data creation with defaults."""
        data = MaterialSegmentData()
        assert data.start_offset == 0.0
        assert data.end_offset == 0.0
        assert data.width == 0.0
    
    def test_creation_with_values(self):
        """Test segment data creation with values."""
        data = MaterialSegmentData(
            start_offset=100.0,
            end_offset=50.0,
            width=762.0
        )
        assert data.start_offset == 100.0
        assert data.end_offset == 50.0
        assert data.width == 762.0


class TestShedBimCladding:
    """Test ShedBimCladding functionality."""
    
    def test_creation_defaults(self):
        """Test cladding creation with defaults."""
        cladding = ShedBimCladding()
        assert cladding.points == []
        assert cladding.normal is None
        assert cladding.right is None
        assert cladding.holes == []
        assert cladding.cladding is None
        assert cladding.cladding_segments == []
        assert cladding.skylights == []
        assert cladding.top_color is None
        assert cladding.bottom_color is None
        assert cladding.color is None
        assert cladding.lining_materials == []
        assert cladding.lining_segments == []
    
    def test_creation_with_geometry(self):
        """Test cladding creation with geometry data."""
        points = [
            Vec3(0, 0, 0),
            Vec3(3000, 0, 0),
            Vec3(3000, 0, 2500),
            Vec3(0, 0, 2500)
        ]
        normal = Vec3(0, 1, 0)  # Facing Y direction
        right = Vec3(1, 0, 0)    # X direction
        
        cladding = ShedBimCladding(
            points=points,
            normal=normal,
            right=right
        )
        
        assert len(cladding.points) == 4
        assert cladding.normal == normal
        assert cladding.right == right
    
    def test_creation_with_materials(self):
        """Test cladding creation with materials."""
        # Cladding material
        cladding_mat = CladdingMaterial(
            name="Corrugated",
            design="Corrugated",
            cover_width=762,
            rib_height=16,
            overlap=93,
            bmt=0.42,
            tct=0.48
        )
        
        # Colors
        top_color = ColorMaterial(name="Surfmist", finish="CB", r=228, g=226, b=213)
        bottom_color = ColorMaterial(name="Monument", finish="CB", r=50, g=50, b=51)
        
        # Lining materials
        lining = LiningMaterial(
            name="Fibreglass Blanket",
            material_type="Fibreglass",
            width_per_roll=1200,
            thickness=50,
            overlap=50
        )
        
        cladding = ShedBimCladding(
            cladding=cladding_mat,
            top_color=top_color,
            bottom_color=bottom_color,
            lining_materials=[lining]
        )
        
        assert cladding.cladding == cladding_mat
        assert cladding.top_color == top_color
        assert cladding.bottom_color == bottom_color
        assert len(cladding.lining_materials) == 1
        assert cladding.lining_materials[0] == lining
    
    def test_add_holes(self):
        """Test adding holes to cladding."""
        cladding = ShedBimCladding()
        
        # Create hole
        hole_points = [
            Vec3(1000, 0, 1000),
            Vec3(2000, 0, 1000),
            Vec3(2000, 0, 2000),
            Vec3(1000, 0, 2000)
        ]
        hole = ShedBimCladdingHole(points=hole_points)
        
        cladding.holes.append(hole)
        assert len(cladding.holes) == 1
        assert cladding.holes[0] == hole
    
    def test_add_skylights(self):
        """Test adding skylights to cladding."""
        cladding = ShedBimCladding()
        
        skylight = ShedBimSkylight(
            centroid=Vec3(1500, 0, 1500),
            sheet_length=1800,
            width=600,
            start_offset=100,
            end_offset=100
        )
        
        cladding.skylights.append(skylight)
        assert len(cladding.skylights) == 1
        assert cladding.skylights[0].centroid == Vec3(1500, 0, 1500)
    
    def test_segments_management(self):
        """Test managing cladding and lining segments."""
        cladding = ShedBimCladding()
        
        # Add cladding segment
        clad_segment = ShedBimCladdingSegment(
            width=762,
            start_offset=0,
            end_offset=0
        )
        cladding.cladding_segments.append(clad_segment)
        
        # Add lining segment
        lining_mat = LiningMaterial(
            name="Test Lining",
            material_type="Fibreglass",
            width_per_roll=1200,
            thickness=50
        )
        lining_segment = ShedBimLiningSegment(
            material=lining_mat,
            width=1100
        )
        cladding.lining_segments.append(lining_segment)
        
        assert len(cladding.cladding_segments) == 1
        assert len(cladding.lining_segments) == 1


class TestShedBimLiningSegment:
    """Test ShedBimLiningSegment functionality."""
    
    def test_creation_defaults(self):
        """Test lining segment creation with defaults."""
        segment = ShedBimLiningSegment()
        assert segment.material is None
        assert segment.cuts == []
        assert segment.start_offset == 0.0
        assert segment.end_offset == 0.0
        assert segment.width == 0.0
    
    def test_creation_with_data(self):
        """Test lining segment creation with data."""
        material = LiningMaterial(
            name="Foil Backed Blanket",
            material_type="Glasswool",
            width_per_roll=1200,
            thickness=75,
            overlap=50
        )
        
        segment = ShedBimLiningSegment(
            material=material,
            width=1100,
            start_offset=50,
            end_offset=50
        )
        
        assert segment.material == material
        assert segment.width == 1100
        assert segment.start_offset == 50
        assert segment.end_offset == 50
    
    def test_add_cuts(self):
        """Test adding cuts to lining segment."""
        segment = ShedBimLiningSegment()
        
        # Add cut for opening
        cut_points = [
            Vec3(500, 0, 500),
            Vec3(700, 0, 500),
            Vec3(700, 0, 700),
            Vec3(500, 0, 700)
        ]
        cut = ShedBimCladdingHole(points=cut_points)
        
        segment.cuts.append(cut)
        assert len(segment.cuts) == 1
        assert len(segment.cuts[0].points) == 4
    
    def test_inheritance(self):
        """Test that lining segment inherits from MaterialSegmentData."""
        segment = ShedBimLiningSegment(width=1200)
        
        # Should have MaterialSegmentData attributes
        assert hasattr(segment, 'start_offset')
        assert hasattr(segment, 'end_offset')
        assert hasattr(segment, 'width')
        assert segment.width == 1200


class TestShedBimCladdingSegment:
    """Test ShedBimCladdingSegment functionality."""
    
    def test_creation_defaults(self):
        """Test cladding segment creation with defaults."""
        segment = ShedBimCladdingSegment()
        assert segment.holes == []
        assert segment.start_offset == 0.0
        assert segment.end_offset == 0.0
        assert segment.width == 0.0
    
    def test_creation_with_data(self):
        """Test cladding segment creation with data."""
        segment = ShedBimCladdingSegment(
            width=762,
            start_offset=0,
            end_offset=93  # Overlap
        )
        
        assert segment.width == 762
        assert segment.start_offset == 0
        assert segment.end_offset == 93
    
    def test_add_holes(self):
        """Test adding holes to cladding segment."""
        segment = ShedBimCladdingSegment()
        
        # Add multiple holes
        hole1 = ShedBimCladdingHole(points=[Vec3(100, 0, 100)])
        hole2 = ShedBimCladdingHole(points=[Vec3(200, 0, 200)])
        
        segment.holes.extend([hole1, hole2])
        assert len(segment.holes) == 2
    
    def test_inheritance(self):
        """Test that cladding segment inherits from MaterialSegmentData."""
        segment = ShedBimCladdingSegment()
        assert isinstance(segment, MaterialSegmentData)


class TestShedBimCladdingHole:
    """Test ShedBimCladdingHole functionality."""
    
    def test_creation_empty(self):
        """Test hole creation with no points."""
        hole = ShedBimCladdingHole()
        assert hole.points == []
    
    def test_creation_with_points(self):
        """Test hole creation with points."""
        points = [
            Vec3(0, 0, 0),
            Vec3(100, 0, 0),
            Vec3(100, 0, 100),
            Vec3(0, 0, 100)
        ]
        
        hole = ShedBimCladdingHole(points=points)
        assert len(hole.points) == 4
        assert hole.points[0] == Vec3(0, 0, 0)
        assert hole.points[2] == Vec3(100, 0, 100)
    
    def test_circular_hole(self):
        """Test creating a circular hole approximation."""
        import math
        
        # Create circular hole with 8 points
        radius = 50
        center = Vec3(500, 0, 500)
        points = []
        
        for i in range(8):
            angle = (i / 8) * 2 * math.pi
            x = center.x + radius * math.cos(angle)
            z = center.z + radius * math.sin(angle)
            points.append(Vec3(x, center.y, z))
        
        hole = ShedBimCladdingHole(points=points)
        assert len(hole.points) == 8
        
        # Check all points are at correct radius
        for point in hole.points:
            dist = ((point.x - center.x)**2 + (point.z - center.z)**2)**0.5
            assert dist == pytest.approx(radius, rel=1e-5)


class TestShedBimSkylight:
    """Test ShedBimSkylight functionality."""
    
    def test_creation_defaults(self):
        """Test skylight creation with defaults."""
        skylight = ShedBimSkylight()
        assert skylight.centroid is None
        assert skylight.sheet_length == 0.0
        assert skylight.start_offset == 0.0
        assert skylight.end_offset == 0.0
        assert skylight.width == 0.0
    
    def test_creation_with_data(self):
        """Test skylight creation with data."""
        skylight = ShedBimSkylight(
            centroid=Vec3(1500, 3000, 0),  # Center position on roof
            sheet_length=1800,  # Standard 1.8m skylight
            width=600,
            start_offset=100,
            end_offset=100
        )
        
        assert skylight.centroid == Vec3(1500, 3000, 0)
        assert skylight.sheet_length == 1800
        assert skylight.width == 600
        assert skylight.start_offset == 100
        assert skylight.end_offset == 100
    
    def test_effective_dimensions(self):
        """Test calculating effective skylight dimensions."""
        skylight = ShedBimSkylight(
            sheet_length=1800,
            width=600,
            start_offset=100,
            end_offset=100
        )
        
        # Effective length accounts for offsets
        effective_length = skylight.sheet_length - skylight.start_offset - skylight.end_offset
        assert effective_length == 1600
        
        # Width remains the same
        assert skylight.width == 600
    
    def test_inheritance(self):
        """Test that skylight inherits from MaterialSegmentData."""
        skylight = ShedBimSkylight()
        assert isinstance(skylight, MaterialSegmentData)
        
        # Can use base class attributes
        skylight.width = 600
        assert skylight.width == 600


if __name__ == "__main__":
    pytest.main([__file__, "-v"])