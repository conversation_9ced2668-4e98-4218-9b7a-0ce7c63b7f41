"""
Tests for IFC output generator.

This module tests the IFC BIM exchange format generation functionality.
"""

import pytest
from pathlib import Path
import tempfile

# Skip tests if ifcopenshell is not installed
pytest.importorskip("ifcopenshell")

from src.output.ifc import IFCGenerator
from src.output import OutputFormat
from src.bim.shed_bim import Shed<PERSON><PERSON>, ShedBimPartMain
from src.bim.wall_roof import ShedBimSide, ShedBimRoof
from src.bim.components import Shed<PERSON>imC<PERSON>umn, Shed<PERSON>im<PERSON>after, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from src.geometry.primitives import Vec3


class TestIFCGenerator:
    """Test IFC generation functionality."""
    
    @pytest.fixture
    def generator(self):
        """Create IFC generator with temp directory."""
        with tempfile.TemporaryDirectory() as tmpdir:
            yield IFCGenerator(Path(tmpdir))
            
    @pytest.fixture
    def shed_bim(self):
        """Create a shed BIM structure for testing."""
        # Create main structure
        main = ShedBimPartMain(
            roof_type="Gable",
            roof_left=ShedBimRoof(
                cladding_material="Colorbond",
                cladding_reversed=False
            ),
            roof_right=ShedBimRoof(
                cladding_material="Colorbond",
                cladding_reversed=False
            ),
            side_left=ShedBimSide(),
            side_right=ShedBimSide()
        )
        
        # Add columns
        for i in range(3):
            x = i * 3000
            main.side_left.columns.append(
                ShedBimColumn(
                    base=Vec3(x, 0, 0),
                    top=Vec3(x, 0, 3000),
                    frame_material="C20030"
                )
            )
            main.side_right.columns.append(
                ShedBimColumn(
                    base=Vec3(x, 6000, 0),
                    top=Vec3(x, 6000, 3000),
                    frame_material="C20030"
                )
            )
            
        # Add rafters
        main.roof_left.rafters = [
            ShedBimRafter(
                start=Vec3(i * 3000, 0, 3000),
                end=Vec3(i * 3000, 3000, 3600),
                frame_material="C15024"
            )
            for i in range(3)
        ]
        
        # Add purlins
        main.roof_left.purlins = [
            ShedBimPurlin(
                start=Vec3(0, j * 1000, 3000 + j * 200),
                end=Vec3(6000, j * 1000, 3000 + j * 200),
                frame_material="C10015"
            )
            for j in range(4)
        ]
        
        return ShedBim(main=main)
        
    def test_get_supported_formats(self, generator):
        """Test supported format listing."""
        formats = generator.get_supported_formats()
        assert OutputFormat.IFC in formats
        assert OutputFormat.IFC4 in formats
        
    def test_generate_ifc4(self, generator, shed_bim):
        """Test IFC4 generation."""
        result = generator.generate(
            shed_bim, 
            "test_model",
            version="IFC4",
            author="Test Author",
            organization="Test Org"
        )
        
        assert result.success
        assert result.format == OutputFormat.IFC4
        assert result.file_path.exists()
        assert result.file_path.suffix == ".ifc4"
        assert result.file_size > 0
        
        # Verify IFC structure
        content = result.file_path.read_text()
        assert "ISO-10303-21;" in content
        assert "HEADER;" in content
        assert "DATA;" in content
        assert "ENDSEC;" in content
        assert "END-ISO-10303-21;" in content
        
    def test_ifc_header(self, generator, shed_bim):
        """Test IFC header generation."""
        result = generator.generate(
            shed_bim,
            "test_header",
            author="John Doe",
            organization="ACME Corp"
        )
        
        assert result.success
        
        content = result.file_path.read_text()
        assert "FILE_DESCRIPTION" in content
        assert "FILE_NAME" in content
        assert "FILE_SCHEMA(('IFC4'));" in content
        assert "John Doe" in content
        assert "ACME Corp" in content
        
    def test_ifc_project_structure(self, generator, shed_bim):
        """Test IFC project hierarchy."""
        result = generator.generate(shed_bim, "test_project")
        
        assert result.success
        
        content = result.file_path.read_text()
        # Check for IFC hierarchy
        assert "IFCPROJECT" in content
        assert "IFCSITE" in content
        assert "IFCBUILDING" in content
        assert "IFCBUILDINGSTOREY" in content
        
    def test_structural_elements(self, generator, shed_bim):
        """Test structural element generation."""
        result = generator.generate(shed_bim, "test_structure")
        
        assert result.success
        
        content = result.file_path.read_text()
        # Check for structural elements
        assert "IFCCOLUMN" in content
        assert "IFCBEAM" in content
        assert "Rafter" in content
        assert "Purlin" in content
        
    def test_material_definitions(self, generator, shed_bim):
        """Test material generation."""
        result = generator.generate(shed_bim, "test_materials")
        
        assert result.success
        
        content = result.file_path.read_text()
        assert "IFCMATERIAL" in content
        assert "C20030" in content  # Column material
        assert "C15024" in content  # Rafter material
        
    def test_guid_generation(self, generator):
        """Test GUID generation for IFC entities."""
        guid1 = generator._generate_guid()
        guid2 = generator._generate_guid()
        
        assert len(guid1) == 22
        assert guid1 != guid2
        
    def test_empty_bim(self, generator):
        """Test handling of empty BIM."""
        empty_bim = ShedBim(main=None)
        result = generator.generate(empty_bim, "empty")
        
        assert not result.success
        assert len(result.errors) > 0
        
    def test_unit_definitions(self, generator, shed_bim):
        """Test unit definitions in IFC."""
        result = generator.generate(shed_bim, "test_units")
        
        assert result.success
        
        content = result.file_path.read_text()
        assert "IFCSIUNIT" in content
        assert "LENGTHUNIT" in content
        assert "AREAUNIT" in content
        assert "VOLUMEUNIT" in content
        
    def test_relationships(self, generator, shed_bim):
        """Test IFC relationship entities."""
        result = generator.generate(shed_bim, "test_relationships")
        
        assert result.success
        
        content = result.file_path.read_text()
        # Check for relationship entities
        assert "IFCRELAGGREGATES" in content
        assert "IFCRELCONTAINEDINSPATIALSTRUCTURE" in content
        assert "IFCRELASSOCIATESMATERIAL" in content


class TestIFCEntityCreation:
    """Test IFC entity creation methods."""
    
    def test_entity_counter(self):
        """Test entity ID generation."""
        gen = IFCGenerator()
        
        id1 = gen._get_entity_id()
        id2 = gen._get_entity_id()
        id3 = gen._get_entity_id()
        
        assert id1 == "#1"
        assert id2 == "#2"
        assert id3 == "#3"
        
    def test_local_placement_creation(self):
        """Test local placement generation."""
        gen = IFCGenerator()
        ifc = []
        
        placement_id = gen._create_local_placement(
            ifc, 
            None,
            Vec3(1000, 2000, 3000)
        )
        
        assert placement_id == "#3"  # After point and axis
        assert "IFCCARTESIANPOINT((1000.0,2000.0,3000.0))" in " ".join(ifc)
        assert "IFCAXIS2PLACEMENT3D" in " ".join(ifc)
        assert "IFCLOCALPLACEMENT" in " ".join(ifc)