"""
Tests for GLTF output generator.

This module tests the GLTF/GLB generation functionality.
"""

import pytest
import json
import struct
from pathlib import Path
import tempfile

from src.output.gltf import GLTFGenerator
from src.output import OutputFormat
from src.bim.shed_bim import Shed<PERSON><PERSON>, ShedBimPartMain
from src.bim.wall_roof import ShedBimSide, ShedBimRoof
from src.bim.components import ShedBimColumn
from src.geometry.primitives import Vec3


class TestGLTFGenerator:
    """Test GLTF generation functionality."""
    
    @pytest.fixture
    def generator(self):
        """Create GLTF generator with temp directory."""
        with tempfile.TemporaryDirectory() as tmpdir:
            yield GLTFGenerator(Path(tmpdir))
            
    @pytest.fixture
    def simple_bim(self):
        """Create a simple BIM structure for testing."""
        # Create main structure
        main = ShedBimPartMain(
            roof_type="Flat",
            roof_left=ShedBimRoof(
                cladding_material="Colorbond",
                cladding_reversed=False
            ),
            side_left=ShedBimSide(),
            side_right=ShedBimSide()
        )
        
        # Add columns
        main.side_left.columns.append(
            ShedBimColumn(
                base=Vec3(0, 0, 0),
                top=Vec3(0, 0, 2400),
                frame_material="C15024"
            )
        )
        
        main.side_left.columns.append(
            ShedBimColumn(
                base=Vec3(3000, 0, 0),
                top=Vec3(3000, 0, 2400),
                frame_material="C15024"
            )
        )
        
        return ShedBim(main=main)
        
    def test_get_supported_formats(self, generator):
        """Test supported format listing."""
        formats = generator.get_supported_formats()
        assert OutputFormat.GLTF in formats
        assert OutputFormat.GLB in formats
        
    def test_generate_gltf(self, generator, simple_bim):
        """Test GLTF generation."""
        result = generator.generate(simple_bim, "test_model")
        
        assert result.success
        assert result.format == OutputFormat.GLTF
        assert result.file_path.exists()
        assert result.file_path.suffix == ".gltf"
        assert result.file_size > 0
        
        # Verify GLTF structure
        with open(result.file_path) as f:
            gltf_data = json.load(f)
            
        assert "asset" in gltf_data
        assert gltf_data["asset"]["version"] == "2.0"
        assert "scenes" in gltf_data
        assert "nodes" in gltf_data
        assert "meshes" in gltf_data
        
    def test_generate_glb(self, generator, simple_bim):
        """Test GLB binary generation."""
        result = generator.generate(simple_bim, "test_model", binary=True)
        
        assert result.success
        assert result.format == OutputFormat.GLB
        assert result.file_path.exists()
        assert result.file_path.suffix == ".glb"
        
        # Verify GLB header
        with open(result.file_path, 'rb') as f:
            magic = f.read(4)
            version = struct.unpack('<I', f.read(4))[0]
            
        assert magic == b'glTF'
        assert version == 2
        
    def test_coordinate_system_conversion(self, generator, simple_bim):
        """Test Y-up coordinate system conversion."""
        result = generator.generate(simple_bim, "test_yup", y_up=True)
        
        assert result.success
        
        # Check that root node has transformation
        with open(result.file_path) as f:
            gltf_data = json.load(f)
            
        root_node = gltf_data["nodes"][0]
        assert "matrix" in root_node
        
    def test_materials_generation(self, generator, simple_bim):
        """Test material generation."""
        result = generator.generate(simple_bim, "test_materials")
        
        assert result.success
        
        with open(result.file_path) as f:
            gltf_data = json.load(f)
            
        assert "materials" in gltf_data
        assert len(gltf_data["materials"]) > 0
        
        # Check material properties
        material = gltf_data["materials"][0]
        assert "name" in material
        assert "pbrMetallicRoughness" in material
        
    def test_empty_bim(self, generator):
        """Test handling of empty BIM."""
        empty_bim = ShedBim(main=None)
        result = generator.generate(empty_bim, "empty")
        
        assert not result.success
        assert len(result.errors) > 0
        
    def test_metadata_generation(self, generator, simple_bim):
        """Test metadata in output result."""
        result = generator.generate(simple_bim, "test_meta")
        
        assert result.success
        assert result.metadata is not None
        assert "generator" in result.metadata
        assert "column_count" in result.metadata
        assert result.metadata["column_count"] == 2


class TestGLTFMeshGeneration:
    """Test mesh generation functionality."""
    
    def test_column_mesh_generation(self):
        """Test column mesh creation."""
        from src.output.base.mesh_builder import MeshBuilder
        from src.materials.base import FrameMaterial, FrameMaterialType
        
        # Create frame material
        frame_mat = FrameMaterial(
            name="C15024",
            material_type=FrameMaterialType.C,
            width=150.0,
            height=24.0,
            thickness=1.5
        )
        
        # Generate mesh
        mesh = MeshBuilder.create_frame_profile(frame_mat, 2.4)
        
        assert len(mesh.vertices) > 0
        assert len(mesh.triangles) > 0
        assert mesh.material_name == "C15024"
        
    def test_sheet_mesh_generation(self):
        """Test sheet/panel mesh creation."""
        from src.output.base.mesh_builder import MeshBuilder
        
        corners = [
            Vec3(0, 0, 0),
            Vec3(1, 0, 0),
            Vec3(1, 1, 0),
            Vec3(0, 1, 0)
        ]
        
        mesh = MeshBuilder.create_sheet(corners, thickness=0.002)
        
        assert len(mesh.vertices) == 8  # 4 corners * 2 sides
        assert len(mesh.triangles) == 12  # 2 per face * 6 faces