"""
Tests for output service integration.

This module tests the output service that manages file generation and storage.
"""

import pytest
import asyncio
from pathlib import Path
import tempfile
import json
import zipfile
from datetime import datetime, timedelta

from src.services.output_service import OutputService, OutputManager
from src.output import OutputFormat
from src.bim.shed_bim import Shed<PERSON><PERSON>, ShedBimPartMain
from src.bim.wall_roof import ShedBimSide, ShedBimRoof
from src.bim.components import ShedBimColumn
from src.geometry.primitives import Vec3


class TestOutputService:
    """Test output service functionality."""
    
    @pytest.fixture
    def service(self):
        """Create output service with temp directories."""
        with tempfile.TemporaryDirectory() as output_dir:
            with tempfile.TemporaryDirectory() as temp_dir:
                yield OutputService(Path(output_dir), Path(temp_dir))
                
    @pytest.fixture
    def simple_bim(self):
        """Create a simple BIM for testing."""
        main = ShedBimPartMain(
            roof_type="Flat",
            roof_left=ShedBimRoof(
                cladding_material="Colorbond",
                cladding_reversed=False
            ),
            side_left=ShedBimSide(),
            side_right=ShedBimSide()
        )
        
        main.side_left.columns.append(
            ShedBimColumn(
                base=Vec3(0, 0, 0),
                top=Vec3(0, 0, 2400),
                frame_material="C15024"
            )
        )
        
        return ShedBim(main=main)
        
    @pytest.mark.asyncio
    async def test_generate_single_format(self, service, simple_bim):
        """Test generating a single output format."""
        result = await service.generate_output(
            simple_bim,
            "test_model",
            OutputFormat.GLTF
        )
        
        assert result.success
        assert result.format == OutputFormat.GLTF
        assert result.file_path.exists()
        
    @pytest.mark.asyncio
    async def test_generate_string_format(self, service, simple_bim):
        """Test generating with string format specification."""
        result = await service.generate_output(
            simple_bim,
            "test_model",
            "gltf"  # String instead of enum
        )
        
        assert result.success
        assert result.format == OutputFormat.GLTF
        
    @pytest.mark.asyncio
    async def test_generate_invalid_format(self, service, simple_bim):
        """Test handling of invalid format."""
        result = await service.generate_output(
            simple_bim,
            "test_model",
            "invalid_format"
        )
        
        assert not result.success
        assert "Unsupported format" in result.errors[0]
        
    @pytest.mark.asyncio
    async def test_generate_multiple_formats(self, service, simple_bim):
        """Test generating multiple formats concurrently."""
        formats = [OutputFormat.GLTF, OutputFormat.DXF, OutputFormat.IFC]
        results = await service.generate_multiple(
            simple_bim,
            "test_multi",
            formats
        )
        
        assert len(results) == 3
        assert all(result.success for result in results.values())
        assert set(results.keys()) == {"gltf", "dxf", "ifc"}
        
    def test_move_to_permanent(self, service):
        """Test moving file from temp to permanent storage."""
        # Create a temp file
        temp_file = service.temp_dir / "temp_file.txt"
        temp_file.write_text("test content")
        
        # Move to permanent
        perm_path = service.move_to_permanent(temp_file, "permanent.txt")
        
        assert perm_path.exists()
        assert perm_path.name == "permanent.txt"
        assert perm_path.read_text() == "test content"
        assert not temp_file.exists()
        
    @pytest.mark.asyncio
    async def test_cleanup_temp_files(self, service):
        """Test temporary file cleanup."""
        # Create old temp file
        old_file = service.temp_dir / "old_file.txt"
        old_file.write_text("old")
        
        # Track it with old timestamp
        service._temp_files[str(old_file)] = datetime.now() - timedelta(hours=25)
        
        # Create recent file
        new_file = service.temp_dir / "new_file.txt"
        new_file.write_text("new")
        service._temp_files[str(new_file)] = datetime.now()
        
        # Run cleanup
        await service.cleanup_temp_files(max_age_hours=24)
        
        assert not old_file.exists()
        assert new_file.exists()
        assert str(old_file) not in service._temp_files
        assert str(new_file) in service._temp_files
        
    def test_get_available_formats(self, service):
        """Test listing available formats."""
        formats = service.get_available_formats()
        
        assert "gltf" in formats
        assert "dxf" in formats
        assert "ifc" in formats
        
    def test_validate_format(self, service):
        """Test format validation."""
        assert service.validate_format("gltf")
        assert service.validate_format("DXF")  # Case insensitive
        assert not service.validate_format("invalid")
        
    @pytest.mark.asyncio
    async def test_generate_preview(self, service, simple_bim):
        """Test preview generation."""
        result = await service.generate_preview(simple_bim, size="small")
        
        assert result.success
        assert result.format == OutputFormat.GLB
        
    def test_create_export_manifest(self, service):
        """Test manifest creation."""
        from src.output import OutputResult
        
        results = {
            "gltf": OutputResult(
                success=True,
                format=OutputFormat.GLTF,
                file_path=Path("test.gltf"),
                file_size=1000
            ),
            "dxf": OutputResult(
                success=False,
                format=OutputFormat.DXF,
                errors=["Test error"]
            )
        }
        
        manifest = service.create_export_manifest(results)
        
        assert "timestamp" in manifest
        assert len(manifest["files"]) == 1
        assert len(manifest["errors"]) == 1
        assert manifest["files"][0]["format"] == "gltf"
        assert manifest["errors"][0]["format"] == "dxf"


class TestOutputManager:
    """Test output manager functionality."""
    
    @pytest.fixture
    def manager(self):
        """Create output manager with service."""
        with tempfile.TemporaryDirectory() as output_dir:
            with tempfile.TemporaryDirectory() as temp_dir:
                service = OutputService(Path(output_dir), Path(temp_dir))
                yield OutputManager(service)
                
    @pytest.fixture
    def simple_bim(self):
        """Create a simple BIM for testing."""
        main = ShedBimPartMain(
            roof_type="Flat",
            roof_left=ShedBimRoof(),
            side_left=ShedBimSide(),
            side_right=ShedBimSide()
        )
        return ShedBim(main=main)
        
    @pytest.mark.asyncio
    async def test_process_order(self, manager, simple_bim):
        """Test order processing."""
        result = await manager.process_order(
            simple_bim,
            "ORDER123",
            ["gltf", "dxf", "invalid_format"]
        )
        
        assert result["order_id"] == "ORDER123"
        assert result["status"] == "partial"  # Due to invalid format
        assert "gltf" in result["files"]
        assert "dxf" in result["files"]
        assert "invalid_format" in result["invalid_formats"]
        
    @pytest.mark.asyncio
    async def test_create_download_package(self, manager, simple_bim):
        """Test ZIP package creation."""
        zip_path = await manager.create_download_package(
            simple_bim,
            "test_package",
            ["gltf", "dxf"]
        )
        
        assert zip_path.exists()
        assert zip_path.suffix == ".zip"
        
        # Verify ZIP contents
        with zipfile.ZipFile(zip_path, 'r') as zf:
            names = zf.namelist()
            assert any("gltf" in name for name in names)
            assert any("dxf" in name for name in names)
            assert "manifest.json" in names
            assert "README.txt" in names
            
            # Check manifest
            manifest_data = zf.read("manifest.json")
            manifest = json.loads(manifest_data)
            assert "files" in manifest
            assert "timestamp" in manifest