# Comprehensive Output Test Design for IFC and GLB

## Overview
This document outlines comprehensive test coverage for Task 6 output library, focusing on IFC and GLB file formats. Tests are designed to ensure feature parity with the C# implementation and validate both functional correctness and output accuracy.

## Test Categories

### 1. Functional Tests
- **Purpose**: Verify that all features work correctly
- **Approach**: Black-box testing of API functionality
- **Coverage**: All public methods and options

### 2. Accuracy Tests
- **Purpose**: Ensure output matches expected geometry and metadata
- **Approach**: Compare output against reference data
- **Coverage**: Geometry precision, material properties, metadata

### 3. Integration Tests
- **Purpose**: Validate end-to-end workflows
- **Approach**: Full pipeline testing from input to output
- **Coverage**: Real-world scenarios

### 4. Performance Tests
- **Purpose**: Ensure acceptable performance
- **Approach**: Benchmark timing and memory usage
- **Coverage**: Large models, concurrent generation

## IFC Test Suite Design

### Functional Tests

#### 1. Basic IFC Generation
```python
test_ifc_file_structure()
- Verify header with timestamp, author, organization
- Check schema version (IFC2X3, IFC4, IFC4X3)
- Validate basic file sections (HEADER, DATA, END)

test_project_hierarchy()
- IfcProject with units and context
- IfcSite with location/coordinates
- IfcBuilding with address
- IfcBuildingStorey with elevation

test_spatial_containment()
- Proper aggregation relationships
- Spatial structure relationships
- Element containment in storeys
```

#### 2. Structural Elements
```python
test_columns_generation()
- All column types (C-section, SHS, custom)
- Correct positioning and orientation
- Material associations
- Property sets (dimensions, weight)

test_beams_and_rafters()
- Beam profiles and dimensions
- Rafter angles and connections
- Haunch details
- Punching information

test_purlins_and_girts()
- Spacing and alignment
- Connection to main structure
- Profile types (Z, C sections)

test_brackets_and_connections()
- Bracket types (apex, eave, base)
- Mesh geometry from 3D models
- Proper positioning
- Material properties
```

#### 3. Building Envelope
```python
test_wall_cladding()
- Profile generation (ribs, pans)
- Hole cutting for openings
- Material layers
- Color specifications

test_roof_cladding()
- Slope calculations
- Ridge and valley details
- Penetrations for skylights
- Flashing integration

test_flashings()
- All flashing types
- Proper overlap and positioning
- Material specifications
- Connection details
```

#### 4. Openings
```python
test_doors()
- PA doors with frames
- Roller doors with guides
- Industrial sliding doors
- Glass sliding doors
- Opening schedules

test_windows()
- Sliding windows with overlap
- Fixed windows
- Window frames and glass
- Sill and head details

test_skylights()
- Roof penetrations
- Transparent materials
- Flashing integration
- Structural modifications
```

#### 5. Foundations and Slabs
```python
test_footings()
- Pad footings with dimensions
- Strip footings
- Pile caps
- Reinforcement details

test_floor_slabs()
- Front, middle, back pieces
- Thickness and levels
- Edge details
- Surface finishes
```

#### 6. Metadata and Properties
```python
test_material_properties()
- Steel grades and finishes
- Color specifications
- Weight and density
- Cost information

test_bom_integration()
- Part numbers and descriptions
- Quantities and units
- Pricing information
- Supplier details

test_property_sets()
- Pset_BeamCommon
- Pset_ColumnCommon
- Custom property sets
- Quantity takeoffs
```

### Accuracy Tests

#### 1. Geometry Validation
```python
test_coordinate_accuracy()
- Vertex positions within tolerance
- Transformation matrices
- Local placement accuracy
- Global positioning

test_dimension_accuracy()
- Profile dimensions
- Opening sizes
- Overall building dimensions
- Connection offsets

test_angle_accuracy()
- Roof slopes
- Frame angles
- Connection angles
- Rotation accuracy
```

#### 2. Material Accuracy
```python
test_material_mapping()
- Color RGB values
- Surface styles
- Transparency values
- Texture references

test_material_quantities()
- Surface areas
- Volumes
- Weights
- Counts
```

#### 3. Reference Comparison
```python
test_against_reference_files()
- Load C# generated IFC
- Compare entity counts
- Validate relationships
- Check property values

test_industry_validator()
- Solibri Model Checker
- IFC validation service
- Schema compliance
- Best practice checks
```

## GLB Test Suite Design

### Functional Tests

#### 1. Basic GLB Generation
```python
test_glb_structure()
- Binary header validation
- JSON chunk parsing
- Binary chunk alignment
- Asset metadata

test_scene_hierarchy()
- Node tree structure
- Transform matrices
- Parent-child relationships
- Instance references

test_coordinate_system()
- Y-up conversion
- Left-handed to right-handed
- Scale factors
- Origin adjustments
```

#### 2. Mesh Generation
```python
test_structural_meshes()
- Profile to mesh conversion
- CSG operations for holes
- Normal generation
- Vertex optimization

test_cladding_meshes()
- Triangulation accuracy
- Profile rib generation
- Hole cutting
- UV mapping

test_bracket_meshes()
- STL import and conversion
- Mesh simplification
- Orientation fixes
- Scale adjustments
```

#### 3. Materials
```python
test_pbr_materials()
- Base color accuracy
- Metallic/roughness values
- Transparency handling
- Double-sided materials

test_material_reuse()
- Caching by properties
- Instance optimization
- Texture references
- Material variants
```

#### 4. Special Features
```python
test_animations()
- Door opening sequences
- Window sliding animations
- Keyframe generation
- Timing controls

test_unity_mode()
- Special markers
- Coordinate adjustments
- Material naming
- Metadata format

test_customer_mode()
- Simplified geometry
- Reduced materials
- File size optimization
- Web-friendly output
```

### Accuracy Tests

#### 1. Visual Validation
```python
test_rendering_accuracy()
- Compare with reference images
- Color accuracy
- Lighting response
- Transparency effects

test_geometry_precision()
- Vertex position accuracy
- Normal vector accuracy
- UV coordinate accuracy
- Tangent space
```

#### 2. Performance Metrics
```python
test_file_size_optimization()
- Mesh deduplication
- Material instancing
- Compression efficiency
- Load time benchmarks

test_rendering_performance()
- Triangle counts
- Draw call optimization
- Texture memory usage
- Frame rate targets
```

## Integration Test Scenarios

### 1. Complete Building Export
```python
test_carport_full_export()
- Create complete carport model
- Export to IFC and GLB
- Validate all components present
- Check relationships and hierarchy

test_industrial_shed_export()
- Complex multi-bay structure
- Mezzanine floors
- Multiple door types
- Full BOM integration
```

### 2. Round-trip Testing
```python
test_ifc_reimport()
- Export to IFC
- Import in external tool
- Verify data preservation
- Check geometry accuracy

test_glb_viewer_compatibility()
- Test in three.js viewer
- Validate in Babylon.js
- Check Unity import
- Verify in native viewers
```

## Test Data Management

### 1. Reference Models
- Simple box structure
- Single bay carport
- Multi-bay industrial shed
- Complex geometry samples

### 2. Expected Outputs
- C# generated reference files
- Validated IFC samples
- Rendered GLB images
- Measurement data

### 3. Test Fixtures
- Reusable model components
- Material libraries
- Profile definitions
- Standard dimensions

## Test Implementation Strategy

### Phase 1: Core Functionality
1. Basic file generation
2. Simple geometry export
3. Material assignment
4. Hierarchy creation

### Phase 2: Advanced Features
1. Complex geometry
2. All element types
3. Metadata integration
4. Performance optimization

### Phase 3: Accuracy Validation
1. Reference comparisons
2. External validation
3. Visual verification
4. Measurement checks

## Success Criteria

### Functional Success
- All tests pass consistently
- No runtime errors
- Complete feature coverage
- API compatibility

### Accuracy Success
- Geometry within 0.1mm tolerance
- Colors within 1% accuracy
- All metadata preserved
- Valid file formats

### Performance Success
- Generation under 5 seconds for typical models
- File sizes comparable to C# output
- Memory usage under 1GB
- Concurrent generation support

## Testing Tools

### IFC Testing
- IfcOpenShell for validation
- Solibri for compliance
- BIMcollab for viewing
- Custom validation scripts

### GLB Testing
- glTF Validator
- Three.js for rendering
- Babylon.js sandbox
- Binary inspection tools

### Automation
- pytest for test execution
- pytest-asyncio for async tests
- pytest-benchmark for performance
- Coverage.py for code coverage