"""
Test suite for line geometry classes (Line1, Line2, Line3).

Tests all line operations and intersections.
"""

import pytest
import math
from src.geometry.lines import Line1, Line2, Line3
from src.geometry.primitives import Vec2, Vec3


class TestLine1:
    """Test Line1 (1D line segment) functionality."""
    
    def test_creation(self):
        """Test Line1 creation."""
        line = Line1(0.0, 10.0)
        assert line.start == 0.0
        assert line.end == 10.0
    
    def test_length(self):
        """Test line length calculation."""
        line1 = Line1(0.0, 10.0)
        assert line1.length() == 10.0
        
        line2 = Line1(5.0, 2.0)
        assert line2.length() == 3.0  # abs(2 - 5)
        
        line3 = Line1(-5.0, -10.0)
        assert line3.length() == 5.0
    
    def test_contains(self):
        """Test point containment."""
        line = Line1(0.0, 10.0)
        
        assert line.contains(5.0) == True
        assert line.contains(0.0) == True
        assert line.contains(10.0) == True
        assert line.contains(-1.0) == False
        assert line.contains(11.0) == False
    
    def test_from_center(self):
        """Test creation from center point."""
        line = Line1.from_center(5.0, 4.0)
        assert line.start == 3.0  # 5 - 4/2
        assert line.end == 7.0    # 5 + 4/2
        assert line.length() == 4.0
    
    def test_reversed_line(self):
        """Test line with start > end."""
        line = Line1(10.0, 0.0)
        assert line.length() == 10.0
        assert line.contains(5.0) == True
    
    def test_point_line(self):
        """Test degenerate line (point)."""
        line = Line1(5.0, 5.0)
        assert line.length() == 0.0
        assert line.contains(5.0) == True
        assert line.contains(4.9) == False
    
    def test_string_representation(self):
        """Test string representation."""
        line = Line1(1.5, 2.5)
        assert str(line) == "(1.5,2.5)"


class TestLine2:
    """Test Line2 (2D line segment) functionality."""
    
    def test_creation(self):
        """Test Line2 creation."""
        line = Line2(Vec2(0, 0), Vec2(3, 4))
        assert line.start == Vec2(0, 0)
        assert line.end == Vec2(3, 4)
    
    def test_vector_properties(self):
        """Test vector and direction properties."""
        line = Line2(Vec2(1, 1), Vec2(4, 5))
        
        vec = line.vector()
        assert vec.x == 3.0  # 4 - 1
        assert vec.y == 4.0  # 5 - 1
        
        direction = line.direction()
        assert abs(direction.length() - 1.0) < 1e-10  # Should be normalized
        
        assert line.magnitude() == 5.0  # 3-4-5 triangle
    
    def test_from_center(self):
        """Test creation from center point."""
        center = Vec2(5, 5)
        direction = Vec2(1, 0)  # Unit vector in X
        length = 6.0
        
        line = Line2.from_center(center, direction, length)
        
        assert line.start == Vec2(2, 5)  # center - direction * length/2
        assert line.end == Vec2(8, 5)    # center + direction * length/2
        assert abs(line.magnitude() - 6.0) < 1e-10
    
    def test_intersection_parallel(self):
        """Test parallel lines (no intersection)."""
        line1 = Line2(Vec2(0, 0), Vec2(10, 0))
        line2 = Line2(Vec2(0, 1), Vec2(10, 1))
        
        assert line1.intersect(line2) is None
        assert line1.intersect_inf(line2) is None
    
    def test_intersection_perpendicular(self):
        """Test perpendicular line intersection."""
        line1 = Line2(Vec2(0, 5), Vec2(10, 5))  # Horizontal
        line2 = Line2(Vec2(5, 0), Vec2(5, 10))  # Vertical
        
        intersection = line1.intersect(line2)
        assert intersection is not None
        assert intersection.x == pytest.approx(5.0)
        assert intersection.y == pytest.approx(5.0)
    
    def test_intersection_segments(self):
        """Test segment intersection vs infinite line intersection."""
        line1 = Line2(Vec2(0, 0), Vec2(5, 5))
        line2 = Line2(Vec2(0, 5), Vec2(5, 0))
        
        # Should intersect at (2.5, 2.5)
        intersection = line1.intersect(line2)
        assert intersection is not None
        assert intersection.x == pytest.approx(2.5)
        assert intersection.y == pytest.approx(2.5)
        
        # Test non-intersecting segments that would intersect if infinite
        line3 = Line2(Vec2(0, 0), Vec2(2, 2))
        line4 = Line2(Vec2(3, 0), Vec2(5, 2))
        
        assert line3.intersect(line4) is None  # Segments don't intersect
        
        # But infinite lines would intersect
        inf_intersection = line3.intersect_inf(line4)
        assert inf_intersection is not None
    
    def test_operators(self):
        """Test arithmetic operators."""
        line = Line2(Vec2(1, 1), Vec2(3, 3))
        offset = Vec2(2, 3)
        
        # Addition
        moved = line + offset
        assert moved.start == Vec2(3, 4)
        assert moved.end == Vec2(5, 6)
        
        # Subtraction
        moved_back = moved - offset
        assert moved_back.start == line.start
        assert moved_back.end == line.end
        
        # Scaling
        scaled = line * 2.0
        # Scaling should preserve start but double the vector
        assert scaled.start == line.start
        expected_end = Vec2(1 + 2*(3-1), 1 + 2*(3-1))
        assert scaled.end == expected_end
    
    def test_collinear_lines(self):
        """Test collinear line intersection."""
        line1 = Line2(Vec2(0, 0), Vec2(5, 0))
        line2 = Line2(Vec2(3, 0), Vec2(8, 0))
        
        # Collinear overlapping lines
        intersection = line1.intersect(line2)
        # Should return None for collinear lines (infinite intersections)
        assert intersection is None
    
    def test_string_representation(self):
        """Test string representation."""
        line = Line2(Vec2(1, 2), Vec2(3, 4))
        s = str(line)
        assert "(1,2)" in s
        assert "(3,4)" in s


class TestLine3:
    """Test Line3 (3D line segment) functionality."""
    
    def test_creation(self):
        """Test Line3 creation."""
        line = Line3(Vec3(0, 0, 0), Vec3(1, 1, 1))
        assert line.start == Vec3(0, 0, 0)
        assert line.end == Vec3(1, 1, 1)
    
    def test_vector_properties(self):
        """Test vector and direction properties."""
        line = Line3(Vec3(1, 2, 3), Vec3(4, 6, 8))
        
        vec = line.vector()
        assert vec == Vec3(3, 4, 5)
        
        direction = line.direction()
        assert abs(direction.length() - 1.0) < 1e-10
        
        magnitude = line.magnitude()
        assert magnitude == pytest.approx(math.sqrt(3*3 + 4*4 + 5*5))
    
    def test_from_center(self):
        """Test creation from center point."""
        center = Vec3(5, 5, 5)
        direction = Vec3(1, 0, 0)  # Unit vector in X
        length = 10.0
        
        line = Line3.from_center(center, direction, length)
        
        assert line.start == Vec3(0, 5, 5)
        assert line.end == Vec3(10, 5, 5)
        assert abs(line.magnitude() - 10.0) < 1e-10
    
    def test_distance_to_point(self):
        """Test distance from line to point."""
        # Line along X axis
        line = Line3(Vec3(0, 0, 0), Vec3(10, 0, 0))
        
        # Point directly above middle of line
        point1 = Vec3(5, 3, 0)
        dist1 = line.distance_to_point(point1)
        assert dist1 == pytest.approx(3.0)
        
        # Point at start
        point2 = Vec3(0, 0, 0)
        dist2 = line.distance_to_point(point2)
        assert dist2 == pytest.approx(0.0)
        
        # Point beyond end of segment
        point3 = Vec3(15, 0, 0)
        dist3 = line.distance_to_point(point3)
        assert dist3 == pytest.approx(5.0)
    
    def test_closest_point_on_line(self):
        """Test finding closest point on line to external point."""
        line = Line3(Vec3(0, 0, 0), Vec3(10, 0, 0))
        
        # Point above middle
        point = Vec3(5, 5, 0)
        closest = line.closest_point(point)
        assert closest == Vec3(5, 0, 0)
        
        # Point before start
        point2 = Vec3(-5, 0, 0)
        closest2 = line.closest_point(point2)
        assert closest2 == Vec3(0, 0, 0)  # Clamped to start
        
        # Point after end
        point3 = Vec3(15, 0, 0)
        closest3 = line.closest_point(point3)
        assert closest3 == Vec3(10, 0, 0)  # Clamped to end
    
    def test_operators(self):
        """Test arithmetic operators."""
        line = Line3(Vec3(1, 1, 1), Vec3(2, 2, 2))
        offset = Vec3(3, 4, 5)
        
        # Addition
        moved = line + offset
        assert moved.start == Vec3(4, 5, 6)
        assert moved.end == Vec3(5, 6, 7)
        
        # Subtraction
        moved_back = moved - offset
        assert moved_back.start == line.start
        assert moved_back.end == line.end
        
        # Scaling
        scaled = line * 3.0
        assert scaled.start == line.start
        # End should be extended by factor of 3
        expected_end = Vec3(1 + 3*(2-1), 1 + 3*(2-1), 1 + 3*(2-1))
        assert scaled.end == expected_end
    
    def test_line_line_distance(self):
        """Test distance between two 3D lines."""
        # Parallel lines
        line1 = Line3(Vec3(0, 0, 0), Vec3(10, 0, 0))
        line2 = Line3(Vec3(0, 1, 0), Vec3(10, 1, 0))
        
        dist = line1.distance_to_line(line2)
        assert dist == pytest.approx(1.0)
        
        # Intersecting lines
        line3 = Line3(Vec3(0, 0, 0), Vec3(10, 10, 0))
        line4 = Line3(Vec3(0, 10, 0), Vec3(10, 0, 0))
        
        dist2 = line3.distance_to_line(line4)
        assert dist2 == pytest.approx(0.0)
    
    def test_skew_lines(self):
        """Test skew lines in 3D."""
        # Lines that don't intersect and aren't parallel
        line1 = Line3(Vec3(0, 0, 0), Vec3(1, 0, 0))  # Along X at Z=0
        line2 = Line3(Vec3(0, 0, 1), Vec3(0, 1, 1))  # Along Y at Z=1
        
        dist = line1.distance_to_line(line2)
        assert dist == pytest.approx(1.0)  # Minimum distance is in Z direction
    
    def test_degenerate_line(self):
        """Test degenerate line (point)."""
        line = Line3(Vec3(5, 5, 5), Vec3(5, 5, 5))
        
        assert line.magnitude() == 0.0
        
        # Distance to point should work
        point = Vec3(8, 9, 12)
        dist = line.distance_to_point(point)
        assert dist == pytest.approx(Vec3.distance(Vec3(5, 5, 5), point))
    
    def test_string_representation(self):
        """Test string representation."""
        line = Line3(Vec3(1, 2, 3), Vec3(4, 5, 6))
        s = str(line)
        assert "(1,2,3)" in s
        assert "(4,5,6)" in s


if __name__ == "__main__":
    pytest.main([__file__, "-v"])