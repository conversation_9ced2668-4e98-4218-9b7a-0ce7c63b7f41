"""
Test suite for box geometry classes (Box2, Box3, Rect).

Tests bounding box operations and collision detection.
"""

import pytest
from src.geometry.boxes import Box2, Box3, Rect
from src.geometry.primitives import Vec2, Vec3


class TestBox2:
    """Test Box2 (2D bounding box) functionality."""
    
    def test_creation(self):
        """Test Box2 creation and automatic ordering."""
        # Normal creation
        box = Box2(Vec2(0, 0), Vec2(10, 10))
        assert box.min == Vec2(0, 0)
        assert box.max == Vec2(10, 10)
        
        # Reversed corners should auto-correct
        box2 = Box2(Vec2(10, 10), Vec2(0, 0))
        assert box2.min == Vec2(0, 0)
        assert box2.max == Vec2(10, 10)
    
    def test_size_and_center(self):
        """Test size and center calculations."""
        box = Box2(Vec2(2, 3), Vec2(8, 11))
        
        size = box.size()
        assert size == Vec2(6, 8)  # 8-2, 11-3
        
        center = box.center()
        assert center == Vec2(5, 7)  # (2+8)/2, (3+11)/2
        
        # Test middle (alias for center)
        assert box.middle() == center
    
    def test_area(self):
        """Test area calculation."""
        box = Box2(Vec2(0, 0), Vec2(5, 4))
        assert box.area() == 20.0  # 5 * 4
        
        # Degenerate box (line)
        box2 = Box2(Vec2(0, 0), Vec2(10, 0))
        assert box2.area() == 0.0
    
    def test_corner_accessors(self):
        """Test corner accessor methods."""
        box = Box2(Vec2(1, 2), Vec2(5, 7))
        
        assert box.bottom_left() == Vec2(1, 2)
        assert box.bottom_right() == Vec2(5, 2)
        assert box.top_left() == Vec2(1, 7)
        assert box.top_right() == Vec2(5, 7)
    
    def test_edge_accessors(self):
        """Test edge coordinate accessors."""
        box = Box2(Vec2(1, 2), Vec2(5, 7))
        
        assert box.left() == 1
        assert box.right() == 5
        assert box.bottom() == 2
        assert box.top() == 7
    
    def test_contains_point(self):
        """Test point containment."""
        box = Box2(Vec2(0, 0), Vec2(10, 10))
        
        # Inside
        assert box.contains(Vec2(5, 5)) == True
        
        # On edges (inclusive)
        assert box.contains(Vec2(0, 5)) == True
        assert box.contains(Vec2(10, 5)) == True
        assert box.contains(Vec2(5, 0)) == True
        assert box.contains(Vec2(5, 10)) == True
        
        # Corners
        assert box.contains(Vec2(0, 0)) == True
        assert box.contains(Vec2(10, 10)) == True
        
        # Outside
        assert box.contains(Vec2(-1, 5)) == False
        assert box.contains(Vec2(11, 5)) == False
        assert box.contains(Vec2(5, -1)) == False
        assert box.contains(Vec2(5, 11)) == False
    
    def test_intersects(self):
        """Test box intersection."""
        box1 = Box2(Vec2(0, 0), Vec2(5, 5))
        
        # Overlapping
        box2 = Box2(Vec2(3, 3), Vec2(8, 8))
        assert box1.intersects(box2) == True
        
        # Touching edges
        box3 = Box2(Vec2(5, 0), Vec2(10, 5))
        assert box1.intersects(box3) == True
        
        # Separate
        box4 = Box2(Vec2(6, 6), Vec2(10, 10))
        assert box1.intersects(box4) == False
        
        # Contained
        box5 = Box2(Vec2(1, 1), Vec2(4, 4))
        assert box1.intersects(box5) == True
        
        # Contains
        box6 = Box2(Vec2(-1, -1), Vec2(6, 6))
        assert box1.intersects(box6) == True
    
    def test_union(self):
        """Test box union operation."""
        box1 = Box2(Vec2(0, 0), Vec2(5, 5))
        box2 = Box2(Vec2(3, 3), Vec2(8, 8))
        
        # Instance method
        union1 = box1.union_with(box2)
        assert union1.min == Vec2(0, 0)
        assert union1.max == Vec2(8, 8)
        
        # Static method
        union2 = Box2.union(box1, box2)
        assert union2.min == Vec2(0, 0)
        assert union2.max == Vec2(8, 8)
        
        # Non-overlapping boxes
        box3 = Box2(Vec2(10, 10), Vec2(15, 15))
        union3 = box1.union_with(box3)
        assert union3.min == Vec2(0, 0)
        assert union3.max == Vec2(15, 15)
    
    def test_expanded(self):
        """Test box expansion."""
        box = Box2(Vec2(5, 5), Vec2(10, 10))
        
        # Positive expansion
        expanded = box.expanded(2)
        assert expanded.min == Vec2(3, 3)
        assert expanded.max == Vec2(12, 12)
        
        # Negative expansion (shrink)
        shrunk = box.expanded(-1)
        assert shrunk.min == Vec2(6, 6)
        assert shrunk.max == Vec2(9, 9)
    
    def test_from_position_and_size(self):
        """Test creation from position and size."""
        box = Box2.from_position_and_size(Vec2(2, 3), Vec2(5, 7))
        assert box.min == Vec2(2, 3)
        assert box.max == Vec2(7, 10)  # 2+5, 3+7
    
    def test_from_center(self):
        """Test creation from center and size."""
        box = Box2.from_center(Vec2(10, 10), Vec2(6, 4))
        assert box.min == Vec2(7, 8)   # 10-3, 10-2
        assert box.max == Vec2(13, 12)  # 10+3, 10+2
    
    def test_from_bottom_center(self):
        """Test creation from bottom-center and size."""
        box = Box2.from_bottom_center(Vec2(10, 5), Vec2(6, 4))
        assert box.min == Vec2(7, 5)   # 10-3, 5
        assert box.max == Vec2(13, 9)  # 10+3, 5+4
    
    def test_from_corners(self):
        """Test creation from corner coordinates."""
        box = Box2.from_corners(1, 2, 5, 7)
        assert box.min == Vec2(1, 2)
        assert box.max == Vec2(5, 7)
    
    def test_from_list(self):
        """Test creation from list of points."""
        points = [
            Vec2(5, 5),
            Vec2(1, 8),
            Vec2(10, 2),
            Vec2(3, 12)
        ]
        
        box = Box2.from_list(points)
        assert box.min == Vec2(1, 2)
        assert box.max == Vec2(10, 12)
        
        # Empty list should create invalid box
        empty_box = Box2.from_list([])
        assert empty_box.min.x > empty_box.max.x  # Invalid box
    
    def test_string_representation(self):
        """Test string representations."""
        box = Box2(Vec2(1, 2), Vec2(3, 4))
        assert str(box) == "[(1,2), (3,4)]"
        assert "min=" in repr(box)
        assert "max=" in repr(box)


class TestBox3:
    """Test Box3 (3D bounding box) functionality."""
    
    def test_creation(self):
        """Test Box3 creation and automatic ordering."""
        box = Box3(Vec3(0, 0, 0), Vec3(10, 10, 10))
        assert box.min == Vec3(0, 0, 0)
        assert box.max == Vec3(10, 10, 10)
        
        # Reversed corners
        box2 = Box3(Vec3(10, 10, 10), Vec3(0, 0, 0))
        assert box2.min == Vec3(0, 0, 0)
        assert box2.max == Vec3(10, 10, 10)
    
    def test_size_and_center(self):
        """Test size and center calculations."""
        box = Box3(Vec3(1, 2, 3), Vec3(5, 8, 11))
        
        size = box.size()
        assert size == Vec3(4, 6, 8)
        
        center = box.center()
        assert center == Vec3(3, 5, 7)
    
    def test_volume(self):
        """Test volume calculation."""
        box = Box3(Vec3(0, 0, 0), Vec3(2, 3, 4))
        assert box.volume() == 24.0  # 2 * 3 * 4
        
        # Degenerate box (plane)
        box2 = Box3(Vec3(0, 0, 0), Vec3(10, 10, 0))
        assert box2.volume() == 0.0
    
    def test_contains_point(self):
        """Test 3D point containment."""
        box = Box3(Vec3(0, 0, 0), Vec3(10, 10, 10))
        
        # Inside
        assert box.contains(Vec3(5, 5, 5)) == True
        
        # On faces
        assert box.contains(Vec3(0, 5, 5)) == True
        assert box.contains(Vec3(10, 5, 5)) == True
        
        # On edges
        assert box.contains(Vec3(0, 0, 5)) == True
        assert box.contains(Vec3(10, 10, 5)) == True
        
        # Corners
        assert box.contains(Vec3(0, 0, 0)) == True
        assert box.contains(Vec3(10, 10, 10)) == True
        
        # Outside
        assert box.contains(Vec3(-1, 5, 5)) == False
        assert box.contains(Vec3(5, 5, 11)) == False
    
    def test_intersects(self):
        """Test 3D box intersection."""
        box1 = Box3(Vec3(0, 0, 0), Vec3(5, 5, 5))
        
        # Overlapping
        box2 = Box3(Vec3(3, 3, 3), Vec3(8, 8, 8))
        assert box1.intersects(box2) == True
        
        # Touching faces
        box3 = Box3(Vec3(5, 0, 0), Vec3(10, 5, 5))
        assert box1.intersects(box3) == True
        
        # Separate
        box4 = Box3(Vec3(6, 6, 6), Vec3(10, 10, 10))
        assert box1.intersects(box4) == False
    
    def test_union(self):
        """Test 3D box union."""
        box1 = Box3(Vec3(0, 0, 0), Vec3(5, 5, 5))
        box2 = Box3(Vec3(3, 3, 3), Vec3(8, 8, 8))
        
        union = box1.union(box2)
        assert union.min == Vec3(0, 0, 0)
        assert union.max == Vec3(8, 8, 8)
    
    def test_expanded(self):
        """Test 3D box expansion."""
        box = Box3(Vec3(5, 5, 5), Vec3(10, 10, 10))
        
        expanded = box.expanded(2)
        assert expanded.min == Vec3(3, 3, 3)
        assert expanded.max == Vec3(12, 12, 12)
    
    def test_from_list(self):
        """Test creation from list of 3D points."""
        points = [
            Vec3(5, 5, 5),
            Vec3(1, 8, 3),
            Vec3(10, 2, 7),
            Vec3(3, 12, 1)
        ]
        
        box = Box3.from_list(points)
        assert box.min == Vec3(1, 2, 1)
        assert box.max == Vec3(10, 12, 7)
    
    def test_from_center(self):
        """Test creation from center and size."""
        box = Box3.from_center(Vec3(10, 10, 10), Vec3(6, 4, 8))
        assert box.min == Vec3(7, 8, 6)
        assert box.max == Vec3(13, 12, 14)


class TestRect:
    """Test Rect (rectangle with position and size) functionality."""
    
    def test_creation(self):
        """Test rectangle creation."""
        rect = Rect(10, 20, 30, 40)
        assert rect.x == 10
        assert rect.y == 20
        assert rect.width == 30
        assert rect.height == 40
    
    def test_edge_properties(self):
        """Test edge property accessors."""
        rect = Rect(10, 20, 30, 40)
        
        assert rect.left == 10
        assert rect.top == 20
        assert rect.right == 40    # 10 + 30
        assert rect.bottom == 60   # 20 + 40
    
    def test_center(self):
        """Test center calculation."""
        rect = Rect(10, 20, 30, 40)
        center = rect.center()
        assert center == Vec2(25, 40)  # (10+30/2, 20+40/2)
    
    def test_area(self):
        """Test area calculation."""
        rect = Rect(0, 0, 10, 5)
        assert rect.area() == 50  # 10 * 5
    
    def test_contains(self):
        """Test point containment."""
        rect = Rect(10, 20, 30, 40)
        
        # Inside
        assert rect.contains(Vec2(25, 40)) == True
        
        # On edges
        assert rect.contains(Vec2(10, 40)) == True
        assert rect.contains(Vec2(40, 40)) == True
        
        # Outside
        assert rect.contains(Vec2(5, 40)) == False
        assert rect.contains(Vec2(45, 40)) == False
    
    def test_intersects(self):
        """Test rectangle intersection."""
        rect1 = Rect(0, 0, 10, 10)
        
        # Overlapping
        rect2 = Rect(5, 5, 10, 10)
        assert rect1.intersects(rect2) == True
        
        # Touching
        rect3 = Rect(10, 0, 10, 10)
        assert rect1.intersects(rect3) == True
        
        # Separate
        rect4 = Rect(15, 15, 10, 10)
        assert rect1.intersects(rect4) == False
    
    def test_intersection(self):
        """Test rectangle intersection calculation."""
        rect1 = Rect(0, 0, 10, 10)
        rect2 = Rect(5, 5, 10, 10)
        
        intersection = rect1.intersection(rect2)
        assert intersection is not None
        assert intersection.x == 5
        assert intersection.y == 5
        assert intersection.width == 5
        assert intersection.height == 5
        
        # Non-intersecting
        rect3 = Rect(20, 20, 10, 10)
        assert rect1.intersection(rect3) is None
    
    def test_union(self):
        """Test rectangle union."""
        rect1 = Rect(0, 0, 10, 10)
        rect2 = Rect(5, 5, 10, 10)
        
        union = rect1.union(rect2)
        assert union.x == 0
        assert union.y == 0
        assert union.width == 15   # To cover both rects
        assert union.height == 15
    
    def test_expanded(self):
        """Test rectangle expansion."""
        rect = Rect(10, 10, 20, 20)
        
        expanded = rect.expanded(5)
        assert expanded.x == 5
        assert expanded.y == 5
        assert expanded.width == 30   # 20 + 2*5
        assert expanded.height == 30
    
    def test_conversions(self):
        """Test conversions between Rect and Box2."""
        rect = Rect(10, 20, 30, 40)
        
        # To Box2
        box = rect.to_box2()
        assert box.min == Vec2(10, 20)
        assert box.max == Vec2(40, 60)
        
        # From Box2
        rect2 = Rect.from_box2(box)
        assert rect2.x == 10
        assert rect2.y == 20
        assert rect2.width == 30
        assert rect2.height == 40
    
    def test_from_corners(self):
        """Test creation from corners."""
        rect = Rect.from_corners(Vec2(10, 20), Vec2(40, 60))
        assert rect.x == 10
        assert rect.y == 20
        assert rect.width == 30
        assert rect.height == 40
    
    def test_string_representation(self):
        """Test string representation."""
        rect = Rect(1, 2, 3, 4)
        assert str(rect) == "Rect(1, 2, 3, 4)"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])