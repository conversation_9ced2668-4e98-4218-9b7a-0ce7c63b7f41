"""Comprehensive tests for Mat4 class."""

import math
import numpy as np
import pytest
from src.geometry.matrix import Mat4
from src.geometry.primitives import Vec3
from src.geometry.basis import Basis3


class TestMat4Creation:
    """Test Mat4 creation and basic properties."""
    
    def test_create_mat4(self):
        """Test creating a Mat4 instance."""
        m = Mat4(
            1.0, 2.0, 3.0, 4.0,
            5.0, 6.0, 7.0, 8.0,
            9.0, 10.0, 11.0, 12.0,
            13.0, 14.0, 15.0, 16.0
        )
        assert m.m11 == 1.0
        assert m.m12 == 2.0
        assert m.m13 == 3.0
        assert m.m14 == 4.0
        assert m.m44 == 16.0
    
    def test_identity(self):
        """Test identity matrix creation."""
        m = Mat4.identity()
        assert m.m11 == 1.0
        assert m.m22 == 1.0
        assert m.m33 == 1.0
        assert m.m44 == 1.0
        assert m.m12 == 0.0
        assert m.m13 == 0.0
        assert m.m14 == 0.0
    
    def test_get_translation(self):
        """Test extracting translation from matrix."""
        m = Mat4.create_translation(5.0, 6.0, 7.0)
        trans = m.get_translation()
        assert trans.x == 5.0
        assert trans.y == 6.0
        assert trans.z == 7.0
    
    def test_get_basis(self):
        """Test extracting basis vectors from matrix."""
        m = Mat4.identity()
        basis = m.get_basis()
        assert basis.x == Vec3(1.0, 0.0, 0.0)
        assert basis.y == Vec3(0.0, 1.0, 0.0)
        assert basis.z == Vec3(0.0, 0.0, 1.0)


class TestMat4Transformations:
    """Test Mat4 transformation creation methods."""
    
    def test_create_translation(self):
        """Test translation matrix creation."""
        m = Mat4.create_translation(2.0, 3.0, 4.0)
        assert m.m14 == 2.0
        assert m.m24 == 3.0
        assert m.m34 == 4.0
        assert m.m11 == 1.0
        assert m.m22 == 1.0
        assert m.m33 == 1.0
        assert m.m44 == 1.0
        
        # Test with Vec3
        v = Vec3(5.0, 6.0, 7.0)
        m2 = Mat4.create_translation_vec(v)
        assert m2.m14 == 5.0
        assert m2.m24 == 6.0
        assert m2.m34 == 7.0
    
    def test_create_scale(self):
        """Test scale matrix creation."""
        # Uniform scale
        m = Mat4.create_scale(2.0)
        assert m.m11 == 2.0
        assert m.m22 == 2.0
        assert m.m33 == 2.0
        assert m.m44 == 1.0
        
        # Non-uniform scale
        m2 = Mat4.create_scale_xyz(2.0, 3.0, 4.0)
        assert m2.m11 == 2.0
        assert m2.m22 == 3.0
        assert m2.m33 == 4.0
        
        # Scale from vector
        v = Vec3(5.0, 6.0, 7.0)
        m3 = Mat4.create_scale_vec(v)
        assert m3.m11 == 5.0
        assert m3.m22 == 6.0
        assert m3.m33 == 7.0
    
    def test_create_rotation_x(self):
        """Test rotation around X axis."""
        angle = math.pi / 4  # 45 degrees
        m = Mat4.create_rotation_x(angle)
        
        assert math.isclose(m.m11, 1.0)
        assert math.isclose(m.m22, math.cos(angle))
        assert math.isclose(m.m23, -math.sin(angle))
        assert math.isclose(m.m32, math.sin(angle))
        assert math.isclose(m.m33, math.cos(angle))
        
        # Test rotation of Y unit vector
        y = Vec3(0.0, 1.0, 0.0)
        rotated = m.transform_vector(y)
        assert math.isclose(rotated.x, 0.0)
        assert math.isclose(rotated.y, math.cos(angle))
        assert math.isclose(rotated.z, math.sin(angle))
    
    def test_create_rotation_y(self):
        """Test rotation around Y axis."""
        angle = math.pi / 4  # 45 degrees
        m = Mat4.create_rotation_y(angle)
        
        assert math.isclose(m.m11, math.cos(angle))
        assert math.isclose(m.m13, math.sin(angle))
        assert math.isclose(m.m22, 1.0)
        assert math.isclose(m.m31, -math.sin(angle))
        assert math.isclose(m.m33, math.cos(angle))
        
        # Test rotation of X unit vector
        x = Vec3(1.0, 0.0, 0.0)
        rotated = m.transform_vector(x)
        assert math.isclose(rotated.x, math.cos(angle))
        assert math.isclose(rotated.y, 0.0)
        assert math.isclose(rotated.z, -math.sin(angle))
    
    def test_create_rotation_z(self):
        """Test rotation around Z axis."""
        angle = math.pi / 4  # 45 degrees
        m = Mat4.create_rotation_z(angle)
        
        assert math.isclose(m.m11, math.cos(angle))
        assert math.isclose(m.m12, -math.sin(angle))
        assert math.isclose(m.m21, math.sin(angle))
        assert math.isclose(m.m22, math.cos(angle))
        assert math.isclose(m.m33, 1.0)
        
        # Test rotation of X unit vector
        x = Vec3(1.0, 0.0, 0.0)
        rotated = m.transform_vector(x)
        assert math.isclose(rotated.x, math.cos(angle))
        assert math.isclose(rotated.y, math.sin(angle))
        assert math.isclose(rotated.z, 0.0)
    
    def test_create_basis(self):
        """Test creating matrix from basis vectors."""
        x = Vec3(1.0, 0.0, 0.0)
        y = Vec3(0.0, 1.0, 0.0)
        z = Vec3(0.0, 0.0, 1.0)
        
        m = Mat4.create_basis(x, y, z)
        assert m.m11 == 1.0
        assert m.m22 == 1.0
        assert m.m33 == 1.0
        assert m.m44 == 1.0
        
        # Test with Basis3
        basis = Basis3(x, y, z)
        m2 = Mat4.create_basis_from_basis3(basis)
        assert m2.m11 == 1.0
        assert m2.m22 == 1.0
        assert m2.m33 == 1.0
    
    def test_create_transform(self):
        """Test creating transformation matrix from basis and position."""
        basis = Basis3.unit_xyz()
        pos = Vec3(5.0, 6.0, 7.0)
        
        m = Mat4.create_transform(basis, pos)
        assert m.m14 == 5.0
        assert m.m24 == 6.0
        assert m.m34 == 7.0
        assert m.m11 == 1.0
        assert m.m22 == 1.0
        assert m.m33 == 1.0


class TestMat4Operations:
    """Test Mat4 mathematical operations."""
    
    def test_matrix_multiplication(self):
        """Test matrix multiplication."""
        # Test identity multiplication
        m1 = Mat4.identity()
        m2 = Mat4.create_translation(1.0, 2.0, 3.0)
        result = m1 * m2
        assert result.m14 == 1.0
        assert result.m24 == 2.0
        assert result.m34 == 3.0
        
        # Test translation then scale
        trans = Mat4.create_translation(2.0, 3.0, 4.0)
        scale = Mat4.create_scale(2.0)
        result = scale * trans
        
        # Apply to a point
        p = Vec3(1.0, 1.0, 1.0)
        transformed = result.transform_position(p)
        # First translate: (1+2, 1+3, 1+4) = (3, 4, 5)
        # Then scale: (3*2, 4*2, 5*2) = (6, 8, 10)
        assert transformed.x == 6.0
        assert transformed.y == 8.0
        assert transformed.z == 10.0
    
    def test_transform_position(self):
        """Test transforming a position."""
        m = Mat4.create_translation(5.0, 6.0, 7.0)
        p = Vec3(1.0, 2.0, 3.0)
        result = m.transform_position(p)
        assert result.x == 6.0
        assert result.y == 8.0
        assert result.z == 10.0
    
    def test_transform_vector(self):
        """Test transforming a vector (ignores translation)."""
        m = Mat4.create_translation(5.0, 6.0, 7.0)
        v = Vec3(1.0, 2.0, 3.0)
        result = m.transform_vector(v)
        # Translation should not affect vectors
        assert result.x == 1.0
        assert result.y == 2.0
        assert result.z == 3.0
        
        # Test with rotation
        m2 = Mat4.create_rotation_z(math.pi / 2)  # 90 degrees
        v2 = Vec3(1.0, 0.0, 0.0)
        result2 = m2.transform_vector(v2)
        assert math.isclose(result2.x, 0.0, abs_tol=1e-10)
        assert math.isclose(result2.y, 1.0, abs_tol=1e-10)
        assert math.isclose(result2.z, 0.0, abs_tol=1e-10)
    
    def test_transform_normal(self):
        """Test transforming a normal."""
        # For orthogonal matrices, transform_normal should work like transform_vector
        m = Mat4.create_rotation_z(math.pi / 2)
        n = Vec3(1.0, 0.0, 0.0)
        result = m.transform_normal(n)
        assert math.isclose(result.x, 0.0, abs_tol=1e-10)
        assert math.isclose(result.y, 1.0, abs_tol=1e-10)
        assert math.isclose(result.z, 0.0, abs_tol=1e-10)
    
    def test_inverse(self):
        """Test matrix inversion."""
        # Test inverse of translation
        m = Mat4.create_translation(2.0, 3.0, 4.0)
        inv = m.get_inverse()
        
        # M * M^-1 should be identity
        identity = m * inv
        assert math.isclose(identity.m11, 1.0, abs_tol=1e-10)
        assert math.isclose(identity.m22, 1.0, abs_tol=1e-10)
        assert math.isclose(identity.m33, 1.0, abs_tol=1e-10)
        assert math.isclose(identity.m44, 1.0, abs_tol=1e-10)
        assert math.isclose(identity.m12, 0.0, abs_tol=1e-10)
        assert math.isclose(identity.m14, 0.0, abs_tol=1e-10)
        
        # Test a point transformed then inverse transformed
        p = Vec3(5.0, 6.0, 7.0)
        transformed = m.transform_position(p)
        back = inv.transform_position(transformed)
        assert math.isclose(back.x, p.x, abs_tol=1e-10)
        assert math.isclose(back.y, p.y, abs_tol=1e-10)
        assert math.isclose(back.z, p.z, abs_tol=1e-10)


class TestMat4NumPy:
    """Test Mat4 NumPy integration."""
    
    def test_to_numpy(self):
        """Test conversion to NumPy array."""
        m = Mat4(
            1.0, 2.0, 3.0, 4.0,
            5.0, 6.0, 7.0, 8.0,
            9.0, 10.0, 11.0, 12.0,
            13.0, 14.0, 15.0, 16.0
        )
        arr = m.to_numpy()
        assert arr[0, 0] == 1.0
        assert arr[0, 3] == 4.0
        assert arr[3, 3] == 16.0
    
    def test_from_numpy(self):
        """Test creation from NumPy array."""
        arr = np.array([
            [1.0, 2.0, 3.0, 4.0],
            [5.0, 6.0, 7.0, 8.0],
            [9.0, 10.0, 11.0, 12.0],
            [13.0, 14.0, 15.0, 16.0]
        ])
        m = Mat4.from_numpy(arr)
        assert m.m11 == 1.0
        assert m.m14 == 4.0
        assert m.m44 == 16.0
    
    def test_numpy_roundtrip(self):
        """Test conversion to and from NumPy."""
        m1 = Mat4.create_transform(
            Basis3.from_xy(
                Vec3(0.0, 1.0, 0.0),
                Vec3(-1.0, 0.0, 0.0)
            ),
            Vec3(5.0, 6.0, 7.0)
        )
        arr = m1.to_numpy()
        m2 = Mat4.from_numpy(arr)
        
        # All components should match
        assert m1.m11 == m2.m11
        assert m1.m14 == m2.m14
        assert m1.m44 == m2.m44


class TestMat4StringRepresentation:
    """Test Mat4 string representations."""
    
    def test_str(self):
        """Test string representation."""
        m = Mat4.identity()
        s = str(m)
        assert "M11:1.0" in s
        assert "M22:1.0" in s
        assert "M33:1.0" in s
        assert "M44:1.0" in s
    
    def test_repr(self):
        """Test detailed representation."""
        m = Mat4.identity()
        r = repr(m)
        assert "Mat4(" in r
        assert "1.0" in r
        assert "0.0" in r


class TestMat4EdgeCases:
    """Test edge cases and complex scenarios."""
    
    def test_combined_transformations(self):
        """Test combining multiple transformations."""
        # Create TRS (Translation * Rotation * Scale) matrix
        scale = Mat4.create_scale(2.0)
        rotation = Mat4.create_rotation_y(math.pi / 4)
        translation = Mat4.create_translation(10.0, 0.0, 0.0)
        
        # Combined transformation
        trs = translation * rotation * scale
        
        # Test on a point
        p = Vec3(1.0, 0.0, 0.0)
        # First scale: (2, 0, 0)
        # Then rotate 45° around Y: (√2, 0, -√2)
        # Then translate: (10+√2, 0, -√2)
        result = trs.transform_position(p)
        assert math.isclose(result.x, 10.0 + math.sqrt(2), abs_tol=1e-10)
        assert math.isclose(result.y, 0.0, abs_tol=1e-10)
        assert math.isclose(result.z, -math.sqrt(2), abs_tol=1e-10)
    
    def test_orthogonal_basis(self):
        """Test creating matrix from orthogonal basis."""
        # Create an orthogonal basis
        x = Vec3(1.0, 0.0, 0.0)
        y = Vec3(0.0, 0.707107, 0.707107)  # 45° rotated Y
        z = Vec3.cross(x, y)
        z = Vec3.normal(z)  # Normalize
        
        m = Mat4.create_basis(x, y, z)
        
        # Transform standard basis vectors
        tx = m.transform_vector(Vec3(1.0, 0.0, 0.0))
        ty = m.transform_vector(Vec3(0.0, 1.0, 0.0))
        tz = m.transform_vector(Vec3(0.0, 0.0, 1.0))
        
        # Should match our basis
        assert math.isclose(tx.x, x.x, abs_tol=1e-6)
        assert math.isclose(ty.y, y.y, abs_tol=1e-6)
        assert math.isclose(tz.z, z.z, abs_tol=1e-6)