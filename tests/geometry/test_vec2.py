"""Comprehensive tests for Vec2 class."""

import math
import pytest
from src.geometry.primitives import Vec2


class TestVec2Creation:
    """Test Vec2 creation and basic properties."""
    
    def test_create_vec2(self):
        """Test creating a Vec2 instance."""
        v = Vec2(3.0, 4.0)
        assert v.x == 3.0
        assert v.y == 4.0
    
    def test_origin(self):
        """Test origin vector."""
        v = Vec2.origin()
        assert v.x == 0.0
        assert v.y == 0.0
    
    def test_unit_vectors(self):
        """Test unit vectors."""
        unit_x = Vec2.unit_x()
        assert unit_x.x == 1.0
        assert unit_x.y == 0.0
        
        unit_y = Vec2.unit_y()
        assert unit_y.x == 0.0
        assert unit_y.y == 1.0
    
    def test_min_max_values(self):
        """Test min/max value vectors."""
        min_v = Vec2.min_value()
        assert min_v.x == -float('inf')
        assert min_v.y == -float('inf')
        
        max_v = Vec2.max_value()
        assert max_v.x == float('inf')
        assert max_v.y == float('inf')


class TestVec2Operations:
    """Test Vec2 mathematical operations."""
    
    def test_length(self):
        """Test vector length calculation."""
        v = Vec2(3.0, 4.0)
        assert v.length() == 5.0
        
        v2 = Vec2(0.0, 0.0)
        assert v2.length() == 0.0
    
    def test_length_squared(self):
        """Test squared length calculation."""
        v = Vec2(3.0, 4.0)
        assert v.length_squared() == 25.0
    
    def test_angle(self):
        """Test angle calculation."""
        v1 = Vec2(1.0, 0.0)
        assert v1.angle() == 0.0
        
        v2 = Vec2(0.0, 1.0)
        assert math.isclose(v2.angle(), math.pi / 2)
        
        v3 = Vec2(-1.0, 0.0)
        assert math.isclose(v3.angle(), math.pi)
        
        v4 = Vec2(0.0, -1.0)
        assert math.isclose(v4.angle(), -math.pi / 2)
    
    def test_dot_product(self):
        """Test dot product calculation."""
        v1 = Vec2(3.0, 4.0)
        v2 = Vec2(2.0, 1.0)
        assert Vec2.dot(v1, v2) == 10.0
        
        # Perpendicular vectors
        v3 = Vec2(1.0, 0.0)
        v4 = Vec2(0.0, 1.0)
        assert Vec2.dot(v3, v4) == 0.0
    
    def test_normalization(self):
        """Test vector normalization."""
        v = Vec2(3.0, 4.0)
        normalized = Vec2.normal(v)
        assert math.isclose(normalized.x, 0.6)
        assert math.isclose(normalized.y, 0.8)
        assert math.isclose(normalized.length(), 1.0)
        
        # Zero vector
        zero = Vec2(0.0, 0.0)
        normalized_zero = Vec2.normal(zero)
        assert normalized_zero.x == 0.0
        assert normalized_zero.y == 0.0


class TestVec2Arithmetic:
    """Test Vec2 arithmetic operations."""
    
    def test_addition(self):
        """Test vector addition."""
        v1 = Vec2(1.0, 2.0)
        v2 = Vec2(3.0, 4.0)
        result = v1 + v2
        assert result.x == 4.0
        assert result.y == 6.0
    
    def test_subtraction(self):
        """Test vector subtraction."""
        v1 = Vec2(5.0, 7.0)
        v2 = Vec2(2.0, 3.0)
        result = v1 - v2
        assert result.x == 3.0
        assert result.y == 4.0
    
    def test_multiplication(self):
        """Test scalar multiplication."""
        v = Vec2(2.0, 3.0)
        result1 = v * 2.0
        assert result1.x == 4.0
        assert result1.y == 6.0
        
        result2 = 3.0 * v
        assert result2.x == 6.0
        assert result2.y == 9.0
    
    def test_division(self):
        """Test scalar division."""
        v = Vec2(6.0, 8.0)
        result = v / 2.0
        assert result.x == 3.0
        assert result.y == 4.0
    
    def test_unary_operators(self):
        """Test unary operators."""
        v = Vec2(3.0, -4.0)
        
        pos_v = +v
        assert pos_v.x == 3.0
        assert pos_v.y == -4.0
        
        neg_v = -v
        assert neg_v.x == -3.0
        assert neg_v.y == 4.0


class TestVec2Intersection:
    """Test line intersection calculations."""
    
    def test_simple_intersection(self):
        """Test simple line intersection."""
        # Two lines that intersect at (2, 2)
        a1 = Vec2(0.0, 0.0)
        a2 = Vec2(4.0, 4.0)
        b1 = Vec2(0.0, 4.0)
        b2 = Vec2(4.0, 0.0)
        
        result = Vec2.inters(a1, a2, b1, b2)
        assert result is not None
        assert math.isclose(result.x, 2.0)
        assert math.isclose(result.y, 2.0)
    
    def test_no_intersection(self):
        """Test parallel lines with no intersection."""
        # Parallel lines
        a1 = Vec2(0.0, 0.0)
        a2 = Vec2(1.0, 0.0)
        b1 = Vec2(0.0, 1.0)
        b2 = Vec2(1.0, 1.0)
        
        result = Vec2.inters(a1, a2, b1, b2)
        assert result is None
    
    def test_infinite_intersection(self):
        """Test intersection with infinite lines."""
        # Lines that don't intersect as segments but do as infinite lines
        a1 = Vec2(0.0, 0.0)
        a2 = Vec2(1.0, 0.0)
        b1 = Vec2(2.0, -1.0)
        b2 = Vec2(2.0, 1.0)
        
        # No intersection as segments
        result_finite = Vec2.inters(a1, a2, b1, b2, infinite=False)
        assert result_finite is None
        
        # Intersection as infinite lines
        result_infinite = Vec2.inters(a1, a2, b1, b2, infinite=True)
        assert result_infinite is not None
        assert math.isclose(result_infinite.x, 2.0)
        assert math.isclose(result_infinite.y, 0.0)
    
    def test_inters_must(self):
        """Test inters_must raises exception when no intersection."""
        a1 = Vec2(0.0, 0.0)
        a2 = Vec2(1.0, 0.0)
        b1 = Vec2(0.0, 1.0)
        b2 = Vec2(1.0, 1.0)
        
        with pytest.raises(ValueError):
            Vec2.inters_must(a1, a2, b1, b2)
    
    def test_inters_list(self):
        """Test intersection with list of connected segments."""
        a1 = Vec2(0.0, 2.0)
        a2 = Vec2(4.0, 2.0)
        
        # Polyline that intersects the test line
        polyline = [
            Vec2(1.0, 0.0),
            Vec2(1.0, 3.0),  # This segment intersects at (1, 2)
            Vec2(3.0, 3.0),
            Vec2(3.0, 0.0)   # This segment intersects at (3, 2)
        ]
        
        result = Vec2.inters_list(a1, a2, polyline)
        assert result is not None
        assert math.isclose(result.x, 1.0)
        assert math.isclose(result.y, 2.0)


class TestVec2Utilities:
    """Test Vec2 utility functions."""
    
    def test_min_max(self):
        """Test component-wise min/max."""
        v1 = Vec2(1.0, 4.0)
        v2 = Vec2(3.0, 2.0)
        
        min_v = Vec2.min(v1, v2)
        assert min_v.x == 1.0
        assert min_v.y == 2.0
        
        max_v = Vec2.max(v1, v2)
        assert max_v.x == 3.0
        assert max_v.y == 4.0
    
    def test_string_representation(self):
        """Test string representations."""
        v = Vec2(1.5, 2.5)
        assert str(v) == "(1.5,2.5)"
        assert repr(v) == "Vec2(x=1.5, y=2.5)"


class TestVec2EdgeCases:
    """Test edge cases and special values."""
    
    def test_zero_vector_operations(self):
        """Test operations with zero vector."""
        zero = Vec2(0.0, 0.0)
        v = Vec2(3.0, 4.0)
        
        assert (v + zero).x == v.x
        assert (v + zero).y == v.y
        assert (v - zero).x == v.x
        assert (v - zero).y == v.y
        assert Vec2.dot(v, zero) == 0.0
    
    def test_negative_values(self):
        """Test operations with negative values."""
        v = Vec2(-3.0, -4.0)
        assert v.length() == 5.0
        assert v.length_squared() == 25.0
    
    def test_very_small_values(self):
        """Test with very small values."""
        epsilon = 1e-10
        v = Vec2(epsilon, epsilon)
        assert v.length() > 0
        assert v.length_squared() > 0
        
        normalized = Vec2.normal(v)
        assert math.isclose(normalized.length(), 1.0, rel_tol=1e-9)
    
    def test_very_large_values(self):
        """Test with very large values."""
        large = 1e100
        v = Vec2(large, large)
        normalized = Vec2.normal(v)
        assert math.isclose(normalized.length(), 1.0, rel_tol=1e-9)


class TestVec2Equality:
    """Test Vec2 equality comparisons."""
    
    def test_equality(self):
        """Test vector equality."""
        v1 = Vec2(1.0, 2.0)
        v2 = Vec2(1.0, 2.0)
        v3 = Vec2(1.0, 2.1)
        
        assert v1 == v2
        assert v1 != v3
        assert not (v1 == v3)
        assert not (v1 != v2)