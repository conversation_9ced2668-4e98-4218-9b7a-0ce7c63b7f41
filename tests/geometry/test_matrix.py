"""
Test suite for Mat4 matrix class.

Tests all matrix operations including transformations and the to_list method.
"""

import pytest
import math
from src.geometry.matrix import Mat4
from src.geometry.primitives import Vec3
from src.geometry.basis import Basis3


class TestMat4:
    """Test Mat4 matrix functionality."""
    
    def test_creation(self):
        """Test matrix creation with default values."""
        m = Mat4()
        # Check identity matrix
        assert m.m11 == 1.0 and m.m22 == 1.0 and m.m33 == 1.0 and m.m44 == 1.0
        assert m.m12 == 0.0 and m.m13 == 0.0 and m.m14 == 0.0
        assert m.m21 == 0.0 and m.m23 == 0.0 and m.m24 == 0.0
        assert m.m31 == 0.0 and m.m32 == 0.0 and m.m34 == 0.0
        assert m.m41 == 0.0 and m.m42 == 0.0 and m.m43 == 0.0
    
    def test_identity(self):
        """Test identity matrix creation."""
        m = Mat4.identity()
        
        # Test diagonal elements
        assert m.m11 == 1.0
        assert m.m22 == 1.0
        assert m.m33 == 1.0
        assert m.m44 == 1.0
        
        # Test that identity doesn't transform vectors
        v = Vec3(1.0, 2.0, 3.0)
        transformed = m.transform_position(v)
        assert transformed.x == v.x
        assert transformed.y == v.y
        assert transformed.z == v.z
    
    def test_translation(self):
        """Test translation matrix creation and application."""
        # Create translation matrix
        m = Mat4.translation(5.0, 10.0, 15.0)
        
        # Test translation extraction
        trans = m.get_translation()
        assert trans.x == 5.0
        assert trans.y == 10.0
        assert trans.z == 15.0
        
        # Test transforming a point
        v = Vec3(1.0, 2.0, 3.0)
        transformed = m.transform_position(v)
        assert transformed.x == 6.0   # 1 + 5
        assert transformed.y == 12.0  # 2 + 10
        assert transformed.z == 18.0  # 3 + 15
        
        # Test that vectors (directions) are not translated
        direction = m.transform_vector(v)
        assert direction.x == v.x
        assert direction.y == v.y
        assert direction.z == v.z
    
    def test_scale(self):
        """Test scale matrix creation and application."""
        # Uniform scale
        m1 = Mat4.scale(2.0, 2.0, 2.0)
        v = Vec3(1.0, 2.0, 3.0)
        scaled = m1.transform_vector(v)
        assert scaled.x == 2.0
        assert scaled.y == 4.0
        assert scaled.z == 6.0
        
        # Non-uniform scale
        m2 = Mat4.scale(1.0, 2.0, 3.0)
        scaled2 = m2.transform_vector(v)
        assert scaled2.x == 1.0
        assert scaled2.y == 4.0
        assert scaled2.z == 9.0
    
    def test_rotation_x(self):
        """Test rotation around X axis."""
        # 90 degree rotation around X
        m = Mat4.rotation_x(math.pi / 2)
        
        # Y axis should rotate to Z axis
        v = Vec3(0.0, 1.0, 0.0)
        rotated = m.transform_vector(v)
        assert abs(rotated.x - 0.0) < 1e-10
        assert abs(rotated.y - 0.0) < 1e-10
        assert abs(rotated.z - 1.0) < 1e-10
        
        # Z axis should rotate to -Y axis
        v2 = Vec3(0.0, 0.0, 1.0)
        rotated2 = m.transform_vector(v2)
        assert abs(rotated2.x - 0.0) < 1e-10
        assert abs(rotated2.y - (-1.0)) < 1e-10
        assert abs(rotated2.z - 0.0) < 1e-10
    
    def test_rotation_y(self):
        """Test rotation around Y axis."""
        # 90 degree rotation around Y
        m = Mat4.rotation_y(math.pi / 2)
        
        # Z axis should rotate to X axis
        v = Vec3(0.0, 0.0, 1.0)
        rotated = m.transform_vector(v)
        assert abs(rotated.x - 1.0) < 1e-10
        assert abs(rotated.y - 0.0) < 1e-10
        assert abs(rotated.z - 0.0) < 1e-10
        
        # X axis should rotate to -Z axis
        v2 = Vec3(1.0, 0.0, 0.0)
        rotated2 = m.transform_vector(v2)
        assert abs(rotated2.x - 0.0) < 1e-10
        assert abs(rotated2.y - 0.0) < 1e-10
        assert abs(rotated2.z - (-1.0)) < 1e-10
    
    def test_rotation_z(self):
        """Test rotation around Z axis."""
        # 90 degree rotation around Z
        m = Mat4.rotation_z(math.pi / 2)
        
        # X axis should rotate to Y axis
        v = Vec3(1.0, 0.0, 0.0)
        rotated = m.transform_vector(v)
        assert abs(rotated.x - 0.0) < 1e-10
        assert abs(rotated.y - 1.0) < 1e-10
        assert abs(rotated.z - 0.0) < 1e-10
        
        # Y axis should rotate to -X axis
        v2 = Vec3(0.0, 1.0, 0.0)
        rotated2 = m.transform_vector(v2)
        assert abs(rotated2.x - (-1.0)) < 1e-10
        assert abs(rotated2.y - 0.0) < 1e-10
        assert abs(rotated2.z - 0.0) < 1e-10
    
    def test_matrix_multiplication(self):
        """Test matrix multiplication."""
        # Translation then rotation
        trans = Mat4.translation(10.0, 0.0, 0.0)
        rot = Mat4.rotation_z(math.pi / 2)  # 90 degrees
        
        # Combined transformation
        combined = rot * trans
        
        # Test point transformation
        v = Vec3(0.0, 0.0, 0.0)
        result = combined.transform_position(v)
        
        # First translate (0,0,0) -> (10,0,0)
        # Then rotate 90° around Z: (10,0,0) -> (0,10,0)
        assert abs(result.x - 0.0) < 1e-10
        assert abs(result.y - 10.0) < 1e-10
        assert abs(result.z - 0.0) < 1e-10
    
    def test_get_basis(self):
        """Test basis extraction from matrix."""
        # Create a rotation matrix
        m = Mat4.rotation_z(math.pi / 4)  # 45 degrees
        
        basis = m.get_basis()
        
        # Check that basis vectors are unit length and orthogonal
        assert abs(basis.x.length() - 1.0) < 1e-10
        assert abs(basis.y.length() - 1.0) < 1e-10
        assert abs(basis.z.length() - 1.0) < 1e-10
        
        # Check orthogonality
        assert abs(Vec3.dot(basis.x, basis.y)) < 1e-10
        assert abs(Vec3.dot(basis.x, basis.z)) < 1e-10
        assert abs(Vec3.dot(basis.y, basis.z)) < 1e-10
    
    def test_to_list(self):
        """Test matrix to list conversion (column-major for GLTF)."""
        m = Mat4(
            1.0, 2.0, 3.0, 4.0,
            5.0, 6.0, 7.0, 8.0,
            9.0, 10.0, 11.0, 12.0,
            13.0, 14.0, 15.0, 16.0
        )
        
        # Get column-major list
        lst = m.to_list()
        
        # Verify column-major order
        expected = [
            1.0, 5.0, 9.0, 13.0,   # First column
            2.0, 6.0, 10.0, 14.0,  # Second column
            3.0, 7.0, 11.0, 15.0,  # Third column
            4.0, 8.0, 12.0, 16.0   # Fourth column
        ]
        
        assert lst == expected
    
    def test_inverse(self):
        """Test matrix inversion."""
        # Create a simple transformation
        m = Mat4.translation(5.0, 10.0, 15.0)
        
        # Get inverse
        inv = m.get_inverse()
        
        # Multiply matrix by its inverse should give identity
        identity = m * inv
        
        # Check diagonal elements are 1
        assert abs(identity.m11 - 1.0) < 1e-10
        assert abs(identity.m22 - 1.0) < 1e-10
        assert abs(identity.m33 - 1.0) < 1e-10
        assert abs(identity.m44 - 1.0) < 1e-10
        
        # Check off-diagonal elements are 0
        assert abs(identity.m12) < 1e-10
        assert abs(identity.m13) < 1e-10
        assert abs(identity.m14) < 1e-10
        
        # Test that inverse translation works
        v = Vec3(10.0, 20.0, 30.0)
        transformed = m.transform_position(v)
        back = inv.transform_position(transformed)
        
        assert abs(back.x - v.x) < 1e-10
        assert abs(back.y - v.y) < 1e-10
        assert abs(back.z - v.z) < 1e-10
    
    def test_transform_normal(self):
        """Test normal transformation (uses inverse transpose)."""
        # Create a non-uniform scale
        m = Mat4.scale(2.0, 1.0, 1.0)
        
        # Transform a normal
        normal = Vec3(1.0, 0.0, 0.0)
        transformed = m.transform_normal(normal)
        
        # Normal should be scaled inversely
        # For scale (2,1,1), normal should be scaled by (1/2,1,1)
        assert abs(transformed.x - 0.5) < 1e-10
        assert abs(transformed.y - 0.0) < 1e-10
        assert abs(transformed.z - 0.0) < 1e-10
    
    def test_combined_transformation(self):
        """Test combined translation, rotation, and scale."""
        # Scale by 2, rotate 45° around Z, then translate
        scale = Mat4.scale(2.0, 2.0, 2.0)
        rotate = Mat4.rotation_z(math.pi / 4)
        translate = Mat4.translation(10.0, 20.0, 0.0)
        
        # Combined: T * R * S
        combined = translate * rotate * scale
        
        # Transform a point
        v = Vec3(1.0, 0.0, 0.0)
        result = combined.transform_position(v)
        
        # Expected: scale (1,0,0) -> (2,0,0)
        # rotate 45° -> (√2, √2, 0)
        # translate -> (10+√2, 20+√2, 0)
        sqrt2 = math.sqrt(2)
        assert abs(result.x - (10.0 + sqrt2)) < 1e-10
        assert abs(result.y - (20.0 + sqrt2)) < 1e-10
        assert abs(result.z - 0.0) < 1e-10
    
    def test_string_representation(self):
        """Test string representation of matrix."""
        m = Mat4.identity()
        s = str(m)
        
        # Should contain the matrix elements
        assert "M11:1" in s or "M11:1.0" in s
        assert "M22:1" in s or "M22:1.0" in s
        assert "M33:1" in s or "M33:1.0" in s
        assert "M44:1" in s or "M44:1.0" in s


if __name__ == "__main__":
    pytest.main([__file__, "-v"])