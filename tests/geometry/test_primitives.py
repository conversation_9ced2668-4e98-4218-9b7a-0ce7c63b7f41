"""
Test suite for geometry primitives (Vec2 and Vec3).

Tests all vector operations including the newly added methods.
"""

import pytest
import math
from src.geometry.primitives import Vec2, Vec3


class TestVec2:
    """Test Vec2 class functionality."""
    
    def test_creation(self):
        """Test Vec2 creation and basic properties."""
        v = Vec2(3.0, 4.0)
        assert v.x == 3.0
        assert v.y == 4.0
    
    def test_length(self):
        """Test vector length calculation."""
        v = Vec2(3.0, 4.0)
        assert v.length() == 5.0  # 3-4-5 triangle
        
        v2 = Vec2(0.0, 0.0)
        assert v2.length() == 0.0
    
    def test_length_squared(self):
        """Test squared length calculation."""
        v = Vec2(3.0, 4.0)
        assert v.length_squared() == 25.0
    
    def test_angle(self):
        """Test angle calculation."""
        v1 = Vec2(1.0, 0.0)
        assert abs(v1.angle() - 0.0) < 1e-10
        
        v2 = Vec2(0.0, 1.0)
        assert abs(v2.angle() - math.pi/2) < 1e-10
        
        v3 = Vec2(-1.0, 0.0)
        assert abs(v3.angle() - math.pi) < 1e-10
    
    def test_static_origin(self):
        """Test origin creation."""
        origin = Vec2.origin()
        assert origin.x == 0.0
        assert origin.y == 0.0
    
    def test_static_units(self):
        """Test unit vector creation."""
        unit_x = Vec2.unit_x()
        assert unit_x.x == 1.0
        assert unit_x.y == 0.0
        
        unit_y = Vec2.unit_y()
        assert unit_y.x == 0.0
        assert unit_y.y == 1.0
    
    def test_dot_product(self):
        """Test dot product calculation."""
        v1 = Vec2(3.0, 4.0)
        v2 = Vec2(1.0, 2.0)
        assert Vec2.dot(v1, v2) == 11.0  # 3*1 + 4*2
        
        # Perpendicular vectors
        v3 = Vec2(1.0, 0.0)
        v4 = Vec2(0.0, 1.0)
        assert Vec2.dot(v3, v4) == 0.0
    
    def test_normalization(self):
        """Test vector normalization."""
        v = Vec2(3.0, 4.0)
        normalized = Vec2.normal(v)
        assert abs(normalized.length() - 1.0) < 1e-10
        assert abs(normalized.x - 0.6) < 1e-10
        assert abs(normalized.y - 0.8) < 1e-10
        
        # Zero vector
        zero = Vec2(0.0, 0.0)
        normalized_zero = Vec2.normal(zero)
        assert normalized_zero.x == 0.0
        assert normalized_zero.y == 0.0
    
    def test_cross_product(self):
        """Test 2D cross product (scalar result)."""
        v1 = Vec2(3.0, 4.0)
        v2 = Vec2(1.0, 2.0)
        cross = Vec2.cross(v1, v2)
        assert cross == 2.0  # 3*2 - 4*1
    
    def test_distance(self):
        """Test distance calculation."""
        v1 = Vec2(0.0, 0.0)
        v2 = Vec2(3.0, 4.0)
        assert Vec2.distance(v1, v2) == 5.0
    
    def test_addition(self):
        """Test vector addition."""
        v1 = Vec2(1.0, 2.0)
        v2 = Vec2(3.0, 4.0)
        
        # Using operator
        result1 = v1 + v2
        assert result1.x == 4.0
        assert result1.y == 6.0
        
        # Using add method (newly added)
        result2 = v1.add(v2)
        assert result2.x == 4.0
        assert result2.y == 6.0
    
    def test_subtraction(self):
        """Test vector subtraction."""
        v1 = Vec2(5.0, 7.0)
        v2 = Vec2(2.0, 3.0)
        result = v1 - v2
        assert result.x == 3.0
        assert result.y == 4.0
    
    def test_scalar_multiplication(self):
        """Test scalar multiplication."""
        v = Vec2(2.0, 3.0)
        
        # Vector * scalar
        result1 = v * 2.0
        assert result1.x == 4.0
        assert result1.y == 6.0
        
        # Scalar * vector
        result2 = 2.0 * v
        assert result2.x == 4.0
        assert result2.y == 6.0
    
    def test_scalar_division(self):
        """Test scalar division."""
        v = Vec2(6.0, 8.0)
        result = v / 2.0
        assert result.x == 3.0
        assert result.y == 4.0
    
    def test_min_max(self):
        """Test component-wise min/max."""
        v1 = Vec2(1.0, 4.0)
        v2 = Vec2(3.0, 2.0)
        
        min_v = Vec2.min(v1, v2)
        assert min_v.x == 1.0
        assert min_v.y == 2.0
        
        max_v = Vec2.max(v1, v2)
        assert max_v.x == 3.0
        assert max_v.y == 4.0
    
    def test_intersection(self):
        """Test line intersection."""
        # Two intersecting lines
        a1 = Vec2(0.0, 0.0)
        a2 = Vec2(10.0, 10.0)
        b1 = Vec2(0.0, 10.0)
        b2 = Vec2(10.0, 0.0)
        
        intersection = Vec2.inters(a1, a2, b1, b2)
        assert intersection is not None
        assert abs(intersection.x - 5.0) < 1e-10
        assert abs(intersection.y - 5.0) < 1e-10
        
        # Parallel lines (no intersection)
        c1 = Vec2(0.0, 0.0)
        c2 = Vec2(10.0, 0.0)
        d1 = Vec2(0.0, 1.0)
        d2 = Vec2(10.0, 1.0)
        
        no_intersection = Vec2.inters(c1, c2, d1, d2)
        assert no_intersection is None
    
    def test_string_representation(self):
        """Test string representations."""
        v = Vec2(1.5, 2.5)
        assert str(v) == "(1.5,2.5)"
        assert repr(v) == "Vec2(x=1.5, y=2.5)"


class TestVec3:
    """Test Vec3 class functionality."""
    
    def test_creation(self):
        """Test Vec3 creation and basic properties."""
        v = Vec3(1.0, 2.0, 3.0)
        assert v.x == 1.0
        assert v.y == 2.0
        assert v.z == 3.0
    
    def test_length(self):
        """Test vector length calculation."""
        v = Vec3(2.0, 3.0, 6.0)
        assert v.length() == 7.0  # sqrt(4 + 9 + 36)
        
        zero = Vec3(0.0, 0.0, 0.0)
        assert zero.length() == 0.0
    
    def test_length_squared(self):
        """Test squared length calculation."""
        v = Vec3(2.0, 3.0, 6.0)
        assert v.length_squared() == 49.0
    
    def test_distance_squared(self):
        """Test squared distance calculation."""
        v1 = Vec3(1.0, 2.0, 3.0)
        v2 = Vec3(4.0, 6.0, 8.0)
        assert v1.distance_squared(v2) == 50.0  # 3² + 4² + 5²
    
    def test_to_array(self):
        """Test array conversion."""
        v = Vec3(1.0, 2.0, 3.0)
        arr = v.to_array()
        assert arr == [1.0, 2.0, 3.0]
    
    def test_midpoint(self):
        """Test midpoint calculation."""
        v1 = Vec3(0.0, 0.0, 0.0)
        v2 = Vec3(2.0, 4.0, 6.0)
        mid = Vec3.midpoint(v1, v2)
        assert mid.x == 1.0
        assert mid.y == 2.0
        assert mid.z == 3.0
    
    def test_static_origin(self):
        """Test origin and zero creation."""
        origin = Vec3.origin()
        assert origin.x == 0.0
        assert origin.y == 0.0
        assert origin.z == 0.0
        
        zero = Vec3.zero()
        assert zero == origin
    
    def test_static_units(self):
        """Test unit vector creation."""
        unit_x = Vec3.unit_x()
        assert unit_x == Vec3(1.0, 0.0, 0.0)
        
        unit_y = Vec3.unit_y()
        assert unit_y == Vec3(0.0, 1.0, 0.0)
        
        unit_z = Vec3.unit_z()
        assert unit_z == Vec3(0.0, 0.0, 1.0)
    
    def test_dot_product(self):
        """Test dot product calculation."""
        v1 = Vec3(1.0, 2.0, 3.0)
        v2 = Vec3(4.0, 5.0, 6.0)
        assert Vec3.dot(v1, v2) == 32.0  # 1*4 + 2*5 + 3*6
        
        # Perpendicular vectors
        v3 = Vec3(1.0, 0.0, 0.0)
        v4 = Vec3(0.0, 1.0, 0.0)
        assert Vec3.dot(v3, v4) == 0.0
    
    def test_cross_product(self):
        """Test cross product calculation."""
        v1 = Vec3(1.0, 0.0, 0.0)
        v2 = Vec3(0.0, 1.0, 0.0)
        cross = Vec3.cross(v1, v2)
        assert cross == Vec3(0.0, 0.0, 1.0)
        
        # Test with arbitrary vectors
        v3 = Vec3(2.0, 3.0, 4.0)
        v4 = Vec3(5.0, 6.0, 7.0)
        cross2 = Vec3.cross(v3, v4)
        assert cross2.x == -3.0  # 3*7 - 4*6
        assert cross2.y == 6.0   # 4*5 - 2*7
        assert cross2.z == -3.0  # 2*6 - 3*5
    
    def test_normalization(self):
        """Test vector normalization."""
        v = Vec3(3.0, 0.0, 4.0)
        normalized = Vec3.normal(v)
        assert abs(normalized.length() - 1.0) < 1e-10
        assert abs(normalized.x - 0.6) < 1e-10
        assert abs(normalized.y - 0.0) < 1e-10
        assert abs(normalized.z - 0.8) < 1e-10
        
        # Zero vector
        zero = Vec3(0.0, 0.0, 0.0)
        normalized_zero = Vec3.normal(zero)
        assert normalized_zero == Vec3(0.0, 0.0, 0.0)
    
    def test_distance(self):
        """Test distance calculation."""
        v1 = Vec3(0.0, 0.0, 0.0)
        v2 = Vec3(2.0, 3.0, 6.0)
        assert Vec3.distance(v1, v2) == 7.0
        
        # Test dist method (newly added)
        assert v1.dist(v2) == 7.0
    
    def test_addition(self):
        """Test vector addition."""
        v1 = Vec3(1.0, 2.0, 3.0)
        v2 = Vec3(4.0, 5.0, 6.0)
        result = v1 + v2
        assert result == Vec3(5.0, 7.0, 9.0)
    
    def test_subtraction(self):
        """Test vector subtraction."""
        v1 = Vec3(5.0, 7.0, 9.0)
        v2 = Vec3(1.0, 2.0, 3.0)
        result = v1 - v2
        assert result == Vec3(4.0, 5.0, 6.0)
    
    def test_scalar_multiplication(self):
        """Test scalar multiplication."""
        v = Vec3(1.0, 2.0, 3.0)
        
        # Vector * scalar
        result1 = v * 2.0
        assert result1 == Vec3(2.0, 4.0, 6.0)
        
        # Scalar * vector
        result2 = 2.0 * v
        assert result2 == Vec3(2.0, 4.0, 6.0)
    
    def test_scalar_division(self):
        """Test scalar division."""
        v = Vec3(6.0, 8.0, 10.0)
        result = v / 2.0
        assert result == Vec3(3.0, 4.0, 5.0)
    
    def test_min_max(self):
        """Test component-wise min/max."""
        v1 = Vec3(1.0, 5.0, 3.0)
        v2 = Vec3(4.0, 2.0, 6.0)
        
        min_v = Vec3.min(v1, v2)
        assert min_v == Vec3(1.0, 2.0, 3.0)
        
        max_v = Vec3.max(v1, v2)
        assert max_v == Vec3(4.0, 5.0, 6.0)
    
    def test_string_representation(self):
        """Test string representations."""
        v = Vec3(1.5, 2.5, 3.5)
        assert str(v) == "(1.5,2.5,3.5)"
        assert repr(v) == "Vec3(x=1.5, y=2.5, z=3.5)"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])