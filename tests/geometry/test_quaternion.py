"""
Test suite for Quaternion class.

Tests quaternion operations for 3D rotations.
"""

import pytest
import math
from src.geometry.quaternion import Quaternion
from src.geometry.primitives import Vec3
from src.geometry.matrix import Mat4


class TestQuaternion:
    """Test Quaternion functionality."""
    
    def test_creation(self):
        """Test quaternion creation."""
        q = Quaternion(0.5, 0.5, 0.5, 0.5)
        assert q.w == 0.5
        assert q.x == 0.5
        assert q.y == 0.5
        assert q.z == 0.5
    
    def test_identity(self):
        """Test identity quaternion."""
        q = Quaternion.identity()
        assert q.w == 1.0
        assert q.x == 0.0
        assert q.y == 0.0
        assert q.z == 0.0
    
    def test_from_axis_angle(self):
        """Test creation from axis and angle."""
        # 90 degree rotation around Z axis
        axis = Vec3(0, 0, 1)
        angle = math.pi / 2
        
        q = Quaternion.from_axis_angle(axis, angle)
        
        # Expected: w = cos(45°), z = sin(45°)
        assert q.w == pytest.approx(math.cos(angle / 2))
        assert q.x == pytest.approx(0.0)
        assert q.y == pytest.approx(0.0)
        assert q.z == pytest.approx(math.sin(angle / 2))
    
    def test_from_euler_angles(self):
        """Test creation from Euler angles."""
        # Simple rotations
        roll = math.pi / 6   # 30 degrees around X
        pitch = math.pi / 4  # 45 degrees around Y
        yaw = math.pi / 3    # 60 degrees around Z
        
        q = Quaternion.from_euler(roll, pitch, yaw)
        
        # Convert back to Euler angles
        r2, p2, y2 = q.to_euler()
        
        assert r2 == pytest.approx(roll)
        assert p2 == pytest.approx(pitch)
        assert y2 == pytest.approx(yaw)
    
    def test_magnitude(self):
        """Test quaternion magnitude."""
        q = Quaternion(0.5, 0.5, 0.5, 0.5)
        assert q.magnitude() == pytest.approx(1.0)
        
        # Non-unit quaternion
        q2 = Quaternion(1, 2, 3, 4)
        expected = math.sqrt(1*1 + 2*2 + 3*3 + 4*4)
        assert q2.magnitude() == pytest.approx(expected)
    
    def test_normalize(self):
        """Test quaternion normalization."""
        q = Quaternion(1, 2, 3, 4)
        normalized = q.normalized()
        
        assert normalized.magnitude() == pytest.approx(1.0)
        
        # Check direction is preserved
        ratio = q.w / normalized.w
        assert q.x / normalized.x == pytest.approx(ratio)
        assert q.y / normalized.y == pytest.approx(ratio)
        assert q.z / normalized.z == pytest.approx(ratio)
    
    def test_conjugate(self):
        """Test quaternion conjugate."""
        q = Quaternion(0.5, 0.5, 0.5, 0.5)
        conj = q.conjugate()
        
        assert conj.w == q.w
        assert conj.x == -q.x
        assert conj.y == -q.y
        assert conj.z == -q.z
    
    def test_inverse(self):
        """Test quaternion inverse."""
        q = Quaternion.from_axis_angle(Vec3(0, 0, 1), math.pi / 4)
        inv = q.inverse()
        
        # q * q^-1 should equal identity
        result = q * inv
        assert result.w == pytest.approx(1.0)
        assert result.x == pytest.approx(0.0, abs=1e-10)
        assert result.y == pytest.approx(0.0, abs=1e-10)
        assert result.z == pytest.approx(0.0, abs=1e-10)
    
    def test_multiplication(self):
        """Test quaternion multiplication."""
        # Two 90-degree rotations around Z
        q1 = Quaternion.from_axis_angle(Vec3(0, 0, 1), math.pi / 2)
        q2 = Quaternion.from_axis_angle(Vec3(0, 0, 1), math.pi / 2)
        
        # Should result in 180-degree rotation
        result = q1 * q2
        
        # Apply to a vector to test
        v = Vec3(1, 0, 0)
        rotated = result.rotate_vector(v)
        
        # Should point in -X direction
        assert rotated.x == pytest.approx(-1.0)
        assert rotated.y == pytest.approx(0.0, abs=1e-10)
        assert rotated.z == pytest.approx(0.0, abs=1e-10)
    
    def test_rotate_vector(self):
        """Test rotating a vector."""
        # 90 degree rotation around Z
        q = Quaternion.from_axis_angle(Vec3(0, 0, 1), math.pi / 2)
        
        # Rotate X axis
        v = Vec3(1, 0, 0)
        rotated = q.rotate_vector(v)
        
        # Should now point in Y direction
        assert rotated.x == pytest.approx(0.0, abs=1e-10)
        assert rotated.y == pytest.approx(1.0)
        assert rotated.z == pytest.approx(0.0)
        
        # Rotate Y axis
        v2 = Vec3(0, 1, 0)
        rotated2 = q.rotate_vector(v2)
        
        # Should now point in -X direction
        assert rotated2.x == pytest.approx(-1.0)
        assert rotated2.y == pytest.approx(0.0, abs=1e-10)
        assert rotated2.z == pytest.approx(0.0)
    
    def test_to_matrix(self):
        """Test conversion to rotation matrix."""
        # 45 degree rotation around Z
        q = Quaternion.from_axis_angle(Vec3(0, 0, 1), math.pi / 4)
        matrix = q.to_matrix()
        
        # Test by applying to vector
        v = Vec3(1, 0, 0)
        rotated = matrix.transform_vector(v)
        
        # Should be at 45 degrees
        assert rotated.x == pytest.approx(math.sqrt(2) / 2)
        assert rotated.y == pytest.approx(math.sqrt(2) / 2)
        assert rotated.z == pytest.approx(0.0)
    
    def test_from_matrix(self):
        """Test creation from rotation matrix."""
        # Create a known rotation matrix
        angle = math.pi / 3  # 60 degrees
        matrix = Mat4.rotation_z(angle)
        
        # Convert to quaternion
        q = Quaternion.from_matrix(matrix)
        
        # Convert back to matrix
        matrix2 = q.to_matrix()
        
        # Matrices should be equivalent
        for i in range(4):
            for j in range(4):
                m1_val = getattr(matrix, f"m{i+1}{j+1}")
                m2_val = getattr(matrix2, f"m{i+1}{j+1}")
                assert m1_val == pytest.approx(m2_val)
    
    def test_slerp(self):
        """Test spherical linear interpolation."""
        # Start: no rotation
        q1 = Quaternion.identity()
        
        # End: 90 degree rotation around Z
        q2 = Quaternion.from_axis_angle(Vec3(0, 0, 1), math.pi / 2)
        
        # Interpolate halfway
        q_mid = Quaternion.slerp(q1, q2, 0.5)
        
        # Should be 45 degree rotation
        v = Vec3(1, 0, 0)
        rotated = q_mid.rotate_vector(v)
        
        assert rotated.x == pytest.approx(math.sqrt(2) / 2)
        assert rotated.y == pytest.approx(math.sqrt(2) / 2)
        assert rotated.z == pytest.approx(0.0)
        
        # Test endpoints
        assert Quaternion.slerp(q1, q2, 0.0) == q1
        assert Quaternion.slerp(q1, q2, 1.0) == q2
    
    def test_dot_product(self):
        """Test quaternion dot product."""
        q1 = Quaternion(0.5, 0.5, 0.5, 0.5)
        q2 = Quaternion(0.5, 0.5, 0.5, 0.5)
        
        dot = Quaternion.dot(q1, q2)
        assert dot == pytest.approx(1.0)  # 4 * 0.5 * 0.5
        
        # Orthogonal quaternions
        q3 = Quaternion(1, 0, 0, 0)
        q4 = Quaternion(0, 1, 0, 0)
        
        dot2 = Quaternion.dot(q3, q4)
        assert dot2 == pytest.approx(0.0)
    
    def test_angle_between(self):
        """Test angle between quaternions."""
        q1 = Quaternion.identity()
        q2 = Quaternion.from_axis_angle(Vec3(0, 0, 1), math.pi / 2)
        
        angle = q1.angle_to(q2)
        # Should be half of the rotation angle (quaternion space)
        assert angle == pytest.approx(math.pi / 4)
    
    def test_from_two_vectors(self):
        """Test creating rotation between two vectors."""
        v1 = Vec3(1, 0, 0)
        v2 = Vec3(0, 1, 0)
        
        q = Quaternion.from_vectors(v1, v2)
        
        # Apply rotation
        rotated = q.rotate_vector(v1)
        
        # Should align with v2
        assert (rotated - v2).length() < 1e-10
    
    def test_look_rotation(self):
        """Test creating look-at rotation."""
        forward = Vec3(1, 0, 0)
        up = Vec3(0, 0, 1)
        
        q = Quaternion.look_rotation(forward, up)
        
        # Forward should be X
        z_axis = q.rotate_vector(Vec3(0, 0, 1))
        assert (z_axis - forward).length() < 1e-10
        
        # Up should be Z
        y_axis = q.rotate_vector(Vec3(0, 1, 0))
        assert Vec3.dot(y_axis, up) > 0.9  # Mostly aligned
    
    def test_equality(self):
        """Test quaternion equality."""
        q1 = Quaternion(0.5, 0.5, 0.5, 0.5)
        q2 = Quaternion(0.5, 0.5, 0.5, 0.5)
        q3 = Quaternion(0.5, 0.5, 0.5, 0.6)
        
        assert q1 == q2
        assert q1 != q3
    
    def test_negation(self):
        """Test quaternion negation."""
        q = Quaternion(0.5, 0.5, 0.5, 0.5)
        neg = -q
        
        assert neg.w == -0.5
        assert neg.x == -0.5
        assert neg.y == -0.5
        assert neg.z == -0.5
        
        # Negated quaternion represents same rotation
        v = Vec3(1, 0, 0)
        assert q.rotate_vector(v) == neg.rotate_vector(v)
    
    def test_addition_subtraction(self):
        """Test quaternion addition and subtraction."""
        q1 = Quaternion(1, 2, 3, 4)
        q2 = Quaternion(5, 6, 7, 8)
        
        # Addition
        sum_q = q1 + q2
        assert sum_q.w == 6
        assert sum_q.x == 8
        assert sum_q.y == 10
        assert sum_q.z == 12
        
        # Subtraction
        diff_q = q2 - q1
        assert diff_q.w == 4
        assert diff_q.x == 4
        assert diff_q.y == 4
        assert diff_q.z == 4
    
    def test_scalar_multiplication(self):
        """Test scalar multiplication."""
        q = Quaternion(1, 2, 3, 4)
        
        scaled = q * 2
        assert scaled.w == 2
        assert scaled.x == 4
        assert scaled.y == 6
        assert scaled.z == 8
        
        # Right multiplication
        scaled2 = 3 * q
        assert scaled2.w == 3
        assert scaled2.x == 6
        assert scaled2.y == 9
        assert scaled2.z == 12
    
    def test_gimbal_lock_avoidance(self):
        """Test handling of gimbal lock scenarios."""
        # Pitch of 90 degrees (gimbal lock condition)
        q = Quaternion.from_euler(0, math.pi / 2, 0)
        
        # Should still be able to convert back
        roll, pitch, yaw = q.to_euler()
        assert pitch == pytest.approx(math.pi / 2)
    
    def test_string_representation(self):
        """Test string representations."""
        q = Quaternion(0.5, 0.5, 0.5, 0.5)
        
        s = str(q)
        assert "0.5" in s
        assert "w" in s or "W" in s
        
        r = repr(q)
        assert "Quaternion" in r
        assert "0.5" in r


if __name__ == "__main__":
    pytest.main([__file__, "-v"])