"""
Test suite for Basis3 class.

Tests 3D coordinate system basis operations.
"""

import pytest
import math
from src.geometry.basis import Basis3
from src.geometry.primitives import Vec3
from src.geometry.matrix import Mat4


class TestBasis3:
    """Test Basis3 functionality."""
    
    def test_creation(self):
        """Test basis creation with three vectors."""
        x = Vec3(1, 0, 0)
        y = Vec3(0, 1, 0)
        z = Vec3(0, 0, 1)
        
        basis = Basis3(x, y, z)
        assert basis.x == x
        assert basis.y == y
        assert basis.z == z
    
    def test_identity_basis(self):
        """Test identity basis creation."""
        basis = Basis3.identity()
        
        assert basis.x == Vec3(1, 0, 0)
        assert basis.y == Vec3(0, 1, 0)
        assert basis.z == Vec3(0, 0, 1)
    
    def test_from_xy(self):
        """Test basis creation from X and Y vectors."""
        x = Vec3(1, 0, 0)
        y = Vec3(0, 1, 0)
        
        basis = Basis3.from_xy(x, y)
        
        assert basis.x == x
        assert basis.y == y
        # Z should be X cross Y
        expected_z = Vec3.cross(x, y)
        assert basis.z == expected_z
        assert basis.z == Vec3(0, 0, 1)
    
    def test_from_xz(self):
        """Test basis creation from X and Z vectors."""
        x = Vec3(1, 0, 0)
        z = Vec3(0, 0, 1)
        
        basis = Basis3.from_xz(x, z)
        
        assert basis.x == x
        assert basis.z == z
        # Y should be Z cross X
        expected_y = Vec3.cross(z, x)
        assert basis.y == expected_y
        assert basis.y == Vec3(0, 1, 0)
    
    def test_from_yz(self):
        """Test basis creation from Y and Z vectors."""
        y = Vec3(0, 1, 0)
        z = Vec3(0, 0, 1)
        
        basis = Basis3.from_yz(y, z)
        
        assert basis.y == y
        assert basis.z == z
        # X should be Y cross Z
        expected_x = Vec3.cross(y, z)
        assert basis.x == expected_x
        assert basis.x == Vec3(1, 0, 0)
    
    def test_from_z_direction(self):
        """Test basis creation from Z direction only."""
        # Z pointing up
        z = Vec3(0, 0, 1)
        basis = Basis3.from_z(z)
        
        assert basis.z == z
        # X and Y should be perpendicular to Z
        assert abs(Vec3.dot(basis.x, basis.z)) < 1e-10
        assert abs(Vec3.dot(basis.y, basis.z)) < 1e-10
        # X and Y should be perpendicular to each other
        assert abs(Vec3.dot(basis.x, basis.y)) < 1e-10
        # All should be unit vectors
        assert abs(basis.x.length() - 1.0) < 1e-10
        assert abs(basis.y.length() - 1.0) < 1e-10
        assert abs(basis.z.length() - 1.0) < 1e-10
    
    def test_orthonormalization(self):
        """Test orthonormalizing a non-orthogonal basis."""
        # Create non-orthogonal basis
        x = Vec3(1, 0.1, 0)  # Slightly off X axis
        y = Vec3(0.1, 1, 0)  # Slightly off Y axis
        z = Vec3(0, 0, 1)
        
        basis = Basis3(x, y, z)
        ortho_basis = basis.orthonormalize()
        
        # Check all vectors are unit length
        assert abs(ortho_basis.x.length() - 1.0) < 1e-10
        assert abs(ortho_basis.y.length() - 1.0) < 1e-10
        assert abs(ortho_basis.z.length() - 1.0) < 1e-10
        
        # Check all vectors are orthogonal
        assert abs(Vec3.dot(ortho_basis.x, ortho_basis.y)) < 1e-10
        assert abs(Vec3.dot(ortho_basis.x, ortho_basis.z)) < 1e-10
        assert abs(Vec3.dot(ortho_basis.y, ortho_basis.z)) < 1e-10
    
    def test_to_matrix(self):
        """Test conversion to transformation matrix."""
        basis = Basis3.identity()
        matrix = basis.to_matrix()
        
        # Should be identity matrix (no rotation)
        assert matrix.m11 == 1.0 and matrix.m22 == 1.0 and matrix.m33 == 1.0
        assert matrix.m12 == 0.0 and matrix.m13 == 0.0
        assert matrix.m21 == 0.0 and matrix.m23 == 0.0
        assert matrix.m31 == 0.0 and matrix.m32 == 0.0
    
    def test_from_matrix(self):
        """Test creation from transformation matrix."""
        # Create a 45-degree rotation around Z
        angle = math.pi / 4
        matrix = Mat4.rotation_z(angle)
        
        basis = Basis3.from_matrix(matrix)
        
        # X should point at 45 degrees
        expected_x = Vec3(math.cos(angle), math.sin(angle), 0)
        assert abs(basis.x.x - expected_x.x) < 1e-10
        assert abs(basis.x.y - expected_x.y) < 1e-10
        
        # Y should point at 135 degrees
        expected_y = Vec3(-math.sin(angle), math.cos(angle), 0)
        assert abs(basis.y.x - expected_y.x) < 1e-10
        assert abs(basis.y.y - expected_y.y) < 1e-10
        
        # Z should remain unchanged
        assert basis.z == Vec3(0, 0, 1)
    
    def test_transform_vector(self):
        """Test transforming vectors between coordinate systems."""
        # Create a rotated basis (90 degrees around Z)
        basis = Basis3(
            Vec3(0, 1, 0),   # X points in Y direction
            Vec3(-1, 0, 0),  # Y points in -X direction
            Vec3(0, 0, 1)    # Z unchanged
        )
        
        # Transform a vector
        local_vec = Vec3(1, 0, 0)  # X in local space
        world_vec = basis.transform_to_world(local_vec)
        
        # Should point in Y direction in world space
        assert world_vec == Vec3(0, 1, 0)
        
        # Transform back
        local_vec2 = basis.transform_to_local(world_vec)
        assert local_vec2 == local_vec
    
    def test_rotation_between_bases(self):
        """Test finding rotation between two bases."""
        basis1 = Basis3.identity()
        
        # 90-degree rotation around Z
        basis2 = Basis3(
            Vec3(0, 1, 0),
            Vec3(-1, 0, 0),
            Vec3(0, 0, 1)
        )
        
        rotation = basis1.rotation_to(basis2)
        
        # Apply rotation to basis1 vectors
        rotated_x = rotation.transform_vector(basis1.x)
        rotated_y = rotation.transform_vector(basis1.y)
        rotated_z = rotation.transform_vector(basis1.z)
        
        # Should match basis2
        assert (rotated_x - basis2.x).length() < 1e-10
        assert (rotated_y - basis2.y).length() < 1e-10
        assert (rotated_z - basis2.z).length() < 1e-10
    
    def test_look_at(self):
        """Test creating basis that looks at a target."""
        position = Vec3(0, 0, 0)
        target = Vec3(10, 0, 0)
        up = Vec3(0, 0, 1)
        
        basis = Basis3.look_at(position, target, up)
        
        # Z should point from position to target (forward)
        expected_z = Vec3(1, 0, 0)  # Normalized direction to target
        assert (basis.z - expected_z).length() < 1e-10
        
        # Y should be close to up direction
        assert Vec3.dot(basis.y, up) > 0.9
        
        # X should be right vector
        assert abs(Vec3.dot(basis.x, basis.y)) < 1e-10
        assert abs(Vec3.dot(basis.x, basis.z)) < 1e-10
    
    def test_euler_angles(self):
        """Test conversion to/from Euler angles."""
        # Create basis from known Euler angles
        # Using ZYX rotation order (yaw, pitch, roll)
        yaw = math.pi / 6    # 30 degrees
        pitch = math.pi / 4  # 45 degrees
        roll = math.pi / 3   # 60 degrees
        
        basis = Basis3.from_euler(yaw, pitch, roll)
        
        # Convert back to Euler angles
        yaw2, pitch2, roll2 = basis.to_euler()
        
        # Should match (within gimbal lock constraints)
        assert abs(yaw2 - yaw) < 1e-10
        assert abs(pitch2 - pitch) < 1e-10
        assert abs(roll2 - roll) < 1e-10
    
    def test_align_vectors(self):
        """Test creating basis that aligns two vectors."""
        from_vec = Vec3(1, 0, 0)
        to_vec = Vec3(0, 1, 0)
        
        basis = Basis3.align(from_vec, to_vec)
        
        # Transforming from_vec should give to_vec
        transformed = basis.transform_to_world(from_vec)
        assert (transformed - to_vec).length() < 1e-10
    
    def test_is_orthonormal(self):
        """Test checking if basis is orthonormal."""
        # Identity is orthonormal
        assert Basis3.identity().is_orthonormal() == True
        
        # Non-orthogonal basis
        non_ortho = Basis3(
            Vec3(1, 0.1, 0),
            Vec3(0, 1, 0),
            Vec3(0, 0, 1)
        )
        assert non_ortho.is_orthonormal() == False
        
        # Non-unit vectors
        non_unit = Basis3(
            Vec3(2, 0, 0),
            Vec3(0, 1, 0),
            Vec3(0, 0, 1)
        )
        assert non_unit.is_orthonormal() == False
    
    def test_determinant(self):
        """Test basis determinant (handedness)."""
        # Right-handed basis
        right_handed = Basis3.identity()
        assert right_handed.determinant() == pytest.approx(1.0)
        
        # Left-handed basis (flip one axis)
        left_handed = Basis3(
            Vec3(-1, 0, 0),
            Vec3(0, 1, 0),
            Vec3(0, 0, 1)
        )
        assert left_handed.determinant() == pytest.approx(-1.0)
    
    def test_slerp(self):
        """Test spherical linear interpolation between bases."""
        basis1 = Basis3.identity()
        basis2 = Basis3.from_euler(0, 0, math.pi / 2)  # 90-degree rotation
        
        # Interpolate halfway
        mid_basis = Basis3.slerp(basis1, basis2, 0.5)
        
        # Should be 45-degree rotation
        angle = basis1.angle_to(mid_basis)
        assert angle == pytest.approx(math.pi / 4)
    
    def test_string_representation(self):
        """Test string representation."""
        basis = Basis3.identity()
        s = str(basis)
        assert "x=" in s
        assert "y=" in s
        assert "z=" in s


if __name__ == "__main__":
    pytest.main([__file__, "-v"])