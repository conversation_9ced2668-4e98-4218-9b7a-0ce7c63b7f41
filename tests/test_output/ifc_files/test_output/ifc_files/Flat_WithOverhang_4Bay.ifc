ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [CoordinationView]'),'2;1');
FILE_NAME('carport.ifc','2025-06-30T18:01:30',('PyModel'),('BREP IFC Generator'),'IFC2X3','PyModel','');
FILE_SCHEMA(('IFC2X3'));
ENDSEC;
DATA;
#1= IFCPERSON($,$,'User',$,$,$,$,$);
#2= IFCORGANIZATION($,'PyModel',$,$,$);
#3= IFCAPPLICATION(#2,'1.0','BREP IFC Generator','PyModel');
#4= IFCOWNERHISTORY(#1,#3,.READWRITE.,$,$,$,$,1234567890);
#5= IFCSIUNIT(*,.LENGTHUNIT.,.MILLI.,.METRE.);
#6= IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#7= IFCUNITASSIGNMENT((#5,#6));
#8= IFCCARTESIANPOINT((0,0,0));
#9= IFCDIRECTION((1.,0.,0.));
#10= IFCDIRECTION((0.,0.,1.));
#11= IFCAXIS2PLACEMENT3D(#8,#10,#9);
#12= IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,1e-05,#11,$);
#13= IFCPROJECT('d5849dce-53f9-4efe-b7b3-aaddf075a3b3',#4,'Carport Project',$,$,$,$,(#12),#7);
#14= IFCSITE('3d5584dc-9ea5-4073-993d-1fcd8083518b',#4,'Site',$,$,$,$,$,.ELEMENT.,$,$,$,$,$);
#15= IFCBUILDING('45e3acd0-59f4-4a42-b344-7a80f87b544c',#4,'Carport',$,$,$,$,$,.ELEMENT.,$,$,$);
#16= IFCBUILDINGSTOREY('c6adf72b-c1be-4276-a20d-ba87b9f64453',#4,'Ground Floor',$,$,$,$,$,.ELEMENT.,0.0);
#17= IFCRELAGGREGATES('6d621fe8-94cc-4fba-b91a-3d51efec8e04',#4,$,$,#13,(#14));
#18= IFCRELAGGREGATES('e3be22c1-19a6-41b6-bb14-ce561ec7e700',#4,$,$,#14,(#15));
#19= IFCRELAGGREGATES('6586e408-95a4-42d1-92c8-ce541de3e872',#4,$,$,#15,(#16));
#20= IFCMATERIAL('Steel');
#21= IFCCARTESIANPOINT((-37.5,-37.5,0));
#22= IFCCARTESIANPOINT((37.5,-37.5,0));
#23= IFCCARTESIANPOINT((37.5,37.5,0));
#24= IFCCARTESIANPOINT((-37.5,37.5,0));
#25= IFCCARTESIANPOINT((-37.5,-37.5,2401.117124155406));
#26= IFCCARTESIANPOINT((37.5,-37.5,2401.117124155406));
#27= IFCCARTESIANPOINT((37.5,37.5,2401.117124155406));
#28= IFCCARTESIANPOINT((-37.5,37.5,2401.117124155406));
#29= IFCPOLYLOOP((#21,#22,#23,#24));
#30= IFCFACEOUTERBOUND(#29,.T.);
#31= IFCCARTESIANPOINT((-35.0,-35.0,0));
#32= IFCCARTESIANPOINT((-35.0,35.0,0));
#33= IFCCARTESIANPOINT((35.0,35.0,0));
#34= IFCCARTESIANPOINT((35.0,-35.0,0));
#35= IFCPOLYLOOP((#31,#32,#33,#34));
#36= IFCFACEBOUND(#35,.F.);
#37= IFCFACE((#30,#36));
#38= IFCCLOSEDSHELL((#37));
#39= IFCFACETEDBREP(#38);
#40= IFCCARTESIANPOINT((101.5,101.5,0));
#41= IFCAXIS2PLACEMENT3D(#40,$,$);
#42= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#39));
#43= IFCPRODUCTDEFINITIONSHAPE($,$,(#42));
#44= IFCCOLUMN('2cdda222-b113-43f7-b531-e687b27acb1b',#4,'Column_L1',$,$,#41,#43,$,$);
#45= IFCCARTESIANPOINT((-37.5,-37.5,0));
#46= IFCCARTESIANPOINT((37.5,-37.5,0));
#47= IFCCARTESIANPOINT((37.5,37.5,0));
#48= IFCCARTESIANPOINT((-37.5,37.5,0));
#49= IFCCARTESIANPOINT((-37.5,-37.5,2401.117124155406));
#50= IFCCARTESIANPOINT((37.5,-37.5,2401.117124155406));
#51= IFCCARTESIANPOINT((37.5,37.5,2401.117124155406));
#52= IFCCARTESIANPOINT((-37.5,37.5,2401.117124155406));
#53= IFCPOLYLOOP((#45,#46,#47,#48));
#54= IFCFACEOUTERBOUND(#53,.T.);
#55= IFCCARTESIANPOINT((-35.0,-35.0,0));
#56= IFCCARTESIANPOINT((-35.0,35.0,0));
#57= IFCCARTESIANPOINT((35.0,35.0,0));
#58= IFCCARTESIANPOINT((35.0,-35.0,0));
#59= IFCPOLYLOOP((#55,#56,#57,#58));
#60= IFCFACEBOUND(#59,.F.);
#61= IFCFACE((#54,#60));
#62= IFCCLOSEDSHELL((#61));
#63= IFCFACETEDBREP(#62);
#64= IFCCARTESIANPOINT((101.5,3069.5,0));
#65= IFCAXIS2PLACEMENT3D(#64,$,$);
#66= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#63));
#67= IFCPRODUCTDEFINITIONSHAPE($,$,(#66));
#68= IFCCOLUMN('7ddc7556-bbfa-4d14-886b-1f494b1e6d85',#4,'Column_L2',$,$,#65,#67,$,$);
#69= IFCCARTESIANPOINT((-37.5,-37.5,0));
#70= IFCCARTESIANPOINT((37.5,-37.5,0));
#71= IFCCARTESIANPOINT((37.5,37.5,0));
#72= IFCCARTESIANPOINT((-37.5,37.5,0));
#73= IFCCARTESIANPOINT((-37.5,-37.5,2401.117124155406));
#74= IFCCARTESIANPOINT((37.5,-37.5,2401.117124155406));
#75= IFCCARTESIANPOINT((37.5,37.5,2401.117124155406));
#76= IFCCARTESIANPOINT((-37.5,37.5,2401.117124155406));
#77= IFCPOLYLOOP((#69,#70,#71,#72));
#78= IFCFACEOUTERBOUND(#77,.T.);
#79= IFCCARTESIANPOINT((-35.0,-35.0,0));
#80= IFCCARTESIANPOINT((-35.0,35.0,0));
#81= IFCCARTESIANPOINT((35.0,35.0,0));
#82= IFCCARTESIANPOINT((35.0,-35.0,0));
#83= IFCPOLYLOOP((#79,#80,#81,#82));
#84= IFCFACEBOUND(#83,.F.);
#85= IFCFACE((#78,#84));
#86= IFCCLOSEDSHELL((#85));
#87= IFCFACETEDBREP(#86);
#88= IFCCARTESIANPOINT((101.5,6069.5,0));
#89= IFCAXIS2PLACEMENT3D(#88,$,$);
#90= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#87));
#91= IFCPRODUCTDEFINITIONSHAPE($,$,(#90));
#92= IFCCOLUMN('725b6dfc-29af-4c89-93a4-51f5fdaca360',#4,'Column_L3',$,$,#89,#91,$,$);
#93= IFCCARTESIANPOINT((-37.5,-37.5,0));
#94= IFCCARTESIANPOINT((37.5,-37.5,0));
#95= IFCCARTESIANPOINT((37.5,37.5,0));
#96= IFCCARTESIANPOINT((-37.5,37.5,0));
#97= IFCCARTESIANPOINT((-37.5,-37.5,2401.117124155406));
#98= IFCCARTESIANPOINT((37.5,-37.5,2401.117124155406));
#99= IFCCARTESIANPOINT((37.5,37.5,2401.117124155406));
#100= IFCCARTESIANPOINT((-37.5,37.5,2401.117124155406));
#101= IFCPOLYLOOP((#93,#94,#95,#96));
#102= IFCFACEOUTERBOUND(#101,.T.);
#103= IFCCARTESIANPOINT((-35.0,-35.0,0));
#104= IFCCARTESIANPOINT((-35.0,35.0,0));
#105= IFCCARTESIANPOINT((35.0,35.0,0));
#106= IFCCARTESIANPOINT((35.0,-35.0,0));
#107= IFCPOLYLOOP((#103,#104,#105,#106));
#108= IFCFACEBOUND(#107,.F.);
#109= IFCFACE((#102,#108));
#110= IFCCLOSEDSHELL((#109));
#111= IFCFACETEDBREP(#110);
#112= IFCCARTESIANPOINT((101.5,9069.5,0));
#113= IFCAXIS2PLACEMENT3D(#112,$,$);
#114= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#111));
#115= IFCPRODUCTDEFINITIONSHAPE($,$,(#114));
#116= IFCCOLUMN('dedfd412-e641-4a79-b74c-bbf82623fc38',#4,'Column_L4',$,$,#113,#115,$,$);
#117= IFCCARTESIANPOINT((-37.5,-37.5,0));
#118= IFCCARTESIANPOINT((37.5,-37.5,0));
#119= IFCCARTESIANPOINT((37.5,37.5,0));
#120= IFCCARTESIANPOINT((-37.5,37.5,0));
#121= IFCCARTESIANPOINT((-37.5,-37.5,2401.117124155406));
#122= IFCCARTESIANPOINT((37.5,-37.5,2401.117124155406));
#123= IFCCARTESIANPOINT((37.5,37.5,2401.117124155406));
#124= IFCCARTESIANPOINT((-37.5,37.5,2401.117124155406));
#125= IFCPOLYLOOP((#117,#118,#119,#120));
#126= IFCFACEOUTERBOUND(#125,.T.);
#127= IFCCARTESIANPOINT((-35.0,-35.0,0));
#128= IFCCARTESIANPOINT((-35.0,35.0,0));
#129= IFCCARTESIANPOINT((35.0,35.0,0));
#130= IFCCARTESIANPOINT((35.0,-35.0,0));
#131= IFCPOLYLOOP((#127,#128,#129,#130));
#132= IFCFACEBOUND(#131,.F.);
#133= IFCFACE((#126,#132));
#134= IFCCLOSEDSHELL((#133));
#135= IFCFACETEDBREP(#134);
#136= IFCCARTESIANPOINT((101.5,11898.5,0));
#137= IFCAXIS2PLACEMENT3D(#136,$,$);
#138= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#135));
#139= IFCPRODUCTDEFINITIONSHAPE($,$,(#138));
#140= IFCCOLUMN('bc591ba9-ffde-485a-a447-7fc6f006cc30',#4,'Column_L5',$,$,#137,#139,$,$);
#141= IFCCARTESIANPOINT((-37.5,-37.5,0));
#142= IFCCARTESIANPOINT((37.5,-37.5,0));
#143= IFCCARTESIANPOINT((37.5,37.5,0));
#144= IFCCARTESIANPOINT((-37.5,37.5,0));
#145= IFCCARTESIANPOINT((-37.5,-37.5,2487.083318926878));
#146= IFCCARTESIANPOINT((37.5,-37.5,2487.083318926878));
#147= IFCCARTESIANPOINT((37.5,37.5,2487.083318926878));
#148= IFCCARTESIANPOINT((-37.5,37.5,2487.083318926878));
#149= IFCPOLYLOOP((#141,#142,#143,#144));
#150= IFCFACEOUTERBOUND(#149,.T.);
#151= IFCCARTESIANPOINT((-35.0,-35.0,0));
#152= IFCCARTESIANPOINT((-35.0,35.0,0));
#153= IFCCARTESIANPOINT((35.0,35.0,0));
#154= IFCCARTESIANPOINT((35.0,-35.0,0));
#155= IFCPOLYLOOP((#151,#152,#153,#154));
#156= IFCFACEBOUND(#155,.F.);
#157= IFCFACE((#150,#156));
#158= IFCCLOSEDSHELL((#157));
#159= IFCFACETEDBREP(#158);
#160= IFCCARTESIANPOINT((4962.5,101.5,0));
#161= IFCAXIS2PLACEMENT3D(#160,$,$);
#162= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#159));
#163= IFCPRODUCTDEFINITIONSHAPE($,$,(#162));
#164= IFCCOLUMN('d44721ce-c7d3-4f61-86e0-d051cd272188',#4,'Column_R1',$,$,#161,#163,$,$);
#165= IFCCARTESIANPOINT((-37.5,-37.5,0));
#166= IFCCARTESIANPOINT((37.5,-37.5,0));
#167= IFCCARTESIANPOINT((37.5,37.5,0));
#168= IFCCARTESIANPOINT((-37.5,37.5,0));
#169= IFCCARTESIANPOINT((-37.5,-37.5,2487.083318926878));
#170= IFCCARTESIANPOINT((37.5,-37.5,2487.083318926878));
#171= IFCCARTESIANPOINT((37.5,37.5,2487.083318926878));
#172= IFCCARTESIANPOINT((-37.5,37.5,2487.083318926878));
#173= IFCPOLYLOOP((#165,#166,#167,#168));
#174= IFCFACEOUTERBOUND(#173,.T.);
#175= IFCCARTESIANPOINT((-35.0,-35.0,0));
#176= IFCCARTESIANPOINT((-35.0,35.0,0));
#177= IFCCARTESIANPOINT((35.0,35.0,0));
#178= IFCCARTESIANPOINT((35.0,-35.0,0));
#179= IFCPOLYLOOP((#175,#176,#177,#178));
#180= IFCFACEBOUND(#179,.F.);
#181= IFCFACE((#174,#180));
#182= IFCCLOSEDSHELL((#181));
#183= IFCFACETEDBREP(#182);
#184= IFCCARTESIANPOINT((4962.5,3069.5,0));
#185= IFCAXIS2PLACEMENT3D(#184,$,$);
#186= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#183));
#187= IFCPRODUCTDEFINITIONSHAPE($,$,(#186));
#188= IFCCOLUMN('b246f60f-7924-40cc-b0d5-53c942c4611b',#4,'Column_R2',$,$,#185,#187,$,$);
#189= IFCCARTESIANPOINT((-37.5,-37.5,0));
#190= IFCCARTESIANPOINT((37.5,-37.5,0));
#191= IFCCARTESIANPOINT((37.5,37.5,0));
#192= IFCCARTESIANPOINT((-37.5,37.5,0));
#193= IFCCARTESIANPOINT((-37.5,-37.5,2487.083318926878));
#194= IFCCARTESIANPOINT((37.5,-37.5,2487.083318926878));
#195= IFCCARTESIANPOINT((37.5,37.5,2487.083318926878));
#196= IFCCARTESIANPOINT((-37.5,37.5,2487.083318926878));
#197= IFCPOLYLOOP((#189,#190,#191,#192));
#198= IFCFACEOUTERBOUND(#197,.T.);
#199= IFCCARTESIANPOINT((-35.0,-35.0,0));
#200= IFCCARTESIANPOINT((-35.0,35.0,0));
#201= IFCCARTESIANPOINT((35.0,35.0,0));
#202= IFCCARTESIANPOINT((35.0,-35.0,0));
#203= IFCPOLYLOOP((#199,#200,#201,#202));
#204= IFCFACEBOUND(#203,.F.);
#205= IFCFACE((#198,#204));
#206= IFCCLOSEDSHELL((#205));
#207= IFCFACETEDBREP(#206);
#208= IFCCARTESIANPOINT((4962.5,6069.5,0));
#209= IFCAXIS2PLACEMENT3D(#208,$,$);
#210= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#207));
#211= IFCPRODUCTDEFINITIONSHAPE($,$,(#210));
#212= IFCCOLUMN('f91f93ec-b090-4cf8-a0d9-32ac36222ac7',#4,'Column_R3',$,$,#209,#211,$,$);
#213= IFCCARTESIANPOINT((-37.5,-37.5,0));
#214= IFCCARTESIANPOINT((37.5,-37.5,0));
#215= IFCCARTESIANPOINT((37.5,37.5,0));
#216= IFCCARTESIANPOINT((-37.5,37.5,0));
#217= IFCCARTESIANPOINT((-37.5,-37.5,2487.083318926878));
#218= IFCCARTESIANPOINT((37.5,-37.5,2487.083318926878));
#219= IFCCARTESIANPOINT((37.5,37.5,2487.083318926878));
#220= IFCCARTESIANPOINT((-37.5,37.5,2487.083318926878));
#221= IFCPOLYLOOP((#213,#214,#215,#216));
#222= IFCFACEOUTERBOUND(#221,.T.);
#223= IFCCARTESIANPOINT((-35.0,-35.0,0));
#224= IFCCARTESIANPOINT((-35.0,35.0,0));
#225= IFCCARTESIANPOINT((35.0,35.0,0));
#226= IFCCARTESIANPOINT((35.0,-35.0,0));
#227= IFCPOLYLOOP((#223,#224,#225,#226));
#228= IFCFACEBOUND(#227,.F.);
#229= IFCFACE((#222,#228));
#230= IFCCLOSEDSHELL((#229));
#231= IFCFACETEDBREP(#230);
#232= IFCCARTESIANPOINT((4962.5,9069.5,0));
#233= IFCAXIS2PLACEMENT3D(#232,$,$);
#234= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#231));
#235= IFCPRODUCTDEFINITIONSHAPE($,$,(#234));
#236= IFCCOLUMN('7d8ae6e0-06b7-455b-acf8-1bedd14a29a6',#4,'Column_R4',$,$,#233,#235,$,$);
#237= IFCCARTESIANPOINT((-37.5,-37.5,0));
#238= IFCCARTESIANPOINT((37.5,-37.5,0));
#239= IFCCARTESIANPOINT((37.5,37.5,0));
#240= IFCCARTESIANPOINT((-37.5,37.5,0));
#241= IFCCARTESIANPOINT((-37.5,-37.5,2487.083318926878));
#242= IFCCARTESIANPOINT((37.5,-37.5,2487.083318926878));
#243= IFCCARTESIANPOINT((37.5,37.5,2487.083318926878));
#244= IFCCARTESIANPOINT((-37.5,37.5,2487.083318926878));
#245= IFCPOLYLOOP((#237,#238,#239,#240));
#246= IFCFACEOUTERBOUND(#245,.T.);
#247= IFCCARTESIANPOINT((-35.0,-35.0,0));
#248= IFCCARTESIANPOINT((-35.0,35.0,0));
#249= IFCCARTESIANPOINT((35.0,35.0,0));
#250= IFCCARTESIANPOINT((35.0,-35.0,0));
#251= IFCPOLYLOOP((#247,#248,#249,#250));
#252= IFCFACEBOUND(#251,.F.);
#253= IFCFACE((#246,#252));
#254= IFCCLOSEDSHELL((#253));
#255= IFCFACETEDBREP(#254);
#256= IFCCARTESIANPOINT((4962.5,11898.5,0));
#257= IFCAXIS2PLACEMENT3D(#256,$,$);
#258= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#255));
#259= IFCPRODUCTDEFINITIONSHAPE($,$,(#258));
#260= IFCCOLUMN('9b08059e-0b89-4f88-90ce-6c8e6c5fe7df',#4,'Column_R5',$,$,#257,#259,$,$);
#261= IFCCARTESIANPOINT((0,0,0));
#262= IFCCARTESIANPOINT((64,0,0));
#263= IFCCARTESIANPOINT((64,152,0));
#264= IFCCARTESIANPOINT((0,152,0));
#265= IFCCARTESIANPOINT((0,0,4861.760091432267));
#266= IFCCARTESIANPOINT((64,0,4861.760091432267));
#267= IFCCARTESIANPOINT((64,152,4861.760091432267));
#268= IFCCARTESIANPOINT((0,152,4861.760091432267));
#269= IFCPOLYLOOP((#261,#262,#263,#264));
#270= IFCFACEOUTERBOUND(#269,.T.);
#271= IFCFACE((#270));
#272= IFCPOLYLOOP((#265,#268,#267,#266));
#273= IFCFACEOUTERBOUND(#272,.T.);
#274= IFCFACE((#273));
#275= IFCPOLYLOOP((#261,#265,#266,#262));
#276= IFCFACEOUTERBOUND(#275,.T.);
#277= IFCFACE((#276));
#278= IFCPOLYLOOP((#263,#267,#268,#264));
#279= IFCFACEOUTERBOUND(#278,.T.);
#280= IFCFACE((#279));
#281= IFCPOLYLOOP((#261,#264,#268,#265));
#282= IFCFACEOUTERBOUND(#281,.T.);
#283= IFCFACE((#282));
#284= IFCPOLYLOOP((#262,#266,#267,#263));
#285= IFCFACEOUTERBOUND(#284,.T.);
#286= IFCFACE((#285));
#287= IFCCLOSEDSHELL((#271,#274,#277,#280,#283,#286));
#288= IFCFACETEDBREP(#287);
#289= IFCCARTESIANPOINT((101.5,101.5,2401.117124155406));
#290= IFCDIRECTION((0.9998436592061368,0.0,0.01768211371082817));
#291= IFCAXIS2PLACEMENT3D(#289,#290,$);
#292= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#288));
#293= IFCPRODUCTDEFINITIONSHAPE($,$,(#292));
#294= IFCMEMBER('cc296716-8826-4030-8a2c-a4f77dad2002',#4,'Rafter_L1',$,$,#291,#293,$,$);
#295= IFCCARTESIANPOINT((0,0,0));
#296= IFCCARTESIANPOINT((64,0,0));
#297= IFCCARTESIANPOINT((64,152,0));
#298= IFCCARTESIANPOINT((0,152,0));
#299= IFCCARTESIANPOINT((0,0,4861.760091432267));
#300= IFCCARTESIANPOINT((64,0,4861.760091432267));
#301= IFCCARTESIANPOINT((64,152,4861.760091432267));
#302= IFCCARTESIANPOINT((0,152,4861.760091432267));
#303= IFCPOLYLOOP((#295,#296,#297,#298));
#304= IFCFACEOUTERBOUND(#303,.T.);
#305= IFCFACE((#304));
#306= IFCPOLYLOOP((#299,#302,#301,#300));
#307= IFCFACEOUTERBOUND(#306,.T.);
#308= IFCFACE((#307));
#309= IFCPOLYLOOP((#295,#299,#300,#296));
#310= IFCFACEOUTERBOUND(#309,.T.);
#311= IFCFACE((#310));
#312= IFCPOLYLOOP((#297,#301,#302,#298));
#313= IFCFACEOUTERBOUND(#312,.T.);
#314= IFCFACE((#313));
#315= IFCPOLYLOOP((#295,#298,#302,#299));
#316= IFCFACEOUTERBOUND(#315,.T.);
#317= IFCFACE((#316));
#318= IFCPOLYLOOP((#296,#300,#301,#297));
#319= IFCFACEOUTERBOUND(#318,.T.);
#320= IFCFACE((#319));
#321= IFCCLOSEDSHELL((#305,#308,#311,#314,#317,#320));
#322= IFCFACETEDBREP(#321);
#323= IFCCARTESIANPOINT((101.5,3069.5,2401.117124155406));
#324= IFCDIRECTION((0.9998436592061368,0.0,0.01768211371082817));
#325= IFCAXIS2PLACEMENT3D(#323,#324,$);
#326= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#322));
#327= IFCPRODUCTDEFINITIONSHAPE($,$,(#326));
#328= IFCMEMBER('d467b0e5-8680-4cd9-9dcd-472ae072cb6e',#4,'Rafter_L2',$,$,#325,#327,$,$);
#329= IFCCARTESIANPOINT((0,0,0));
#330= IFCCARTESIANPOINT((64,0,0));
#331= IFCCARTESIANPOINT((64,152,0));
#332= IFCCARTESIANPOINT((0,152,0));
#333= IFCCARTESIANPOINT((0,0,4861.760091432267));
#334= IFCCARTESIANPOINT((64,0,4861.760091432267));
#335= IFCCARTESIANPOINT((64,152,4861.760091432267));
#336= IFCCARTESIANPOINT((0,152,4861.760091432267));
#337= IFCPOLYLOOP((#329,#330,#331,#332));
#338= IFCFACEOUTERBOUND(#337,.T.);
#339= IFCFACE((#338));
#340= IFCPOLYLOOP((#333,#336,#335,#334));
#341= IFCFACEOUTERBOUND(#340,.T.);
#342= IFCFACE((#341));
#343= IFCPOLYLOOP((#329,#333,#334,#330));
#344= IFCFACEOUTERBOUND(#343,.T.);
#345= IFCFACE((#344));
#346= IFCPOLYLOOP((#331,#335,#336,#332));
#347= IFCFACEOUTERBOUND(#346,.T.);
#348= IFCFACE((#347));
#349= IFCPOLYLOOP((#329,#332,#336,#333));
#350= IFCFACEOUTERBOUND(#349,.T.);
#351= IFCFACE((#350));
#352= IFCPOLYLOOP((#330,#334,#335,#331));
#353= IFCFACEOUTERBOUND(#352,.T.);
#354= IFCFACE((#353));
#355= IFCCLOSEDSHELL((#339,#342,#345,#348,#351,#354));
#356= IFCFACETEDBREP(#355);
#357= IFCCARTESIANPOINT((101.5,6069.5,2401.117124155406));
#358= IFCDIRECTION((0.9998436592061368,0.0,0.01768211371082817));
#359= IFCAXIS2PLACEMENT3D(#357,#358,$);
#360= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#356));
#361= IFCPRODUCTDEFINITIONSHAPE($,$,(#360));
#362= IFCMEMBER('0a53c369-f231-4842-af37-85bb526e1379',#4,'Rafter_L3',$,$,#359,#361,$,$);
#363= IFCCARTESIANPOINT((0,0,0));
#364= IFCCARTESIANPOINT((64,0,0));
#365= IFCCARTESIANPOINT((64,152,0));
#366= IFCCARTESIANPOINT((0,152,0));
#367= IFCCARTESIANPOINT((0,0,4861.760091432267));
#368= IFCCARTESIANPOINT((64,0,4861.760091432267));
#369= IFCCARTESIANPOINT((64,152,4861.760091432267));
#370= IFCCARTESIANPOINT((0,152,4861.760091432267));
#371= IFCPOLYLOOP((#363,#364,#365,#366));
#372= IFCFACEOUTERBOUND(#371,.T.);
#373= IFCFACE((#372));
#374= IFCPOLYLOOP((#367,#370,#369,#368));
#375= IFCFACEOUTERBOUND(#374,.T.);
#376= IFCFACE((#375));
#377= IFCPOLYLOOP((#363,#367,#368,#364));
#378= IFCFACEOUTERBOUND(#377,.T.);
#379= IFCFACE((#378));
#380= IFCPOLYLOOP((#365,#369,#370,#366));
#381= IFCFACEOUTERBOUND(#380,.T.);
#382= IFCFACE((#381));
#383= IFCPOLYLOOP((#363,#366,#370,#367));
#384= IFCFACEOUTERBOUND(#383,.T.);
#385= IFCFACE((#384));
#386= IFCPOLYLOOP((#364,#368,#369,#365));
#387= IFCFACEOUTERBOUND(#386,.T.);
#388= IFCFACE((#387));
#389= IFCCLOSEDSHELL((#373,#376,#379,#382,#385,#388));
#390= IFCFACETEDBREP(#389);
#391= IFCCARTESIANPOINT((101.5,9069.5,2401.117124155406));
#392= IFCDIRECTION((0.9998436592061368,0.0,0.01768211371082817));
#393= IFCAXIS2PLACEMENT3D(#391,#392,$);
#394= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#390));
#395= IFCPRODUCTDEFINITIONSHAPE($,$,(#394));
#396= IFCMEMBER('6c6ec742-4e4b-4606-88a4-b435a15845be',#4,'Rafter_L4',$,$,#393,#395,$,$);
#397= IFCCARTESIANPOINT((0,0,0));
#398= IFCCARTESIANPOINT((64,0,0));
#399= IFCCARTESIANPOINT((64,152,0));
#400= IFCCARTESIANPOINT((0,152,0));
#401= IFCCARTESIANPOINT((0,0,4861.760091432267));
#402= IFCCARTESIANPOINT((64,0,4861.760091432267));
#403= IFCCARTESIANPOINT((64,152,4861.760091432267));
#404= IFCCARTESIANPOINT((0,152,4861.760091432267));
#405= IFCPOLYLOOP((#397,#398,#399,#400));
#406= IFCFACEOUTERBOUND(#405,.T.);
#407= IFCFACE((#406));
#408= IFCPOLYLOOP((#401,#404,#403,#402));
#409= IFCFACEOUTERBOUND(#408,.T.);
#410= IFCFACE((#409));
#411= IFCPOLYLOOP((#397,#401,#402,#398));
#412= IFCFACEOUTERBOUND(#411,.T.);
#413= IFCFACE((#412));
#414= IFCPOLYLOOP((#399,#403,#404,#400));
#415= IFCFACEOUTERBOUND(#414,.T.);
#416= IFCFACE((#415));
#417= IFCPOLYLOOP((#397,#400,#404,#401));
#418= IFCFACEOUTERBOUND(#417,.T.);
#419= IFCFACE((#418));
#420= IFCPOLYLOOP((#398,#402,#403,#399));
#421= IFCFACEOUTERBOUND(#420,.T.);
#422= IFCFACE((#421));
#423= IFCCLOSEDSHELL((#407,#410,#413,#416,#419,#422));
#424= IFCFACETEDBREP(#423);
#425= IFCCARTESIANPOINT((101.5,11898.5,2401.117124155406));
#426= IFCDIRECTION((0.9998436592061368,0.0,0.01768211371082817));
#427= IFCAXIS2PLACEMENT3D(#425,#426,$);
#428= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#424));
#429= IFCPRODUCTDEFINITIONSHAPE($,$,(#428));
#430= IFCMEMBER('11f9f722-7bb6-4b9c-8a08-9f5d9e93e063',#4,'Rafter_L5',$,$,#427,#429,$,$);
#431= IFCCARTESIANPOINT((0,0,0));
#432= IFCCARTESIANPOINT((300,0,0));
#433= IFCCARTESIANPOINT((300,300,0));
#434= IFCCARTESIANPOINT((0,300,0));
#435= IFCCARTESIANPOINT((0,0,300));
#436= IFCCARTESIANPOINT((300,0,300));
#437= IFCCARTESIANPOINT((300,300,300));
#438= IFCCARTESIANPOINT((0,300,300));
#439= IFCPOLYLOOP((#431,#432,#433,#434));
#440= IFCFACEOUTERBOUND(#439,.T.);
#441= IFCFACE((#440));
#442= IFCPOLYLOOP((#435,#438,#437,#436));
#443= IFCFACEOUTERBOUND(#442,.T.);
#444= IFCFACE((#443));
#445= IFCPOLYLOOP((#431,#435,#436,#432));
#446= IFCFACEOUTERBOUND(#445,.T.);
#447= IFCFACE((#446));
#448= IFCPOLYLOOP((#433,#437,#438,#434));
#449= IFCFACEOUTERBOUND(#448,.T.);
#450= IFCFACE((#449));
#451= IFCPOLYLOOP((#431,#434,#438,#435));
#452= IFCFACEOUTERBOUND(#451,.T.);
#453= IFCFACE((#452));
#454= IFCPOLYLOOP((#432,#436,#437,#433));
#455= IFCFACEOUTERBOUND(#454,.T.);
#456= IFCFACE((#455));
#457= IFCCLOSEDSHELL((#441,#444,#447,#450,#453,#456));
#458= IFCFACETEDBREP(#457);
#459= IFCCARTESIANPOINT((-48.5,-48.5,-300));
#460= IFCAXIS2PLACEMENT3D(#459,$,$);
#461= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#458));
#462= IFCPRODUCTDEFINITIONSHAPE($,$,(#461));
#463= IFCFOOTING('29c7a084-c7fd-4eda-96de-3287db7238b4',#4,'Footing_L1',$,$,#460,#462,$,.FOOTING_BEAM.);
#464= IFCCARTESIANPOINT((0,0,0));
#465= IFCCARTESIANPOINT((300,0,0));
#466= IFCCARTESIANPOINT((300,300,0));
#467= IFCCARTESIANPOINT((0,300,0));
#468= IFCCARTESIANPOINT((0,0,300));
#469= IFCCARTESIANPOINT((300,0,300));
#470= IFCCARTESIANPOINT((300,300,300));
#471= IFCCARTESIANPOINT((0,300,300));
#472= IFCPOLYLOOP((#464,#465,#466,#467));
#473= IFCFACEOUTERBOUND(#472,.T.);
#474= IFCFACE((#473));
#475= IFCPOLYLOOP((#468,#471,#470,#469));
#476= IFCFACEOUTERBOUND(#475,.T.);
#477= IFCFACE((#476));
#478= IFCPOLYLOOP((#464,#468,#469,#465));
#479= IFCFACEOUTERBOUND(#478,.T.);
#480= IFCFACE((#479));
#481= IFCPOLYLOOP((#466,#470,#471,#467));
#482= IFCFACEOUTERBOUND(#481,.T.);
#483= IFCFACE((#482));
#484= IFCPOLYLOOP((#464,#467,#471,#468));
#485= IFCFACEOUTERBOUND(#484,.T.);
#486= IFCFACE((#485));
#487= IFCPOLYLOOP((#465,#469,#470,#466));
#488= IFCFACEOUTERBOUND(#487,.T.);
#489= IFCFACE((#488));
#490= IFCCLOSEDSHELL((#474,#477,#480,#483,#486,#489));
#491= IFCFACETEDBREP(#490);
#492= IFCCARTESIANPOINT((-48.5,2919.5,-300));
#493= IFCAXIS2PLACEMENT3D(#492,$,$);
#494= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#491));
#495= IFCPRODUCTDEFINITIONSHAPE($,$,(#494));
#496= IFCFOOTING('87dbf8a5-2c63-41cb-bca2-4063c308179f',#4,'Footing_L2',$,$,#493,#495,$,.FOOTING_BEAM.);
#497= IFCCARTESIANPOINT((0,0,0));
#498= IFCCARTESIANPOINT((300,0,0));
#499= IFCCARTESIANPOINT((300,300,0));
#500= IFCCARTESIANPOINT((0,300,0));
#501= IFCCARTESIANPOINT((0,0,300));
#502= IFCCARTESIANPOINT((300,0,300));
#503= IFCCARTESIANPOINT((300,300,300));
#504= IFCCARTESIANPOINT((0,300,300));
#505= IFCPOLYLOOP((#497,#498,#499,#500));
#506= IFCFACEOUTERBOUND(#505,.T.);
#507= IFCFACE((#506));
#508= IFCPOLYLOOP((#501,#504,#503,#502));
#509= IFCFACEOUTERBOUND(#508,.T.);
#510= IFCFACE((#509));
#511= IFCPOLYLOOP((#497,#501,#502,#498));
#512= IFCFACEOUTERBOUND(#511,.T.);
#513= IFCFACE((#512));
#514= IFCPOLYLOOP((#499,#503,#504,#500));
#515= IFCFACEOUTERBOUND(#514,.T.);
#516= IFCFACE((#515));
#517= IFCPOLYLOOP((#497,#500,#504,#501));
#518= IFCFACEOUTERBOUND(#517,.T.);
#519= IFCFACE((#518));
#520= IFCPOLYLOOP((#498,#502,#503,#499));
#521= IFCFACEOUTERBOUND(#520,.T.);
#522= IFCFACE((#521));
#523= IFCCLOSEDSHELL((#507,#510,#513,#516,#519,#522));
#524= IFCFACETEDBREP(#523);
#525= IFCCARTESIANPOINT((-48.5,5919.5,-300));
#526= IFCAXIS2PLACEMENT3D(#525,$,$);
#527= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#524));
#528= IFCPRODUCTDEFINITIONSHAPE($,$,(#527));
#529= IFCFOOTING('dce5c3d1-4c54-49f2-a112-3a54159f993f',#4,'Footing_L3',$,$,#526,#528,$,.FOOTING_BEAM.);
#530= IFCCARTESIANPOINT((0,0,0));
#531= IFCCARTESIANPOINT((300,0,0));
#532= IFCCARTESIANPOINT((300,300,0));
#533= IFCCARTESIANPOINT((0,300,0));
#534= IFCCARTESIANPOINT((0,0,300));
#535= IFCCARTESIANPOINT((300,0,300));
#536= IFCCARTESIANPOINT((300,300,300));
#537= IFCCARTESIANPOINT((0,300,300));
#538= IFCPOLYLOOP((#530,#531,#532,#533));
#539= IFCFACEOUTERBOUND(#538,.T.);
#540= IFCFACE((#539));
#541= IFCPOLYLOOP((#534,#537,#536,#535));
#542= IFCFACEOUTERBOUND(#541,.T.);
#543= IFCFACE((#542));
#544= IFCPOLYLOOP((#530,#534,#535,#531));
#545= IFCFACEOUTERBOUND(#544,.T.);
#546= IFCFACE((#545));
#547= IFCPOLYLOOP((#532,#536,#537,#533));
#548= IFCFACEOUTERBOUND(#547,.T.);
#549= IFCFACE((#548));
#550= IFCPOLYLOOP((#530,#533,#537,#534));
#551= IFCFACEOUTERBOUND(#550,.T.);
#552= IFCFACE((#551));
#553= IFCPOLYLOOP((#531,#535,#536,#532));
#554= IFCFACEOUTERBOUND(#553,.T.);
#555= IFCFACE((#554));
#556= IFCCLOSEDSHELL((#540,#543,#546,#549,#552,#555));
#557= IFCFACETEDBREP(#556);
#558= IFCCARTESIANPOINT((-48.5,8919.5,-300));
#559= IFCAXIS2PLACEMENT3D(#558,$,$);
#560= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#557));
#561= IFCPRODUCTDEFINITIONSHAPE($,$,(#560));
#562= IFCFOOTING('2ce81e19-7fdd-48e1-8c98-791e413bdbff',#4,'Footing_L4',$,$,#559,#561,$,.FOOTING_BEAM.);
#563= IFCCARTESIANPOINT((0,0,0));
#564= IFCCARTESIANPOINT((300,0,0));
#565= IFCCARTESIANPOINT((300,300,0));
#566= IFCCARTESIANPOINT((0,300,0));
#567= IFCCARTESIANPOINT((0,0,300));
#568= IFCCARTESIANPOINT((300,0,300));
#569= IFCCARTESIANPOINT((300,300,300));
#570= IFCCARTESIANPOINT((0,300,300));
#571= IFCPOLYLOOP((#563,#564,#565,#566));
#572= IFCFACEOUTERBOUND(#571,.T.);
#573= IFCFACE((#572));
#574= IFCPOLYLOOP((#567,#570,#569,#568));
#575= IFCFACEOUTERBOUND(#574,.T.);
#576= IFCFACE((#575));
#577= IFCPOLYLOOP((#563,#567,#568,#564));
#578= IFCFACEOUTERBOUND(#577,.T.);
#579= IFCFACE((#578));
#580= IFCPOLYLOOP((#565,#569,#570,#566));
#581= IFCFACEOUTERBOUND(#580,.T.);
#582= IFCFACE((#581));
#583= IFCPOLYLOOP((#563,#566,#570,#567));
#584= IFCFACEOUTERBOUND(#583,.T.);
#585= IFCFACE((#584));
#586= IFCPOLYLOOP((#564,#568,#569,#565));
#587= IFCFACEOUTERBOUND(#586,.T.);
#588= IFCFACE((#587));
#589= IFCCLOSEDSHELL((#573,#576,#579,#582,#585,#588));
#590= IFCFACETEDBREP(#589);
#591= IFCCARTESIANPOINT((-48.5,11748.5,-300));
#592= IFCAXIS2PLACEMENT3D(#591,$,$);
#593= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#590));
#594= IFCPRODUCTDEFINITIONSHAPE($,$,(#593));
#595= IFCFOOTING('a6a6f6a3-b266-48af-84c7-faedcf9c9ab6',#4,'Footing_L5',$,$,#592,#594,$,.FOOTING_BEAM.);
#596= IFCRELCONTAINEDINSPATIALSTRUCTURE('5a58f2ae-de14-401a-8239-12f0700bf575',#4,$,$,(#44,#68,#92,#116,#140,#164,#188,#212,#236,#260,#294,#328,#362,#396,#430,#463,#496,#529,#562,#595),#16);
#597= IFCRELASSOCIATESMATERIAL('28eaaae7-0c91-4841-82c1-22ef6382d050',#4,$,$,(#44,#68,#92,#116,#140,#164,#188,#212,#236,#260),#20);
#598= IFCRELASSOCIATESMATERIAL('1eb95867-8351-4107-961d-6221ce02e670',#4,$,$,(#294,#328,#362,#396,#430),#20);
ENDSEC;
END-ISO-10303-21;