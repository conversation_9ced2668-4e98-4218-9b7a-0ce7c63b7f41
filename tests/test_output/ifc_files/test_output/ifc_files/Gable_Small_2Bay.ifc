ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [CoordinationView]'),'2;1');
FILE_NAME('carport.ifc','2025-06-30T18:01:30',('PyModel'),('BREP IFC Generator'),'IFC2X3','PyModel','');
FILE_SCHEMA(('IFC2X3'));
ENDSEC;
DATA;
#1= IFCPERSON($,$,'User',$,$,$,$,$);
#2= IFCORGANIZATION($,'PyModel',$,$,$);
#3= IFCAPPLICATION(#2,'1.0','BREP IFC Generator','PyModel');
#4= IFCOWNERHISTORY(#1,#3,.READWRITE.,$,$,$,$,1234567890);
#5= IFCSIUNIT(*,.LENGTHUNIT.,.MILLI.,.METRE.);
#6= IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#7= IFCUNITASSIGNMENT((#5,#6));
#8= IFCCARTESIANPOINT((0,0,0));
#9= IFCDIRECTION((1.,0.,0.));
#10= IFCDIRECTION((0.,0.,1.));
#11= IFCAXIS2PLACEMENT3D(#8,#10,#9);
#12= IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,1e-05,#11,$);
#13= IFCPROJECT('2e696ab5-b07d-4f66-abe9-6cbbe3097314',#4,'Carport Project',$,$,$,$,(#12),#7);
#14= IFCSITE('6c50eb15-925f-4ead-b65f-687ffbb21cf4',#4,'Site',$,$,$,$,$,.ELEMENT.,$,$,$,$,$);
#15= IFCBUILDING('cf40411b-36df-4a9f-b951-537a9ca612c8',#4,'Carport',$,$,$,$,$,.ELEMENT.,$,$,$);
#16= IFCBUILDINGSTOREY('4aec1fcf-108e-48da-a2c4-971866537c19',#4,'Ground Floor',$,$,$,$,$,.ELEMENT.,0.0);
#17= IFCRELAGGREGATES('2aa0d044-fc0b-4798-a24d-c6e66bef3752',#4,$,$,#13,(#14));
#18= IFCRELAGGREGATES('9398198f-8818-4470-b49d-d696ca38f4b3',#4,$,$,#14,(#15));
#19= IFCRELAGGREGATES('c2e134a9-1cda-4d8b-a7bf-65e5fb51c1a9',#4,$,$,#15,(#16));
#20= IFCMATERIAL('Steel');
#21= IFCCARTESIANPOINT((-37.5,-37.5,0));
#22= IFCCARTESIANPOINT((37.5,-37.5,0));
#23= IFCCARTESIANPOINT((37.5,37.5,0));
#24= IFCCARTESIANPOINT((-37.5,37.5,0));
#25= IFCCARTESIANPOINT((-37.5,-37.5,2411.284926765342));
#26= IFCCARTESIANPOINT((37.5,-37.5,2411.284926765342));
#27= IFCCARTESIANPOINT((37.5,37.5,2411.284926765342));
#28= IFCCARTESIANPOINT((-37.5,37.5,2411.284926765342));
#29= IFCPOLYLOOP((#21,#22,#23,#24));
#30= IFCFACEOUTERBOUND(#29,.T.);
#31= IFCCARTESIANPOINT((-35.0,-35.0,0));
#32= IFCCARTESIANPOINT((-35.0,35.0,0));
#33= IFCCARTESIANPOINT((35.0,35.0,0));
#34= IFCCARTESIANPOINT((35.0,-35.0,0));
#35= IFCPOLYLOOP((#31,#32,#33,#34));
#36= IFCFACEBOUND(#35,.F.);
#37= IFCFACE((#30,#36));
#38= IFCCLOSEDSHELL((#37));
#39= IFCFACETEDBREP(#38);
#40= IFCCARTESIANPOINT((101.5,37.5,0));
#41= IFCAXIS2PLACEMENT3D(#40,$,$);
#42= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#39));
#43= IFCPRODUCTDEFINITIONSHAPE($,$,(#42));
#44= IFCCOLUMN('53abc418-dd65-4bb9-9008-82e9eaed9d84',#4,'Column_L1',$,$,#41,#43,$,$);
#45= IFCCARTESIANPOINT((-37.5,-37.5,0));
#46= IFCCARTESIANPOINT((37.5,-37.5,0));
#47= IFCCARTESIANPOINT((37.5,37.5,0));
#48= IFCCARTESIANPOINT((-37.5,37.5,0));
#49= IFCCARTESIANPOINT((-37.5,-37.5,2411.284926765342));
#50= IFCCARTESIANPOINT((37.5,-37.5,2411.284926765342));
#51= IFCCARTESIANPOINT((37.5,37.5,2411.284926765342));
#52= IFCCARTESIANPOINT((-37.5,37.5,2411.284926765342));
#53= IFCPOLYLOOP((#45,#46,#47,#48));
#54= IFCFACEOUTERBOUND(#53,.T.);
#55= IFCCARTESIANPOINT((-35.0,-35.0,0));
#56= IFCCARTESIANPOINT((-35.0,35.0,0));
#57= IFCCARTESIANPOINT((35.0,35.0,0));
#58= IFCCARTESIANPOINT((35.0,-35.0,0));
#59= IFCPOLYLOOP((#55,#56,#57,#58));
#60= IFCFACEBOUND(#59,.F.);
#61= IFCFACE((#54,#60));
#62= IFCCLOSEDSHELL((#61));
#63= IFCFACETEDBREP(#62);
#64= IFCCARTESIANPOINT((101.5,3000.0,0));
#65= IFCAXIS2PLACEMENT3D(#64,$,$);
#66= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#63));
#67= IFCPRODUCTDEFINITIONSHAPE($,$,(#66));
#68= IFCCOLUMN('8efcafd7-a0c1-4d47-bf7f-37365958621e',#4,'Column_L2',$,$,#65,#67,$,$);
#69= IFCCARTESIANPOINT((-37.5,-37.5,0));
#70= IFCCARTESIANPOINT((37.5,-37.5,0));
#71= IFCCARTESIANPOINT((37.5,37.5,0));
#72= IFCCARTESIANPOINT((-37.5,37.5,0));
#73= IFCCARTESIANPOINT((-37.5,-37.5,2411.284926765342));
#74= IFCCARTESIANPOINT((37.5,-37.5,2411.284926765342));
#75= IFCCARTESIANPOINT((37.5,37.5,2411.284926765342));
#76= IFCCARTESIANPOINT((-37.5,37.5,2411.284926765342));
#77= IFCPOLYLOOP((#69,#70,#71,#72));
#78= IFCFACEOUTERBOUND(#77,.T.);
#79= IFCCARTESIANPOINT((-35.0,-35.0,0));
#80= IFCCARTESIANPOINT((-35.0,35.0,0));
#81= IFCCARTESIANPOINT((35.0,35.0,0));
#82= IFCCARTESIANPOINT((35.0,-35.0,0));
#83= IFCPOLYLOOP((#79,#80,#81,#82));
#84= IFCFACEBOUND(#83,.F.);
#85= IFCFACE((#78,#84));
#86= IFCCLOSEDSHELL((#85));
#87= IFCFACETEDBREP(#86);
#88= IFCCARTESIANPOINT((101.5,5962.5,0));
#89= IFCAXIS2PLACEMENT3D(#88,$,$);
#90= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#87));
#91= IFCPRODUCTDEFINITIONSHAPE($,$,(#90));
#92= IFCCOLUMN('40281498-95eb-4b8a-928d-2ae398b59c69',#4,'Column_L3',$,$,#89,#91,$,$);
#93= IFCCARTESIANPOINT((-37.5,-37.5,0));
#94= IFCCARTESIANPOINT((37.5,-37.5,0));
#95= IFCCARTESIANPOINT((37.5,37.5,0));
#96= IFCCARTESIANPOINT((-37.5,37.5,0));
#97= IFCCARTESIANPOINT((-37.5,-37.5,2411.284926765342));
#98= IFCCARTESIANPOINT((37.5,-37.5,2411.284926765342));
#99= IFCCARTESIANPOINT((37.5,37.5,2411.284926765342));
#100= IFCCARTESIANPOINT((-37.5,37.5,2411.284926765342));
#101= IFCPOLYLOOP((#93,#94,#95,#96));
#102= IFCFACEOUTERBOUND(#101,.T.);
#103= IFCCARTESIANPOINT((-35.0,-35.0,0));
#104= IFCCARTESIANPOINT((-35.0,35.0,0));
#105= IFCCARTESIANPOINT((35.0,35.0,0));
#106= IFCCARTESIANPOINT((35.0,-35.0,0));
#107= IFCPOLYLOOP((#103,#104,#105,#106));
#108= IFCFACEBOUND(#107,.F.);
#109= IFCFACE((#102,#108));
#110= IFCCLOSEDSHELL((#109));
#111= IFCFACETEDBREP(#110);
#112= IFCCARTESIANPOINT((4462.5,37.5,0));
#113= IFCAXIS2PLACEMENT3D(#112,$,$);
#114= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#111));
#115= IFCPRODUCTDEFINITIONSHAPE($,$,(#114));
#116= IFCCOLUMN('a1b8b6f1-cf31-4f6d-8100-cefbce365470',#4,'Column_R1',$,$,#113,#115,$,$);
#117= IFCCARTESIANPOINT((-37.5,-37.5,0));
#118= IFCCARTESIANPOINT((37.5,-37.5,0));
#119= IFCCARTESIANPOINT((37.5,37.5,0));
#120= IFCCARTESIANPOINT((-37.5,37.5,0));
#121= IFCCARTESIANPOINT((-37.5,-37.5,2411.284926765342));
#122= IFCCARTESIANPOINT((37.5,-37.5,2411.284926765342));
#123= IFCCARTESIANPOINT((37.5,37.5,2411.284926765342));
#124= IFCCARTESIANPOINT((-37.5,37.5,2411.284926765342));
#125= IFCPOLYLOOP((#117,#118,#119,#120));
#126= IFCFACEOUTERBOUND(#125,.T.);
#127= IFCCARTESIANPOINT((-35.0,-35.0,0));
#128= IFCCARTESIANPOINT((-35.0,35.0,0));
#129= IFCCARTESIANPOINT((35.0,35.0,0));
#130= IFCCARTESIANPOINT((35.0,-35.0,0));
#131= IFCPOLYLOOP((#127,#128,#129,#130));
#132= IFCFACEBOUND(#131,.F.);
#133= IFCFACE((#126,#132));
#134= IFCCLOSEDSHELL((#133));
#135= IFCFACETEDBREP(#134);
#136= IFCCARTESIANPOINT((4462.5,3000.0,0));
#137= IFCAXIS2PLACEMENT3D(#136,$,$);
#138= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#135));
#139= IFCPRODUCTDEFINITIONSHAPE($,$,(#138));
#140= IFCCOLUMN('497040ca-a6d1-4c16-ba13-0788e1de5035',#4,'Column_R2',$,$,#137,#139,$,$);
#141= IFCCARTESIANPOINT((-37.5,-37.5,0));
#142= IFCCARTESIANPOINT((37.5,-37.5,0));
#143= IFCCARTESIANPOINT((37.5,37.5,0));
#144= IFCCARTESIANPOINT((-37.5,37.5,0));
#145= IFCCARTESIANPOINT((-37.5,-37.5,2411.284926765342));
#146= IFCCARTESIANPOINT((37.5,-37.5,2411.284926765342));
#147= IFCCARTESIANPOINT((37.5,37.5,2411.284926765342));
#148= IFCCARTESIANPOINT((-37.5,37.5,2411.284926765342));
#149= IFCPOLYLOOP((#141,#142,#143,#144));
#150= IFCFACEOUTERBOUND(#149,.T.);
#151= IFCCARTESIANPOINT((-35.0,-35.0,0));
#152= IFCCARTESIANPOINT((-35.0,35.0,0));
#153= IFCCARTESIANPOINT((35.0,35.0,0));
#154= IFCCARTESIANPOINT((35.0,-35.0,0));
#155= IFCPOLYLOOP((#151,#152,#153,#154));
#156= IFCFACEBOUND(#155,.F.);
#157= IFCFACE((#150,#156));
#158= IFCCLOSEDSHELL((#157));
#159= IFCFACETEDBREP(#158);
#160= IFCCARTESIANPOINT((4462.5,5962.5,0));
#161= IFCAXIS2PLACEMENT3D(#160,$,$);
#162= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#159));
#163= IFCPRODUCTDEFINITIONSHAPE($,$,(#162));
#164= IFCCOLUMN('da5b53eb-2782-493f-85b9-0f35312e30dd',#4,'Column_R3',$,$,#161,#163,$,$);
#165= IFCCARTESIANPOINT((0,0,0));
#166= IFCCARTESIANPOINT((64,0,0));
#167= IFCCARTESIANPOINT((64,152,0));
#168= IFCCARTESIANPOINT((0,152,0));
#169= IFCCARTESIANPOINT((0,0,2182.80199598373));
#170= IFCCARTESIANPOINT((64,0,2182.80199598373));
#171= IFCCARTESIANPOINT((64,152,2182.80199598373));
#172= IFCCARTESIANPOINT((0,152,2182.80199598373));
#173= IFCPOLYLOOP((#165,#166,#167,#168));
#174= IFCFACEOUTERBOUND(#173,.T.);
#175= IFCFACE((#174));
#176= IFCPOLYLOOP((#169,#172,#171,#170));
#177= IFCFACEOUTERBOUND(#176,.T.);
#178= IFCFACE((#177));
#179= IFCPOLYLOOP((#165,#169,#170,#166));
#180= IFCFACEOUTERBOUND(#179,.T.);
#181= IFCFACE((#180));
#182= IFCPOLYLOOP((#167,#171,#172,#168));
#183= IFCFACEOUTERBOUND(#182,.T.);
#184= IFCFACE((#183));
#185= IFCPOLYLOOP((#165,#168,#172,#169));
#186= IFCFACEOUTERBOUND(#185,.T.);
#187= IFCFACE((#186));
#188= IFCPOLYLOOP((#166,#170,#171,#167));
#189= IFCFACEOUTERBOUND(#188,.T.);
#190= IFCFACE((#189));
#191= IFCCLOSEDSHELL((#175,#178,#181,#184,#187,#190));
#192= IFCFACETEDBREP(#191);
#193= IFCCARTESIANPOINT((101.5,37.5,2411.284926765342));
#194= IFCDIRECTION((0.9842853378149532,0.0,0.17658531581788836));
#195= IFCAXIS2PLACEMENT3D(#193,#194,$);
#196= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#192));
#197= IFCPRODUCTDEFINITIONSHAPE($,$,(#196));
#198= IFCMEMBER('6f84e30e-96c3-41b4-9d6f-0063a72bef6b',#4,'Rafter_L1',$,$,#195,#197,$,$);
#199= IFCCARTESIANPOINT((0,0,0));
#200= IFCCARTESIANPOINT((64,0,0));
#201= IFCCARTESIANPOINT((64,152,0));
#202= IFCCARTESIANPOINT((0,152,0));
#203= IFCCARTESIANPOINT((0,0,2182.80199598373));
#204= IFCCARTESIANPOINT((64,0,2182.80199598373));
#205= IFCCARTESIANPOINT((64,152,2182.80199598373));
#206= IFCCARTESIANPOINT((0,152,2182.80199598373));
#207= IFCPOLYLOOP((#199,#200,#201,#202));
#208= IFCFACEOUTERBOUND(#207,.T.);
#209= IFCFACE((#208));
#210= IFCPOLYLOOP((#203,#206,#205,#204));
#211= IFCFACEOUTERBOUND(#210,.T.);
#212= IFCFACE((#211));
#213= IFCPOLYLOOP((#199,#203,#204,#200));
#214= IFCFACEOUTERBOUND(#213,.T.);
#215= IFCFACE((#214));
#216= IFCPOLYLOOP((#201,#205,#206,#202));
#217= IFCFACEOUTERBOUND(#216,.T.);
#218= IFCFACE((#217));
#219= IFCPOLYLOOP((#199,#202,#206,#203));
#220= IFCFACEOUTERBOUND(#219,.T.);
#221= IFCFACE((#220));
#222= IFCPOLYLOOP((#200,#204,#205,#201));
#223= IFCFACEOUTERBOUND(#222,.T.);
#224= IFCFACE((#223));
#225= IFCCLOSEDSHELL((#209,#212,#215,#218,#221,#224));
#226= IFCFACETEDBREP(#225);
#227= IFCCARTESIANPOINT((101.5,3000.0,2411.284926765342));
#228= IFCDIRECTION((0.9842853378149532,0.0,0.17658531581788836));
#229= IFCAXIS2PLACEMENT3D(#227,#228,$);
#230= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#226));
#231= IFCPRODUCTDEFINITIONSHAPE($,$,(#230));
#232= IFCMEMBER('9eb3e0a0-dbf8-4b51-b737-ee518eb3b7b3',#4,'Rafter_L2',$,$,#229,#231,$,$);
#233= IFCCARTESIANPOINT((0,0,0));
#234= IFCCARTESIANPOINT((64,0,0));
#235= IFCCARTESIANPOINT((64,152,0));
#236= IFCCARTESIANPOINT((0,152,0));
#237= IFCCARTESIANPOINT((0,0,2182.80199598373));
#238= IFCCARTESIANPOINT((64,0,2182.80199598373));
#239= IFCCARTESIANPOINT((64,152,2182.80199598373));
#240= IFCCARTESIANPOINT((0,152,2182.80199598373));
#241= IFCPOLYLOOP((#233,#234,#235,#236));
#242= IFCFACEOUTERBOUND(#241,.T.);
#243= IFCFACE((#242));
#244= IFCPOLYLOOP((#237,#240,#239,#238));
#245= IFCFACEOUTERBOUND(#244,.T.);
#246= IFCFACE((#245));
#247= IFCPOLYLOOP((#233,#237,#238,#234));
#248= IFCFACEOUTERBOUND(#247,.T.);
#249= IFCFACE((#248));
#250= IFCPOLYLOOP((#235,#239,#240,#236));
#251= IFCFACEOUTERBOUND(#250,.T.);
#252= IFCFACE((#251));
#253= IFCPOLYLOOP((#233,#236,#240,#237));
#254= IFCFACEOUTERBOUND(#253,.T.);
#255= IFCFACE((#254));
#256= IFCPOLYLOOP((#234,#238,#239,#235));
#257= IFCFACEOUTERBOUND(#256,.T.);
#258= IFCFACE((#257));
#259= IFCCLOSEDSHELL((#243,#246,#249,#252,#255,#258));
#260= IFCFACETEDBREP(#259);
#261= IFCCARTESIANPOINT((101.5,5962.5,2411.284926765342));
#262= IFCDIRECTION((0.9842853378149532,0.0,0.17658531581788836));
#263= IFCAXIS2PLACEMENT3D(#261,#262,$);
#264= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#260));
#265= IFCPRODUCTDEFINITIONSHAPE($,$,(#264));
#266= IFCMEMBER('c0121371-559c-473c-8112-f337c20a045a',#4,'Rafter_L3',$,$,#263,#265,$,$);
#267= IFCCARTESIANPOINT((0,0,0));
#268= IFCCARTESIANPOINT((300,0,0));
#269= IFCCARTESIANPOINT((300,300,0));
#270= IFCCARTESIANPOINT((0,300,0));
#271= IFCCARTESIANPOINT((0,0,300));
#272= IFCCARTESIANPOINT((300,0,300));
#273= IFCCARTESIANPOINT((300,300,300));
#274= IFCCARTESIANPOINT((0,300,300));
#275= IFCPOLYLOOP((#267,#268,#269,#270));
#276= IFCFACEOUTERBOUND(#275,.T.);
#277= IFCFACE((#276));
#278= IFCPOLYLOOP((#271,#274,#273,#272));
#279= IFCFACEOUTERBOUND(#278,.T.);
#280= IFCFACE((#279));
#281= IFCPOLYLOOP((#267,#271,#272,#268));
#282= IFCFACEOUTERBOUND(#281,.T.);
#283= IFCFACE((#282));
#284= IFCPOLYLOOP((#269,#273,#274,#270));
#285= IFCFACEOUTERBOUND(#284,.T.);
#286= IFCFACE((#285));
#287= IFCPOLYLOOP((#267,#270,#274,#271));
#288= IFCFACEOUTERBOUND(#287,.T.);
#289= IFCFACE((#288));
#290= IFCPOLYLOOP((#268,#272,#273,#269));
#291= IFCFACEOUTERBOUND(#290,.T.);
#292= IFCFACE((#291));
#293= IFCCLOSEDSHELL((#277,#280,#283,#286,#289,#292));
#294= IFCFACETEDBREP(#293);
#295= IFCCARTESIANPOINT((-48.5,-112.5,-300));
#296= IFCAXIS2PLACEMENT3D(#295,$,$);
#297= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#294));
#298= IFCPRODUCTDEFINITIONSHAPE($,$,(#297));
#299= IFCFOOTING('6f367611-1d7b-490b-b38b-6865ab70a150',#4,'Footing_L1',$,$,#296,#298,$,.FOOTING_BEAM.);
#300= IFCCARTESIANPOINT((0,0,0));
#301= IFCCARTESIANPOINT((300,0,0));
#302= IFCCARTESIANPOINT((300,300,0));
#303= IFCCARTESIANPOINT((0,300,0));
#304= IFCCARTESIANPOINT((0,0,300));
#305= IFCCARTESIANPOINT((300,0,300));
#306= IFCCARTESIANPOINT((300,300,300));
#307= IFCCARTESIANPOINT((0,300,300));
#308= IFCPOLYLOOP((#300,#301,#302,#303));
#309= IFCFACEOUTERBOUND(#308,.T.);
#310= IFCFACE((#309));
#311= IFCPOLYLOOP((#304,#307,#306,#305));
#312= IFCFACEOUTERBOUND(#311,.T.);
#313= IFCFACE((#312));
#314= IFCPOLYLOOP((#300,#304,#305,#301));
#315= IFCFACEOUTERBOUND(#314,.T.);
#316= IFCFACE((#315));
#317= IFCPOLYLOOP((#302,#306,#307,#303));
#318= IFCFACEOUTERBOUND(#317,.T.);
#319= IFCFACE((#318));
#320= IFCPOLYLOOP((#300,#303,#307,#304));
#321= IFCFACEOUTERBOUND(#320,.T.);
#322= IFCFACE((#321));
#323= IFCPOLYLOOP((#301,#305,#306,#302));
#324= IFCFACEOUTERBOUND(#323,.T.);
#325= IFCFACE((#324));
#326= IFCCLOSEDSHELL((#310,#313,#316,#319,#322,#325));
#327= IFCFACETEDBREP(#326);
#328= IFCCARTESIANPOINT((-48.5,2850.0,-300));
#329= IFCAXIS2PLACEMENT3D(#328,$,$);
#330= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#327));
#331= IFCPRODUCTDEFINITIONSHAPE($,$,(#330));
#332= IFCFOOTING('ac80b804-2a5c-40cd-bb04-3388ffb7c344',#4,'Footing_L2',$,$,#329,#331,$,.FOOTING_BEAM.);
#333= IFCCARTESIANPOINT((0,0,0));
#334= IFCCARTESIANPOINT((300,0,0));
#335= IFCCARTESIANPOINT((300,300,0));
#336= IFCCARTESIANPOINT((0,300,0));
#337= IFCCARTESIANPOINT((0,0,300));
#338= IFCCARTESIANPOINT((300,0,300));
#339= IFCCARTESIANPOINT((300,300,300));
#340= IFCCARTESIANPOINT((0,300,300));
#341= IFCPOLYLOOP((#333,#334,#335,#336));
#342= IFCFACEOUTERBOUND(#341,.T.);
#343= IFCFACE((#342));
#344= IFCPOLYLOOP((#337,#340,#339,#338));
#345= IFCFACEOUTERBOUND(#344,.T.);
#346= IFCFACE((#345));
#347= IFCPOLYLOOP((#333,#337,#338,#334));
#348= IFCFACEOUTERBOUND(#347,.T.);
#349= IFCFACE((#348));
#350= IFCPOLYLOOP((#335,#339,#340,#336));
#351= IFCFACEOUTERBOUND(#350,.T.);
#352= IFCFACE((#351));
#353= IFCPOLYLOOP((#333,#336,#340,#337));
#354= IFCFACEOUTERBOUND(#353,.T.);
#355= IFCFACE((#354));
#356= IFCPOLYLOOP((#334,#338,#339,#335));
#357= IFCFACEOUTERBOUND(#356,.T.);
#358= IFCFACE((#357));
#359= IFCCLOSEDSHELL((#343,#346,#349,#352,#355,#358));
#360= IFCFACETEDBREP(#359);
#361= IFCCARTESIANPOINT((-48.5,5812.5,-300));
#362= IFCAXIS2PLACEMENT3D(#361,$,$);
#363= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#360));
#364= IFCPRODUCTDEFINITIONSHAPE($,$,(#363));
#365= IFCFOOTING('f98cf6b8-eff8-49cc-b280-f1bd43ba6f3c',#4,'Footing_L3',$,$,#362,#364,$,.FOOTING_BEAM.);
#366= IFCRELCONTAINEDINSPATIALSTRUCTURE('6c682635-f93b-4341-b459-c2f334dfb69b',#4,$,$,(#44,#68,#92,#116,#140,#164,#198,#232,#266,#299,#332,#365),#16);
#367= IFCRELASSOCIATESMATERIAL('f9cce7a4-4adc-43ae-9e6f-44cb58752367',#4,$,$,(#44,#68,#92,#116,#140,#164),#20);
#368= IFCRELASSOCIATESMATERIAL('f3091de2-a424-4b38-8a54-7b3b96bbacad',#4,$,$,(#198,#232,#266),#20);
ENDSEC;
END-ISO-10303-21;