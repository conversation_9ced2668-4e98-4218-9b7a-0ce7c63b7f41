#!/usr/bin/env python3
"""
Test script to generate 50+ carport IFC files with various configurations.

This script generates a comprehensive test suite of carport IFC files
aligned with the C# BimCoreLibrary implementation.
"""

import sys
from pathlib import Path
from datetime import datetime

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.ifc_generation import CarportIFCGenerator


def main():
    """Generate comprehensive carport test suite."""
    print("=" * 80)
    print("CARPORT IFC TEST GENERATION")
    print("Aligned with C# BimCoreLibrary Implementation")
    print("=" * 80)
    
    # Create output directory
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_dir = Path(__file__).parent.parent.parent / "test_output" / f"carports_{timestamp}"
    
    # Initialize generator
    generator = CarportIFCGenerator(output_dir)
    
    print(f"\nOutput directory: {output_dir}")
    print("\nStarting generation...")
    
    # Generate 50 test carports
    start_time = datetime.now()
    generated_files = generator.generate_test_suite(count=50)
    end_time = datetime.now()
    
    duration = (end_time - start_time).total_seconds()
    
    # Generate report
    report = generator.generate_report(generated_files)
    report += f"\nGeneration Time: {duration:.2f} seconds\n"
    report += f"Average Time per File: {duration/len(generated_files):.2f} seconds\n"
    
    # Save report
    report_path = output_dir / "test_generation_report.txt"
    with open(report_path, 'w') as f:
        f.write(report)
    
    # Print summary
    print("\n" + "=" * 80)
    print("GENERATION COMPLETE")
    print("=" * 80)
    print(f"Total Files Generated: {len(generated_files)}")
    print(f"Output Directory: {output_dir}")
    print(f"Report: {report_path}")
    print(f"Total Time: {duration:.2f} seconds")
    print("\n✅ Test generation successful!")
    
    # Generate a few specific examples with engineering data
    print("\n" + "=" * 80)
    print("GENERATING EXAMPLES WITH SPECIFIC MATERIALS")
    print("=" * 80)
    
    # Import engineering data capabilities
    from src.business.engineering import EngData
    
    # Example 1: Heavy duty carport
    eng_data_heavy = EngData()
    eng_data_heavy.ENG_COLUMN = "SHS10010030"  # 100x100x3.0mm columns
    eng_data_heavy.ENG_RAFTER = "C20319"  # C203x76x1.9mm rafters
    eng_data_heavy.ENG_PURLINSIZE = "C15015"  # C150x64x1.5mm purlins
    eng_data_heavy.ENG_PURLINROW = 5
    eng_data_heavy.ENG_FOOTINGTYPE = "bored"
    eng_data_heavy.ENG_FOOTINGDIA = "450"
    eng_data_heavy.ENG_FOOTINGDEPTH = "600"
    
    heavy_path = generator.generate_with_engineering_data(
        eng_data_heavy,
        building_input=None
    )
    print(f"✅ Generated heavy duty carport: {heavy_path.name}")
    
    # Example 2: Light duty carport  
    eng_data_light = EngData()
    eng_data_light.ENG_COLUMN = "SHS06506525"  # 65x65x2.5mm columns
    eng_data_light.ENG_RAFTER = "C15015"  # C150x64x1.5mm rafters
    eng_data_light.ENG_PURLINSIZE = "TH064100"  # TopHat 64x100mm purlins
    eng_data_light.ENG_PURLINROW = 3
    eng_data_light.ENG_FOOTINGTYPE = "block"
    eng_data_light.ENG_FOOTINGDIA = "300"
    eng_data_light.ENG_FOOTINGDEPTH = "300"
    
    light_path = generator.generate_with_engineering_data(
        eng_data_light,
        building_input=None
    )
    print(f"✅ Generated light duty carport: {light_path.name}")
    
    print("\n🎉 All carport IFC files generated successfully!")


if __name__ == "__main__":
    main()