#!/usr/bin/env python3
"""
Test IFC generation without footings to isolate the issue.
"""

import sys
from pathlib import Path

# Add paths
sys.path.insert(0, str(Path(__file__).parent.parent.parent))  # PyModel

from src.business.building_input import BuildingInput, CarportRoofType
from src.business.structure_builder import CarportBuilder
from src.ifc_generation.ifc_brep_generator import IFCBREPGenerator


class SimplifiedIFCGenerator(IFCBREPGenerator):
    """Simplified generator that skips problematic elements."""
    
    def generate_ifc_content(self, carport, output_path):
        """Generate IFC content with simplified approach."""
        # Reset state
        self.entity_counter = 0
        self.entities = []
        
        # IFC Header
        header = f"""ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [Ifc4NotAssigned]'),'2;1');
FILE_NAME('{output_path.name}','2025-06-30T00:00:00',('PyModel'),('GeometryGym'),'PyModel IFC','PyModel','');
FILE_SCHEMA(('IFC4'));
ENDSEC;
DATA;
"""
        
        # Basic setup entities
        origin = self.add_entity('IFCCARTESIANPOINT', '(0.0,0.0,0.0)')
        axis2_3d_global = self.add_entity('IFCAXIS2PLACEMENT3D', origin, '$', '$')
        self.global_placement_id = self.add_entity('IFCLOCALPLACEMENT', '$', axis2_3d_global)
        
        # Organization and history
        person = self.add_entity('IFCPERSON', '$', "'PyModel'", '$', '$', '$', '$', '$', '$')
        org = self.add_entity('IFCORGANIZATION', '$', "'PyModel'", '$', '$', '$')
        person_org = self.add_entity('IFCPERSONANDORGANIZATION', person, org, '$')
        app = self.add_entity('IFCAPPLICATION', org, "'v1.0.0.0'", "'PyModel'", "'PyModel'")
        self.owner_history_id = self.add_entity('IFCOWNERHISTORY', person_org, app, '$', '.ADDED.', '$', '$', '$', '1750747168')
        
        # Units - MILLIMETERS
        len_unit = self.add_entity('IFCSIUNIT', '*', '.LENGTHUNIT.', '.MILLI.', '.METRE.')
        area_unit = self.add_entity('IFCSIUNIT', '*', '.AREAUNIT.', '$', '.SQUARE_METRE.')
        vol_unit = self.add_entity('IFCSIUNIT', '*', '.VOLUMEUNIT.', '$', '.CUBIC_METRE.')
        angle_unit = self.add_entity('IFCSIUNIT', '*', '.PLANEANGLEUNIT.', '$', '.RADIAN.')
        units = self.add_entity('IFCUNITASSIGNMENT', f"({len_unit},{area_unit},{vol_unit},{angle_unit})")
        
        # Geometric context
        self.context_id = self.add_entity('IFCGEOMETRICREPRESENTATIONCONTEXT', "'3D'", "'Model'", '3', '0.0001', axis2_3d_global, '$')
        self.subcontext_id = self.add_entity('IFCGEOMETRICREPRESENTATIONSUBCONTEXT', "'Body'", "'Model'", '*', '*', '*', '*', self.context_id, '$', '.MODEL_VIEW.', '$')
        
        # Project structure
        import uuid
        project = self.add_entity('IFCPROJECT', f"'{str(uuid.uuid4())}'", self.owner_history_id, "'Test Project'", '$', '$', '$', '$', f"({self.context_id})", units)
        
        site = self.add_entity('IFCSITE', f"'{str(uuid.uuid4())}'", self.owner_history_id, "'Test Site'", '$', '$', self.global_placement_id, '$', '$', '.ELEMENT.', '$', '$', '$', '$', '$')
        
        building = self.add_entity('IFCBUILDING', f"'{str(uuid.uuid4())}'", self.owner_history_id, "'Test Building'", '$', '$', self.global_placement_id, '$', '$', '.ELEMENT.', '$', '$', '$')
        
        # Aggregation relationships
        self.add_entity('IFCRELAGGREGATES', f"'{str(uuid.uuid4())}'", self.owner_history_id, '$', '$', project, f"({site})")
        self.add_entity('IFCRELAGGREGATES', f"'{str(uuid.uuid4())}'", self.owner_history_id, '$', '$', site, f"({building})")
        
        # Material
        steel = self.add_entity('IFCMATERIAL', "'Steel'", '$', '$')
        
        element_ids = []
        
        # Add only columns (skip footings for now)
        if carport.main and carport.main.side_left and carport.main.side_left.columns:
            print(f"  Adding {len(carport.main.side_left.columns)} left columns...")
            for i, col in enumerate(carport.main.side_left.columns):
                if col.column:
                    # Create simple box for column
                    width = 75
                    height = 75
                    length = col.column.end_pos.z - col.column.start_pos.z
                    
                    # Create simple box representation
                    p1 = self.create_point(0, 0, 0)
                    p2 = self.create_point(width, 0, 0)
                    p3 = self.create_point(width, height, 0)
                    p4 = self.create_point(0, height, 0)
                    p5 = self.create_point(0, 0, length)
                    p6 = self.create_point(width, 0, length)
                    p7 = self.create_point(width, height, length)
                    p8 = self.create_point(0, height, length)
                    
                    # Create placement
                    pt = self.create_point(col.column.start_pos.x, col.column.start_pos.y, col.column.start_pos.z)
                    local_axis = self.add_entity('IFCAXIS2PLACEMENT3D', pt, '$', '$')
                    local_pl = self.add_entity('IFCLOCALPLACEMENT', self.global_placement_id, local_axis)
                    
                    # Create simple swept solid instead of BREP
                    profile_pts = self.add_entity('IFCPOLYLINE', f"({p1},{p2},{p3},{p4},{p1})")
                    profile = self.add_entity('IFCARBITRARYCLOSEDPROFILEDEF', '.AREA.', "'Column Profile'", profile_pts)
                    
                    dir_z = self.create_direction(0, 0, 1)
                    extrude = self.add_entity('IFCEXTRUDEDAREASOLID', profile, '$', dir_z, length)
                    
                    shape_rep = self.add_entity('IFCSHAPEREPRESENTATION', self.subcontext_id, "'Body'", "'SweptSolid'", f"({extrude})")
                    prod_shape = self.add_entity('IFCPRODUCTDEFINITIONSHAPE', '$', '$', f"({shape_rep})")
                    
                    member = self.add_entity('IFCMEMBER', f"'{str(uuid.uuid4())}'", self.owner_history_id,
                                           f"'{col.column.tag}'", "'Column'", '$', local_pl, prod_shape, '$', '.COLUMN.')
                    element_ids.append(member)
        
        # Add to building
        if element_ids:
            self.add_entity('IFCRELCONTAINEDINSPATIALSTRUCTURE', f"'{str(uuid.uuid4())}'", 
                           self.owner_history_id, '$', '$', f"({','.join(element_ids)})", building)
        
        # Combine all content
        content = header
        for entity in self.entities:
            content += entity + "\n"
        content += "ENDSEC;\nEND-ISO-10303-21;"
        
        return content


def main():
    print("="*60)
    print("SIMPLIFIED IFC TEST (NO FOOTINGS)")
    print("="*60)
    
    # Create simple carport
    building_input = BuildingInput(
        span=6000,
        length=9000,
        height=2700,
        roof_type=CarportRoofType.GABLE,
        pitch=15,
        bays=3,
        slab=True
    )
    
    print("\nCreating carport structure...")
    carport = CarportBuilder.create_carport(building_input)
    
    print("\nGenerating simplified IFC...")
    generator = SimplifiedIFCGenerator()
    output_path = Path(__file__).parent.parent.parent / "test_output" / "simple_no_footings.ifc"
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    try:
        ifc_content = generator.generate_ifc_content(carport, output_path)
        
        with open(output_path, 'w') as f:
            f.write(ifc_content)
        
        print(f"\n✅ Success! Generated: {output_path}")
        print(f"   File size: {len(ifc_content):,} bytes")
        print(f"   Entities: {ifc_content.count('#')}")
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()