#!/usr/bin/env python3
"""Simple test to verify IFC generation works."""

import sys
from pathlib import Path

# Set up paths FIRST
pymodel_path = Path(__file__).parent.parent.parent
sys.path.insert(0, str(pymodel_path))

# Now import from PyModel
from src.business.building_input import BuildingInput, CarportRoofType
from src.business.structure_builder import CarportBuilder
from src.ifc_generation.ifc_brep_generator import IFCBREPGenerator

def main():
    print("Testing IFC Generation...")
    
    # Create test carport
    building_input = BuildingInput(
        span=6000,
        length=9000,
        height=2700,
        roof_type=CarportRoofType.GABLE,
        pitch=15,
        bays=3,
        slab=True
    )
    
    print("Creating carport structure...")
    carport = CarportBuilder.create_carport(building_input)
    
    print("Generating IFC...")
    generator = IFCBREPGenerator()
    output_path = Path(__file__).parent.parent.parent / "test_output" / "simple_test.ifc"
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    ifc_content = generator.generate_ifc_content(carport, output_path)
    
    with open(output_path, 'w') as f:
        f.write(ifc_content)
    
    print(f"✅ Success! Generated {output_path}")
    print(f"   File size: {len(ifc_content):,} bytes")
    print(f"   Entities: {ifc_content.count('#')}")

if __name__ == "__main__":
    main()