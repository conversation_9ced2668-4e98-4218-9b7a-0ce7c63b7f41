#!/usr/bin/env python3
"""
Simple test to verify carport structure generation works.
"""

import sys
from pathlib import Path

# Add PyModel to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.business.building_input import BuildingInput, CarportRoofType
from src.business.structure_builder import CarportBuilder
import json


def test_carport_creation():
    """Test creating a carport structure."""
    print("="*60)
    print("TESTING CARPORT STRUCTURE CREATION")
    print("="*60)
    
    # Test 1: Gable carport
    print("\n1. Creating GABLE carport...")
    input1 = BuildingInput(
        span=6000,
        length=9000,
        height=2700,
        roof_type=CarportRoofType.GABLE,
        pitch=15,
        bays=3,
        slab=True
    )
    
    carport1 = CarportBuilder.create_carport(input1)
    analyze_carport(carport1, "GABLE")
    
    # Test 2: Flat carport
    print("\n2. Creating FLAT carport...")
    input2 = BuildingInput(
        span=6000,
        length=9000,
        height=2700,
        roof_type=CarportRoofType.FLAT,
        pitch=5,
        bays=3,
        slab=True
    )
    
    carport2 = CarportBuilder.create_carport(input2)
    analyze_carport(carport2, "FLAT")
    
    # Test 3: Carport with overhang
    print("\n3. Creating carport with OVERHANG...")
    input3 = BuildingInput(
        span=6000,
        length=9000,
        height=2700,
        roof_type=CarportRoofType.GABLE,
        pitch=15,
        bays=3,
        overhang=300,
        slab=True
    )
    
    carport3 = CarportBuilder.create_carport(input3)
    analyze_carport(carport3, "GABLE with overhang")
    
    # Save one structure as JSON for inspection
    save_structure_json(carport1, "test_output/gable_structure.json")
    
    print("\n✅ All carport structures created successfully!")


def analyze_carport(carport, type_name):
    """Analyze and print carport structure."""
    print(f"\n{type_name} Carport Analysis:")
    print("-" * 40)
    
    if not carport.main:
        print("❌ No main structure!")
        return
    
    # Slab
    if carport.main.slab and carport.main.slab.middle:
        slab = carport.main.slab.middle
        print(f"✓ Slab: {slab.bottom_left.x:.0f} to {slab.bottom_right.x:.0f} mm")
    else:
        print("✗ No slab")
    
    # Columns
    left_cols = len(carport.main.side_left.columns) if carport.main.side_left else 0
    right_cols = len(carport.main.side_right.columns) if carport.main.side_right else 0
    print(f"✓ Columns: {left_cols} left, {right_cols} right")
    
    if left_cols > 0 and carport.main.side_left.columns[0].column:
        col = carport.main.side_left.columns[0].column
        print(f"  First column: ({col.start_pos.x:.0f}, {col.start_pos.y:.0f}, {col.start_pos.z:.0f}) to ({col.end_pos.x:.0f}, {col.end_pos.y:.0f}, {col.end_pos.z:.0f})")
        print(f"  Material: {col.material.name}")
    
    # Rafters
    if carport.main.roof_left:
        print(f"✓ Left rafters: {len(carport.main.roof_left.rafters)}")
        if carport.main.roof_left.rafters and carport.main.roof_left.rafters[0]:
            raf = carport.main.roof_left.rafters[0]
            print(f"  First rafter: ({raf.start_pos.x:.0f}, {raf.start_pos.y:.0f}, {raf.start_pos.z:.0f}) to ({raf.end_pos.x:.0f}, {raf.end_pos.y:.0f}, {raf.end_pos.z:.0f})")
    
    if carport.main.roof_right:
        print(f"✓ Right rafters: {len(carport.main.roof_right.rafters)}")
    
    # Purlins
    if carport.main.roof_left and carport.main.roof_left.roof_purlin_bays:
        total_purlins = sum(len(bay.roof_purlins) for bay in carport.main.roof_left.roof_purlin_bays)
        print(f"✓ Left purlins: {total_purlins} in {len(carport.main.roof_left.roof_purlin_bays)} bays")
    
    # Eave beams
    if carport.main.side_left and carport.main.side_left.eave_beams:
        print(f"✓ Left eave beams: {len(carport.main.side_left.eave_beams)}")
    
    if carport.main.side_right and carport.main.side_right.eave_beams:
        print(f"✓ Right eave beams: {len(carport.main.side_right.eave_beams)}")


def save_structure_json(carport, filepath):
    """Save carport structure as JSON for inspection."""
    from src.bim.shed_bim import ShedBim
    
    # Convert to ShedBim
    shed_bim = carport.to_shed_bim()
    
    # Create simple dict representation
    data = {
        "job_number": carport.job_number,
        "has_main": carport.main is not None,
        "has_slab": carport.main.slab is not None if carport.main else False,
        "column_count": {
            "left": len(carport.main.side_left.columns) if carport.main and carport.main.side_left else 0,
            "right": len(carport.main.side_right.columns) if carport.main and carport.main.side_right else 0
        },
        "rafter_count": {
            "left": len(carport.main.roof_left.rafters) if carport.main and carport.main.roof_left else 0,
            "right": len(carport.main.roof_right.rafters) if carport.main and carport.main.roof_right else 0
        }
    }
    
    # Save
    Path(filepath).parent.mkdir(parents=True, exist_ok=True)
    with open(filepath, 'w') as f:
        json.dump(data, f, indent=2)
    
    print(f"\n📄 Structure saved to: {filepath}")


if __name__ == "__main__":
    test_carport_creation()