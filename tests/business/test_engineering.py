"""
Test suite for engineering service integration.

Tests engineering data validation and service communication.
"""

import pytest
import httpx
from unittest.mock import AsyncMock, Mo<PERSON>, patch
from src.business.engineering import EngData, EngineeringService, MockEngineeringService
from src.business.building_input import BuildingInput, BuildingType, CarportRoofType


class TestEngData:
    """Test EngData functionality."""
    
    def test_creation_with_valid_data(self):
        """Test creating engineering data with valid inputs."""
        eng_data = EngData(
            ENG_RAFTER="C15024",
            ENG_PURLINSIZE="TH064100",
            ENG_PURLINROW=5,
            ENG_COLUMN="SHS10010030",
            ENG_APEXBRACE="C15015",
            ENG_FOOTINGTYPE="bored",
            ENG_FOOTINGDIA="450",
            ENG_FOOTINGDEPTH="600"
        )
        
        assert eng_data.ENG_RAFTER == "C15024"
        assert eng_data.ENG_PURLINSIZE == "TH064100"
        assert eng_data.ENG_PURLINROW == 5
        assert eng_data.ENG_COLUMN == "SHS10010030"
        assert eng_data.ENG_APEXBRACE == "C15015"
        assert eng_data.ENG_FOOTINGTYPE == "bored"
        assert eng_data.ENG_FOOTINGDIA == "450"
        assert eng_data.ENG_FOOTINGDEPTH == "600"
    
    def test_creation_without_apex_brace(self):
        """Test creating engineering data without optional apex brace."""
        eng_data = EngData(
            ENG_RAFTER="C15024",
            ENG_PURLINSIZE="C15015",
            ENG_PURLINROW=3,
            ENG_COLUMN="SHS07507525",
            ENG_FOOTINGTYPE="block",
            ENG_FOOTINGDIA="300",
            ENG_FOOTINGDEPTH="300"
        )
        
        assert eng_data.ENG_APEXBRACE is None
    
    def test_validation_invalid_footing_diameter(self):
        """Test validation with invalid footing diameter."""
        with pytest.raises(ValueError, match="Invalid footing dimensions"):
            EngData(
                ENG_RAFTER="C15024",
                ENG_PURLINSIZE="TH064100",
                ENG_PURLINROW=5,
                ENG_COLUMN="SHS10010030",
                ENG_FOOTINGTYPE="bored",
                ENG_FOOTINGDIA="invalid",  # Not a number
                ENG_FOOTINGDEPTH="600"
            )
    
    def test_validation_invalid_footing_depth(self):
        """Test validation with invalid footing depth."""
        with pytest.raises(ValueError, match="Invalid footing dimensions"):
            EngData(
                ENG_RAFTER="C15024",
                ENG_PURLINSIZE="TH064100",
                ENG_PURLINROW=5,
                ENG_COLUMN="SHS10010030",
                ENG_FOOTINGTYPE="bored",
                ENG_FOOTINGDIA="450",
                ENG_FOOTINGDEPTH="not_a_number"
            )
    
    def test_validation_invalid_footing_type(self):
        """Test validation with invalid footing type."""
        with pytest.raises(ValueError, match="Invalid footing type"):
            EngData(
                ENG_RAFTER="C15024",
                ENG_PURLINSIZE="TH064100",
                ENG_PURLINROW=5,
                ENG_COLUMN="SHS10010030",
                ENG_FOOTINGTYPE="unknown",  # Not bored or block
                ENG_FOOTINGDIA="450",
                ENG_FOOTINGDEPTH="600"
            )
    
    def test_footing_type_case_insensitive(self):
        """Test footing type validation is case insensitive."""
        # Should accept uppercase
        eng_data1 = EngData(
            ENG_RAFTER="C15024",
            ENG_PURLINSIZE="TH064100",
            ENG_PURLINROW=5,
            ENG_COLUMN="SHS10010030",
            ENG_FOOTINGTYPE="BORED",
            ENG_FOOTINGDIA="450",
            ENG_FOOTINGDEPTH="600"
        )
        assert eng_data1.ENG_FOOTINGTYPE == "BORED"
        
        # Should accept mixed case
        eng_data2 = EngData(
            ENG_RAFTER="C15024",
            ENG_PURLINSIZE="TH064100",
            ENG_PURLINROW=5,
            ENG_COLUMN="SHS10010030",
            ENG_FOOTINGTYPE="Block",
            ENG_FOOTINGDIA="300",
            ENG_FOOTINGDEPTH="300"
        )
        assert eng_data2.ENG_FOOTINGTYPE == "Block"


class TestEngineeringService:
    """Test EngineeringService functionality."""
    
    @pytest.fixture
    def service(self):
        """Create engineering service instance."""
        return EngineeringService("https://api.example.com", "test-api-key", timeout=10.0)
    
    @pytest.fixture
    def building_input(self):
        """Create test building input."""
        return BuildingInput(
            building_type=BuildingType.CARPORT,
            roof_type=CarportRoofType.FLAT,
            span=6000,
            length=9000,
            height=2700,
            bays=3,
            wind_speed=41,
            pitch=3,
            soil="M",
            overhang=600,
            validate_engineering=True
        )
    
    def test_initialization(self, service):
        """Test service initialization."""
        assert service.base_url == "https://api.example.com"
        assert service.api_key == "test-api-key"
        assert service.timeout == 10.0
        assert service._client is None
    
    def test_base_url_trailing_slash_removal(self):
        """Test that trailing slash is removed from base URL."""
        service = EngineeringService("https://api.example.com/", "key")
        assert service.base_url == "https://api.example.com"
    
    @pytest.mark.asyncio
    async def test_context_manager(self, service):
        """Test async context manager functionality."""
        async with service as svc:
            assert svc._client is not None
            assert isinstance(svc._client, httpx.AsyncClient)
        # Client should be closed after context
    
    @pytest.mark.asyncio
    async def test_validate_design_no_validation_required(self, service, building_input):
        """Test validation when not required."""
        building_input.validate_engineering = False
        result = await service.validate_design(building_input)
        assert result is None
    
    @pytest.mark.asyncio
    async def test_validate_design_success(self, service, building_input):
        """Test successful validation."""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "rafter": "C20030",
            "purlinSize": "C15024",
            "purlinRows": 7,
            "column": "SHS15015040",
            "apexBrace": "C15024",
            "footingType": "bored",
            "footingDiameter": 600,
            "footingDepth": 900
        }
        mock_response.raise_for_status = Mock()
        
        with patch.object(httpx.AsyncClient, 'post', return_value=mock_response) as mock_post:
            result = await service.validate_design(building_input)
            
            assert result is not None
            assert result.ENG_RAFTER == "C20030"
            assert result.ENG_PURLINSIZE == "C15024"
            assert result.ENG_PURLINROW == 7
            assert result.ENG_COLUMN == "SHS15015040"
            assert result.ENG_APEXBRACE == "C15024"
            assert result.ENG_FOOTINGTYPE == "bored"
            assert result.ENG_FOOTINGDIA == "600"
            assert result.ENG_FOOTINGDEPTH == "900"
            
            # Verify API call
            mock_post.assert_called_once()
            call_args = mock_post.call_args
            assert call_args[0][0] == "https://api.example.com/api/engineering/validate"
            assert call_args[1]["headers"]["Authorization"] == "Bearer test-api-key"
    
    @pytest.mark.asyncio
    async def test_validate_design_http_error(self, service, building_input):
        """Test validation with HTTP error."""
        mock_response = Mock()
        mock_response.status_code = 500
        mock_response.raise_for_status.side_effect = httpx.HTTPStatusError(
            "Server error", request=Mock(), response=mock_response
        )
        
        with patch.object(httpx.AsyncClient, 'post', return_value=mock_response):
            result = await service.validate_design(building_input)
            assert result is None
    
    @pytest.mark.asyncio
    async def test_validate_design_network_error(self, service, building_input):
        """Test validation with network error."""
        with patch.object(httpx.AsyncClient, 'post', side_effect=httpx.NetworkError("Connection failed")):
            result = await service.validate_design(building_input)
            assert result is None
    
    def test_prepare_request_data(self, service, building_input):
        """Test request data preparation."""
        data = service._prepare_request_data(building_input)
        
        assert data["buildingType"] == "Carport"
        assert data["roofType"] == "Flat"
        assert data["span"] == 6000
        assert data["length"] == 9000
        assert data["height"] == 2700
        assert data["bays"] == 3
        assert data["windSpeed"] == 41
        assert data["pitch"] == 3
        assert data["soil"] == "M"
        assert data["overhang"] == 600
    
    def test_parse_response_success(self, service):
        """Test successful response parsing."""
        data = {
            "rafter": "C15024",
            "purlinSize": "TH064100",
            "purlinRows": 5,
            "column": "SHS10010030",
            "apexBrace": "C15015",
            "footingType": "bored",
            "footingDiameter": 450,
            "footingDepth": 600
        }
        
        result = service._parse_response(data)
        assert result is not None
        assert result.ENG_RAFTER == "C15024"
        assert result.ENG_PURLINROW == 5
        assert result.ENG_FOOTINGDIA == "450"
    
    def test_parse_response_missing_required_field(self, service):
        """Test response parsing with missing required field."""
        data = {
            "rafter": "C15024",
            # Missing purlinSize
            "purlinRows": 5,
            "column": "SHS10010030",
            "footingType": "bored",
            "footingDiameter": 450,
            "footingDepth": 600
        }
        
        result = service._parse_response(data)
        # Should still return result with empty string for missing field
        assert result is not None
        assert result.ENG_PURLINSIZE == ""
    
    def test_parse_response_invalid_numeric_field(self, service):
        """Test response parsing with invalid numeric field."""
        data = {
            "rafter": "C15024",
            "purlinSize": "TH064100",
            "purlinRows": "invalid",  # Should be int
            "column": "SHS10010030",
            "footingType": "bored",
            "footingDiameter": 450,
            "footingDepth": 600
        }
        
        result = service._parse_response(data)
        assert result is None
    
    def test_validate_design_sync(self, service, building_input):
        """Test synchronous wrapper for validate_design."""
        # Mock the async validate_design
        mock_result = EngData(
            ENG_RAFTER="C15024",
            ENG_PURLINSIZE="TH064100",
            ENG_PURLINROW=5,
            ENG_COLUMN="SHS10010030",
            ENG_FOOTINGTYPE="bored",
            ENG_FOOTINGDIA="450",
            ENG_FOOTINGDEPTH="600"
        )
        
        with patch.object(service, 'validate_design', return_value=mock_result) as mock_async:
            with patch('asyncio.get_event_loop') as mock_loop:
                mock_loop.return_value.run_until_complete.return_value = mock_result
                
                result = service.validate_design_sync(building_input)
                assert result == mock_result


class TestMockEngineeringService:
    """Test MockEngineeringService functionality."""
    
    @pytest.fixture
    def service(self):
        """Create mock engineering service instance."""
        return MockEngineeringService()
    
    @pytest.fixture
    def building_input(self):
        """Create test building input."""
        return BuildingInput(
            building_type=BuildingType.CARPORT,
            roof_type=CarportRoofType.FLAT,
            span=6000,
            length=9000,
            height=2700,
            bays=3,
            wind_speed=41,
            pitch=3,
            validate_engineering=True
        )
    
    def test_initialization(self, service):
        """Test mock service initialization."""
        assert service.base_url == "http://mock"
        assert service.api_key == "mock-key"
    
    @pytest.mark.asyncio
    async def test_no_validation_required(self, service, building_input):
        """Test when validation is not required."""
        building_input.validate_engineering = False
        result = await service.validate_design(building_input)
        assert result is None
    
    @pytest.mark.asyncio
    async def test_small_span(self, service, building_input):
        """Test validation for small span."""
        building_input.span = 3000
        result = await service.validate_design(building_input)
        
        assert result is not None
        assert result.ENG_RAFTER == "C15015"
        assert result.ENG_PURLINSIZE == "TH064100"
        assert result.ENG_PURLINROW == 3
        assert result.ENG_COLUMN == "SHS07507525"
        assert result.ENG_FOOTINGTYPE == "block"
        assert result.ENG_FOOTINGDIA == "300"
        assert result.ENG_FOOTINGDEPTH == "300"
        assert result.ENG_APEXBRACE is None  # Flat roof
    
    @pytest.mark.asyncio
    async def test_medium_span(self, service, building_input):
        """Test validation for medium span."""
        building_input.span = 5000
        result = await service.validate_design(building_input)
        
        assert result is not None
        assert result.ENG_RAFTER == "C15024"
        assert result.ENG_PURLINSIZE == "C15015"
        assert result.ENG_PURLINROW == 5
        assert result.ENG_COLUMN == "SHS10010030"
        assert result.ENG_FOOTINGTYPE == "bored"
        assert result.ENG_FOOTINGDIA == "450"
        assert result.ENG_FOOTINGDEPTH == "600"
    
    @pytest.mark.asyncio
    async def test_large_span(self, service, building_input):
        """Test validation for large span."""
        building_input.span = 8000
        result = await service.validate_design(building_input)
        
        assert result is not None
        assert result.ENG_RAFTER == "C20030"
        assert result.ENG_PURLINSIZE == "C15024"
        assert result.ENG_PURLINROW == 7
        assert result.ENG_COLUMN == "SHS15015040"
        assert result.ENG_FOOTINGTYPE == "bored"
        assert result.ENG_FOOTINGDIA == "600"
        assert result.ENG_FOOTINGDEPTH == "900"
    
    @pytest.mark.asyncio
    async def test_gable_roof_has_apex_brace(self, service, building_input):
        """Test that gable roofs get apex brace."""
        building_input.roof_type = CarportRoofType.GABLE
        building_input.span = 6000
        result = await service.validate_design(building_input)
        
        assert result is not None
        assert result.ENG_APEXBRACE == "C15015"
    
    @pytest.mark.asyncio
    async def test_consistency(self, service, building_input):
        """Test that mock returns consistent results."""
        # Same input should give same output
        result1 = await service.validate_design(building_input)
        result2 = await service.validate_design(building_input)
        
        assert result1.ENG_RAFTER == result2.ENG_RAFTER
        assert result1.ENG_COLUMN == result2.ENG_COLUMN
        assert result1.ENG_FOOTINGTYPE == result2.ENG_FOOTINGTYPE


if __name__ == "__main__":
    pytest.main([__file__, "-v"])