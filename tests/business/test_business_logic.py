"""Comprehensive tests for Task 4: Business Logic Layer.

Tests the building input validation, structure builder pattern,
carport builder implementation, and engineering integration.
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

import pytest
from typing import Optional
import math

# Task 4 imports
from src.business import (
    BuildingInput, BuildingType, CarportRoofType,
    CarportBuilder, CarportProduct,
    EngData, MockEngineeringService,
    CarportConfig, CarportHelpers
)

# Supporting imports from previous tasks
from src.geometry import Vec3, Line1
from src.materials import FrameMaterial, FootingMaterial, FootingMaterialType
from src.bim import ShedBim


class TestBuildingInput:
    """Test BuildingInput validation and constraints."""
    
    def test_basic_creation(self):
        """Test creating a basic building input."""
        input_data = BuildingInput(
            building_type=BuildingType.CARPORT,
            name="Test Carport",
            roof_type=CarportRoofType.FLAT,
            bays=2,
            span=6000,
            length=6000,
            height=2400
        )
        
        assert input_data.building_type == BuildingType.CARPORT
        assert input_data.name == "Test Carport"
        assert input_data.bays == 2
        assert input_data.span == 6000
    
    def test_overhang_constraints_flat_roof(self):
        """Test overhang constraints for flat roofs.
        
        C# Ref: BuildingInput.cs lines 20-68
        """
        # Small span (<=3600) - max 600mm overhang
        input_data = BuildingInput(
            roof_type=CarportRoofType.FLAT,
            span=3000
        )
        
        input_data.overhang = 500
        assert input_data.overhang == 500
        
        input_data.overhang = 700
        assert input_data.overhang == 600  # Clamped to max
        
        # Medium span (<=5000) - max 900mm overhang
        input_data.span = 4500
        input_data.overhang = 800
        assert input_data.overhang == 800
        
        input_data.overhang = 1000
        assert input_data.overhang == 900  # Clamped to max
        
        # Large span (>5000) - max 1200mm overhang
        input_data.span = 6000
        input_data.overhang = 1100
        assert input_data.overhang == 1100
        
        input_data.overhang = 1300
        assert input_data.overhang == 1200  # Clamped to max
    
    def test_overhang_non_flat_roof(self):
        """Test that non-flat roofs have zero overhang."""
        # Gable roof
        input_data = BuildingInput(
            roof_type=CarportRoofType.GABLE,
            span=6000
        )
        
        input_data.overhang = 600
        assert input_data.overhang == 0  # Always zero for non-flat
        
        # Awning roof
        input_data.roof_type = CarportRoofType.AWNING
        input_data.overhang = 600
        assert input_data.overhang == 0
    
    def test_validation(self):
        """Test input validation."""
        # Valid input
        input_data = BuildingInput(
            roof_type=CarportRoofType.FLAT,
            bays=2,
            span=6000,
            length=6000,
            height=2400,
            wind_speed=41
        )
        assert input_data.validate() is True
        
        # Invalid span
        input_data.span = 0
        assert input_data.validate() is False
        input_data.span = 6000
        
        # Invalid bays
        input_data.bays = 0
        assert input_data.validate() is False
        input_data.bays = 2
        
        # Gable roof needs pitch
        input_data.roof_type = CarportRoofType.GABLE
        input_data.pitch = 0
        assert input_data.validate() is False
        
        input_data.pitch = 15
        assert input_data.validate() is True
    
    def test_serialization(self):
        """Test converting to dictionary."""
        input_data = BuildingInput(
            name="Test",
            roof_type=CarportRoofType.FLAT,
            span=6000,
            length=6000,
            height=2400
        )
        
        data_dict = input_data.to_dict()
        assert data_dict["name"] == "Test"
        assert data_dict["roof_type"] == "Flat"
        assert data_dict["span"] == 6000


class TestEngineering:
    """Test engineering service integration."""
    
    def test_eng_data_creation(self):
        """Test creating engineering data."""
        eng_data = EngData(
            ENG_RAFTER="C15024",
            ENG_PURLINSIZE="TH064100",
            ENG_PURLINROW=5,
            ENG_COLUMN="SHS10010030",
            ENG_APEXBRACE="C10010",
            ENG_FOOTINGTYPE="bored",
            ENG_FOOTINGDIA="450",
            ENG_FOOTINGDEPTH="600"
        )
        
        assert eng_data.ENG_RAFTER == "C15024"
        assert eng_data.ENG_PURLINROW == 5
        assert eng_data.ENG_FOOTINGTYPE == "bored"
    
    def test_eng_data_validation(self):
        """Test engineering data validation."""
        # Valid data
        eng_data = EngData(
            ENG_RAFTER="C15024",
            ENG_PURLINSIZE="TH064100",
            ENG_PURLINROW=5,
            ENG_COLUMN="SHS10010030",
            ENG_FOOTINGTYPE="block",
            ENG_FOOTINGDIA="300",
            ENG_FOOTINGDEPTH="300"
        )
        # Should not raise exception
        
        # Invalid footing dimensions
        with pytest.raises(ValueError):
            EngData(
                ENG_RAFTER="C15024",
                ENG_PURLINSIZE="TH064100",
                ENG_PURLINROW=5,
                ENG_COLUMN="SHS10010030",
                ENG_FOOTINGTYPE="block",
                ENG_FOOTINGDIA="invalid",
                ENG_FOOTINGDEPTH="300"
            )
    
    @pytest.mark.asyncio
    async def test_mock_engineering_service(self):
        """Test mock engineering service."""
        service = MockEngineeringService()
        
        # Small span
        input_data = BuildingInput(
            roof_type=CarportRoofType.FLAT,
            span=3000,
            length=6000,
            height=2400,
            validate_engineering=True
        )
        
        eng_data = await service.validate_design(input_data)
        assert eng_data is not None
        assert eng_data.ENG_RAFTER == "C15015"
        assert eng_data.ENG_FOOTINGTYPE == "block"
        
        # Large span
        input_data.span = 8000
        eng_data = await service.validate_design(input_data)
        assert eng_data.ENG_RAFTER == "C20030"
        assert eng_data.ENG_FOOTINGTYPE == "bored"
        
        # No validation requested
        input_data.validate_engineering = False
        eng_data = await service.validate_design(input_data)
        assert eng_data is None


class TestCarportHelpers:
    """Test carport helper functions."""
    
    def test_calculate_bay_positions(self):
        """Test bay position calculation."""
        positions = CarportHelpers.calculate_bay_positions(9000, 3)
        
        assert len(positions) == 4  # 3 bays = 4 frames
        assert positions[0] == 0
        assert positions[1] == 3000
        assert positions[2] == 6000
        assert positions[3] == 9000
    
    def test_calculate_purlin_positions_flat(self):
        """Test purlin positions for flat roof."""
        positions = CarportHelpers.calculate_purlin_positions(6000, 5, "Flat")
        
        assert len(positions) == 5
        assert positions[0] == pytest.approx(1000)
        assert positions[2] == pytest.approx(3000)  # Middle
        assert positions[4] == pytest.approx(5000)
    
    def test_calculate_purlin_positions_gable(self):
        """Test purlin positions for gable roof."""
        positions = CarportHelpers.calculate_purlin_positions(6000, 3, "Gable")
        
        assert len(positions) == 3
        # Gable has purlins on each side
        assert positions[0] == pytest.approx(750)
        assert positions[1] == pytest.approx(1500)
        assert positions[2] == pytest.approx(2250)
    
    def test_calculate_roof_slope(self):
        """Test roof slope calculation."""
        # 15 degree pitch on 6m span
        rise = CarportHelpers.calculate_roof_slope(6000, 15)
        expected = 6000 * math.tan(math.radians(15))
        
        assert rise == pytest.approx(expected)
        assert rise == pytest.approx(1607.7, rel=0.1)
    
    def test_get_color_material(self):
        """Test color material lookup."""
        # Known color
        color = CarportHelpers.get_color_material("WOODLAND_GREY")
        assert color is not None
        assert color.name == "WOODLAND_GREY"
        assert color.r == 138
        assert color.g == 135
        assert color.b == 133
        
        # Unknown color - returns grey
        color = CarportHelpers.get_color_material("UNKNOWN_COLOR")
        assert color.r == 128
        assert color.g == 128
        assert color.b == 128
        
        # Empty string
        color = CarportHelpers.get_color_material("")
        assert color is None
    
    def test_validate_frame_spacing(self):
        """Test frame spacing validation."""
        # Valid spacing
        positions = [0, 3000, 6000, 9000]
        assert CarportHelpers.validate_frame_spacing(positions) is True
        
        # Invalid spacing (too close)
        positions = [0, 1000, 2000, 3000]
        assert CarportHelpers.validate_frame_spacing(positions) is False
        
        # Edge case - single position
        positions = [0]
        assert CarportHelpers.validate_frame_spacing(positions) is True
    
    def test_calculate_punching_points(self):
        """Test punching point calculation."""
        from src.materials.helpers import FrameMaterialHelper
        
        material = FrameMaterialHelper.c_section(15024)
        
        # Single connection
        points = CarportHelpers.calculate_punching_points(material, 3000, 1)
        assert len(points) == 1
        assert points[0] == 1500  # Center
        
        # Multiple connections
        points = CarportHelpers.calculate_punching_points(material, 3000, 3)
        assert len(points) == 3
        assert points[0] == 750
        assert points[1] == 1500
        assert points[2] == 2250
        
        # No connections
        points = CarportHelpers.calculate_punching_points(material, 3000, 0)
        assert len(points) == 0
    
    def test_get_downpipe_positions(self):
        """Test downpipe position calculation."""
        # Short building - single downpipe
        positions = CarportHelpers.get_downpipe_positions(6000)
        assert len(positions) == 1
        assert positions[0] == 0
        
        # Long building - multiple downpipes
        positions = CarportHelpers.get_downpipe_positions(18000)
        assert len(positions) == 2
        assert positions[0] == 0
        assert positions[1] == 18000
        
        # Very long building
        positions = CarportHelpers.get_downpipe_positions(36000)
        assert len(positions) == 3
        assert positions[0] == 0
        assert positions[1] == 18000
        assert positions[2] == 36000


class TestCarportBuilder:
    """Test CarportBuilder implementation."""
    
    def create_test_input(self) -> BuildingInput:
        """Create standard test input."""
        return BuildingInput(
            building_type=BuildingType.CARPORT,
            name="Test Carport",
            roof_type=CarportRoofType.FLAT,
            validate_engineering=False,
            bays=2,
            span=6000,
            length=6000,
            height=2400,
            wind_speed=41,
            pitch=3,
            slab=True,
            overhang=300,
            cladding_type="corrugated"
        )
    
    def test_static_factory_method(self):
        """Test static create_carport method."""
        input_data = self.create_test_input()
        
        product = CarportBuilder.create_carport(input_data)
        
        assert isinstance(product, CarportProduct)
        assert product.main is not None
        assert product.job_number is not None
    
    def test_initialization(self):
        """Test CarportBuilder initialization."""
        input_data = self.create_test_input()
        
        builder = CarportBuilder(input_data)
        
        assert builder.building_type == BuildingType.CARPORT
        assert builder.bay_size == 3000  # 6000 / 2 bays
        assert builder.is_corrugated is True
        assert builder.height == 2400
        assert builder.span == 6000
    
    def test_slab_creation(self):
        """Test slab structure creation."""
        input_data = self.create_test_input()
        input_data.slab = True
        input_data.overhang = 300
        
        product = CarportBuilder.create_carport(input_data)
        
        assert product.main.slab is not None
        assert product.main.slab.middle is not None
        assert product.main.slab.middle.thickness == CarportConfig.SLAB_THICKNESS
        
        # Check slab dimensions
        slab = product.main.slab.middle
        assert slab.bottom_left.x == 300  # overhang
        assert slab.bottom_right.x == 6300  # overhang + span
        assert slab.top_left.z == 6000  # length
    
    def test_column_creation(self):
        """Test column and footing creation."""
        input_data = self.create_test_input()
        
        product = CarportBuilder.create_carport(input_data)
        
        # Should have 3 frames for 2 bays
        assert len(product.main.side_left.columns) == 3
        assert len(product.main.side_right.columns) == 3
        
        # Check first column
        left_col = product.main.side_left.columns[0]
        assert left_col.column is not None
        assert left_col.footing is not None
        assert left_col.column.tag == "COL_MAIN_L_1"
        assert left_col.footing.tag == "FOOT_MAIN_L_1"
        
        # Check column height
        col_height = left_col.column.end_pos.z - left_col.column.start_pos.z
        assert col_height >= 2400
    
    def test_rafter_creation_flat(self):
        """Test rafter creation for flat roof."""
        input_data = self.create_test_input()
        input_data.roof_type = CarportRoofType.FLAT
        
        product = CarportBuilder.create_carport(input_data)
        
        # Check rafters exist
        assert hasattr(product.main, 'rafters')
        assert len(product.main.rafters) == 3  # One per frame
        
        # Check rafter spans from left to right column
        rafter = product.main.rafters[0]
        assert rafter.tag == "RAFTER_1"
    
    def test_rafter_creation_gable(self):
        """Test rafter creation for gable roof."""
        input_data = self.create_test_input()
        input_data.roof_type = CarportRoofType.GABLE
        input_data.pitch = 15
        
        product = CarportBuilder.create_carport(input_data)
        
        # Gable has 2 rafters per frame
        assert len(product.main.rafters) == 6  # 2 per frame, 3 frames
        
        # Check tags
        assert any(r.tag == "RAFTER_L_1" for r in product.main.rafters)
        assert any(r.tag == "RAFTER_R_1" for r in product.main.rafters)
    
    def test_cladding_creation(self):
        """Test roof cladding creation."""
        input_data = self.create_test_input()
        
        product = CarportBuilder.create_carport(input_data)
        
        # Check cladding exists
        assert product.main.roof_left is not None
        assert product.main.roof_left.wall is not None
        assert product.main.roof_left.wall.cladding is not None
        
        # Check segments
        cladding = product.main.roof_left.wall.cladding
        assert len(cladding.segments) == 2  # One per bay
        
        # Check material
        assert cladding.material.name == "CORRUGATED"
    
    def test_purlin_creation(self):
        """Test purlin creation."""
        input_data = self.create_test_input()
        
        product = CarportBuilder.create_carport(input_data)
        
        # Check purlins exist
        assert product.main.roof_left is not None
        assert len(product.main.roof_left.purlin_bays) > 0
        
        # Check purlin runs full length
        first_bay = product.main.roof_left.purlin_bays[0]
        first_purlin = first_bay.purlins[0].purlin
        purlin_length = first_purlin.end_pos.y - first_purlin.start_pos.y
        assert purlin_length == 6000
    
    def test_flashing_creation(self):
        """Test flashing creation."""
        input_data = self.create_test_input()
        
        product = CarportBuilder.create_carport(input_data)
        
        # Check flashings exist
        assert hasattr(product.main, 'flashings')
        assert len(product.main.flashings) >= 2  # Front and back
        
        # Check tags
        tags = [f.tag for f in product.main.flashings]
        assert "FLASHING_FRONT" in tags
        assert "FLASHING_BACK" in tags
    
    def test_downpipe_creation(self):
        """Test downpipe creation."""
        input_data = self.create_test_input()
        input_data.downpipe_size = "large"
        
        product = CarportBuilder.create_carport(input_data)
        
        # Check downpipes exist
        assert hasattr(product.main, 'downpipes')
        assert len(product.main.downpipes) >= 1
        
        # Check downpipe properties
        dp = product.main.downpipes[0]
        assert dp.material.name == "RECT_100x75"  # Large size
        assert dp.length == 2400  # Building height
    
    def test_engineering_integration(self):
        """Test builder with engineering data."""
        input_data = self.create_test_input()
        
        # Create mock engineering data
        eng_data = EngData(
            ENG_RAFTER="C20030",
            ENG_PURLINSIZE="C15024",
            ENG_PURLINROW=7,
            ENG_COLUMN="SHS15015040",
            ENG_APEXBRACE="C15024",
            ENG_FOOTINGTYPE="bored",
            ENG_FOOTINGDIA="600",
            ENG_FOOTINGDEPTH="900"
        )
        
        product = CarportBuilder.create_carport(input_data, eng_data)
        
        # Check engineering specs applied
        col = product.main.side_left.columns[0]
        assert col.column.material.name == "SHS15015040"
        
        footing = col.footing.footing
        assert footing.footing_type == FootingMaterialType.BORED
        assert footing.diameter == 600
        assert footing.depth == 900
    
    def test_frame_overrides(self):
        """Test frame material overrides."""
        input_data = self.create_test_input()
        input_data.frame_override = True
        input_data.post_override = "SHS10010030"
        input_data.rafter_override = "C20030"
        
        product = CarportBuilder.create_carport(input_data)
        
        # Check overrides applied
        col = product.main.side_left.columns[0]
        assert col.column.material.name == "SHS10010030"
        
        rafter = product.main.rafters[0]
        assert rafter.material.name == "C20030"
    
    def test_to_shed_bim_conversion(self):
        """Test converting CarportProduct to ShedBim."""
        input_data = self.create_test_input()
        
        product = CarportBuilder.create_carport(input_data)
        shed_bim = product.to_shed_bim()
        
        assert isinstance(shed_bim, ShedBim)
        assert shed_bim.main is not None
        assert shed_bim.main == product.main


class TestIntegration:
    """Integration tests for complete workflow."""
    
    @pytest.mark.asyncio
    async def test_complete_carport_workflow(self):
        """Test complete carport creation workflow."""
        # Create input
        input_data = BuildingInput(
            building_type=BuildingType.CARPORT,
            name="Integration Test Carport",
            roof_type=CarportRoofType.FLAT,
            validate_engineering=True,
            bays=3,
            span=6000,
            length=9000,
            height=2700,
            wind_speed=41,
            pitch=3,
            slab=True,
            overhang=600,
            cladding_type="monoclad",
            roof_color="WOODLAND_GREY",
            post_color="MONUMENT",
            downpipe_size="large"
        )
        
        # Validate input
        assert input_data.validate() is True
        
        # Get engineering validation
        eng_service = MockEngineeringService()
        eng_data = await eng_service.validate_design(input_data)
        assert eng_data is not None
        
        # Create carport
        product = CarportBuilder.create_carport(input_data, eng_data)
        
        # Verify complete structure
        assert product.main is not None
        assert len(product.main.side_left.columns) == 4  # 3 bays = 4 frames
        assert len(product.main.rafters) == 4
        assert product.main.slab is not None
        assert product.main.roof_left.wall.cladding is not None
        assert len(product.main.flashings) > 0
        assert len(product.main.downpipes) > 0
        
        # Verify engineering specs applied
        col = product.main.side_left.columns[0]
        assert col.column.material.name == eng_data.ENG_COLUMN
        assert col.footing.footing.footing_type.name == eng_data.ENG_FOOTINGTYPE.upper()
    
    def test_gable_carport_workflow(self):
        """Test gable roof carport creation."""
        input_data = BuildingInput(
            building_type=BuildingType.CARPORT,
            name="Gable Test",
            roof_type=CarportRoofType.GABLE,
            validate_engineering=False,
            bays=2,
            span=6000,
            length=6000,
            height=2400,
            pitch=22.5,
            cladding_type="corrugated"
        )
        
        product = CarportBuilder.create_carport(input_data)
        
        # Verify gable-specific features
        assert product.main.roof_right is not None  # Both sides for gable
        assert len(product.main.rafters) == 6  # 2 per frame
        
        # Check for eave purlins (gable only)
        assert hasattr(product.main, 'eave_purlins')
        assert len(product.main.eave_purlins) == 2
    
    def test_edge_cases(self):
        """Test edge cases and boundary conditions."""
        # Minimum size carport
        input_data = BuildingInput(
            roof_type=CarportRoofType.FLAT,
            bays=1,
            span=2400,
            length=2400,
            height=2100
        )
        
        product = CarportBuilder.create_carport(input_data)
        assert len(product.main.side_left.columns) == 2  # 1 bay = 2 frames
        
        # Maximum reasonable size
        input_data = BuildingInput(
            roof_type=CarportRoofType.FLAT,
            bays=10,
            span=12000,
            length=30000,
            height=4500
        )
        
        product = CarportBuilder.create_carport(input_data)
        assert len(product.main.side_left.columns) == 11  # 10 bays = 11 frames


def run_all_tests():
    """Run all business logic tests."""
    pytest.main([__file__, "-v"])


if __name__ == "__main__":
    run_all_tests()