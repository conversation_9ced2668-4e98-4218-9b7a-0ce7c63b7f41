"""
Test suite for building input validation and business logic.

Tests input validation, constraints, and computed properties.
"""

import pytest
from src.business.building_input import (
    BuildingInput, BuildingType, CarportRoofType
)


class TestBuildingInput:
    """Test BuildingInput functionality."""
    
    def test_creation_defaults(self):
        """Test building input creation with defaults."""
        input_data = BuildingInput()
        
        assert input_data.building_type == BuildingType.CARPORT
        assert input_data.name == ""
        assert input_data.roof_type == CarportRoofType.FLAT
        assert input_data.validate_engineering == False
        assert input_data.bays == 1
        assert input_data.span == 0.0
        assert input_data.length == 0.0
        assert input_data.height == 0.0
    
    def test_creation_with_values(self):
        """Test building input creation with specific values."""
        input_data = BuildingInput(
            building_type=BuildingType.CARPORT,
            name="Test Carport",
            roof_type=CarportRoofType.GABLE,
            span=6000,
            length=6000,
            height=2700,
            bays=2,
            wind_speed=32,
            pitch=10.0
        )
        
        assert input_data.building_type == BuildingType.CARPORT
        assert input_data.name == "Test Carport"
        assert input_data.roof_type == CarportRoofType.GABLE
        assert input_data.span == 6000
        assert input_data.length == 6000
        assert input_data.height == 2700
        assert input_data.bays == 2
        assert input_data.wind_speed == 32
        assert input_data.pitch == 10.0
    
    def test_bay_size_calculation(self):
        """Test bay size calculation."""
        input_data = BuildingInput(
            length=9000,
            bays=3
        )
        
        assert input_data.bay_size == 3000  # 9000 / 3
        
        # Test with single bay
        input_data.bays = 1
        assert input_data.bay_size == 9000
        
        # Test with zero bays (should not crash)
        input_data.bays = 0
        assert input_data.bay_size == 0
    
    def test_overhang_constraints_flat_roof(self):
        """Test overhang constraints for flat roof."""
        input_data = BuildingInput(
            roof_type=CarportRoofType.FLAT,
            span=3600
        )
        
        # For span <= 3600, max overhang is 600
        input_data.overhang = 1000
        assert input_data.overhang == 600  # Should be clamped
        
        # Within limit
        input_data.overhang = 500
        assert input_data.overhang == 500
        
        # Test larger span
        input_data.span = 4500  # Between 3600 and 5000
        input_data.overhang = 1000
        assert input_data.overhang == 900  # Should be clamped
        
        # Test even larger span
        input_data.span = 6000  # > 5000
        input_data.overhang = 1500
        assert input_data.overhang == 1200  # Should be clamped
    
    def test_overhang_non_flat_roof(self):
        """Test overhang is zero for non-flat roofs."""
        input_data = BuildingInput(
            roof_type=CarportRoofType.GABLE,
            span=6000
        )
        
        # Should always be 0 for non-flat roofs
        input_data.overhang = 1000
        assert input_data.overhang == 0
        
        # Test other roof types
        input_data.roof_type = CarportRoofType.AWNING
        input_data.overhang = 500
        assert input_data.overhang == 0
    
    def test_validation_span_limits(self):
        """Test span validation limits."""
        input_data = BuildingInput(
            span=3000,  # Minimum valid
            length=6000,
            height=2400
        )
        
        assert input_data.validate() == True
        
        # Test below minimum
        input_data.span = 2999
        assert input_data.validate() == False
        
        # Test maximum
        input_data.span = 12000
        assert input_data.validate() == True
        
        # Test above maximum
        input_data.span = 12001
        assert input_data.validate() == False
    
    def test_validation_height_limits(self):
        """Test height validation limits."""
        input_data = BuildingInput(
            span=6000,
            length=6000,
            height=2100  # Minimum valid
        )
        
        assert input_data.validate() == True
        
        # Test below minimum
        input_data.height = 2099
        assert input_data.validate() == False
        
        # Test reasonable maximum
        input_data.height = 6000
        assert input_data.validate() == True
    
    def test_validation_length_limits(self):
        """Test length validation based on bays."""
        input_data = BuildingInput(
            span=6000,
            height=2700,
            length=3000,  # Minimum for 1 bay
            bays=1
        )
        
        assert input_data.validate() == True
        
        # Test minimum bay size
        input_data.length = 2999
        assert input_data.validate() == False
        
        # Test multiple bays
        input_data.bays = 3
        input_data.length = 9000  # 3 x 3000
        assert input_data.validate() == True
    
    def test_validation_wind_speed(self):
        """Test wind speed validation."""
        input_data = BuildingInput(
            span=6000,
            length=6000,
            height=2700,
            wind_speed=28  # Valid wind speed
        )
        
        assert input_data.validate() == True
        
        # Test invalid wind speed
        input_data.wind_speed = 35  # Not in standard list
        assert input_data.validate() == False
        
        # Test standard wind speeds
        for speed in [28, 32, 36, 41]:
            input_data.wind_speed = speed
            assert input_data.validate() == True
    
    def test_validation_pitch(self):
        """Test roof pitch validation."""
        input_data = BuildingInput(
            span=6000,
            length=6000,
            height=2700,
            roof_type=CarportRoofType.GABLE,
            pitch=10.0  # Valid pitch
        )
        
        assert input_data.validate() == True
        
        # Test minimum pitch for gable
        input_data.pitch = 4.9
        assert input_data.validate() == False
        
        input_data.pitch = 5.0
        assert input_data.validate() == True
        
        # Test maximum pitch
        input_data.pitch = 30.0
        assert input_data.validate() == True
        
        input_data.pitch = 30.1
        assert input_data.validate() == False
        
        # Test flat roof has no pitch
        input_data.roof_type = CarportRoofType.FLAT
        input_data.pitch = 0.0
        assert input_data.validate() == True
    
    def test_slab_properties(self):
        """Test slab-related properties."""
        input_data = BuildingInput(
            slab=True,
            slab_thickness=100.0
        )
        
        assert input_data.slab == True
        assert input_data.slab_thickness == 100.0
        
        # Test without slab
        input_data.slab = False
        assert input_data.slab == False
    
    def test_soil_type(self):
        """Test soil type property."""
        input_data = BuildingInput(soil="M")
        assert input_data.soil == "M"
        
        # Test different soil types
        for soil_type in ["M", "H", "VH", "EH"]:
            input_data.soil = soil_type
            assert input_data.soil == soil_type
    
    def test_engineering_validation_flag(self):
        """Test engineering validation flag."""
        input_data = BuildingInput()
        assert input_data.validate_engineering == False
        
        input_data.validate_engineering = True
        assert input_data.validate_engineering == True
    
    def test_complete_carport_configuration(self):
        """Test a complete valid carport configuration."""
        input_data = BuildingInput(
            building_type=BuildingType.CARPORT,
            name="Standard Gable Carport",
            roof_type=CarportRoofType.GABLE,
            span=6000,
            length=9000,
            height=2700,
            bays=3,
            wind_speed=32,
            pitch=15.0,
            slab=True,
            slab_thickness=100.0,
            soil="M",
            validate_engineering=True
        )
        
        # Verify all properties
        assert input_data.validate() == True
        assert input_data.bay_size == 3000
        assert input_data.overhang == 0  # Gable roof has no overhang


if __name__ == "__main__":
    pytest.main([__file__, "-v"])