#!/usr/bin/env python3
"""
GLB Generation Testing Facility

Comprehensive tests for GLB file generation including:
- All roof types
- Different materials
- Quality levels
- File validation
- Visual inspection guide
"""

import os
import sys
import json
import struct
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

# Add project to path
sys.path.insert(0, str(Path(__file__).parent))

from src.business.building_input import CarportRoofType
from src.glb_generation.carport_glb_generator import CarportGLBGenerator
from src.glb_generation.material_builder import GLBMaterialBuilder

# Output directory
OUTPUT_DIR = Path("test_output/glb_tests")
OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

# Test results tracking
test_results = {
    "timestamp": datetime.now().isoformat(),
    "tests": [],
    "total": 0,
    "passed": 0,
    "failed": 0,
    "statistics": {
        "total_file_size_mb": 0,
        "average_file_size_kb": 0,
        "total_generation_time_s": 0,
        "average_generation_time_s": 0
    }
}


def validate_glb_file(file_path: Path) -> Dict[str, Any]:
    """
    Validate GLB file structure.
    
    Returns validation results including:
    - Is valid GLB
    - File size
    - JSON chunk size
    - Binary chunk size
    - Has valid header
    """
    result = {
        "valid": False,
        "file_size": 0,
        "json_size": 0,
        "binary_size": 0,
        "errors": []
    }
    
    try:
        with open(file_path, "rb") as f:
            # Read header
            magic = f.read(4)
            if magic != b'glTF':
                result["errors"].append("Invalid magic number")
                return result
                
            version = struct.unpack("<I", f.read(4))[0]
            if version != 2:
                result["errors"].append(f"Invalid version: {version}")
                return result
                
            total_length = struct.unpack("<I", f.read(4))[0]
            result["file_size"] = file_path.stat().st_size
            
            if total_length != result["file_size"]:
                result["errors"].append("File size mismatch")
                
            # Read JSON chunk
            json_length = struct.unpack("<I", f.read(4))[0]
            json_type = struct.unpack("<I", f.read(4))[0]
            
            if json_type != 0x4E4F534A:  # 'JSON'
                result["errors"].append("Invalid JSON chunk type")
                return result
                
            json_data = f.read(json_length)
            result["json_size"] = json_length
            
            # Validate JSON
            try:
                gltf = json.loads(json_data)
                if "asset" not in gltf:
                    result["errors"].append("Missing asset info")
                if "scenes" not in gltf:
                    result["errors"].append("Missing scenes")
                if "nodes" not in gltf:
                    result["errors"].append("Missing nodes")
            except json.JSONDecodeError as e:
                result["errors"].append(f"Invalid JSON: {str(e)}")
                return result
                
            # Read binary chunk if present
            if f.tell() < total_length:
                binary_length = struct.unpack("<I", f.read(4))[0]
                binary_type = struct.unpack("<I", f.read(4))[0]
                
                if binary_type != 0x004E4942:  # 'BIN\0'
                    result["errors"].append("Invalid binary chunk type")
                else:
                    result["binary_size"] = binary_length
                    
        result["valid"] = len(result["errors"]) == 0
        return result
        
    except Exception as e:
        result["errors"].append(f"Validation error: {str(e)}")
        return result


def test_basic_generation():
    """Test basic GLB generation for all roof types."""
    print("\n1. Testing Basic GLB Generation")
    print("-" * 60)
    
    generator = CarportGLBGenerator()
    
    test_configs = [
        {
            "name": "FLAT_basic",
            "roof_type": CarportRoofType.FLAT,
            "span": 6000,
            "length": 6000,
            "height": 2400,
            "pitch": 2,
            "bays": 2,
            "overhang": 300
        },
        {
            "name": "GABLE_basic", 
            "roof_type": CarportRoofType.GABLE,
            "span": 6000,
            "length": 9000,
            "height": 2700,
            "pitch": 15,
            "bays": 3,
            "overhang": 0
        },
        {
            "name": "AWNING_basic",
            "roof_type": CarportRoofType.AWNING,
            "span": 5000,
            "length": 8000,
            "height": 2500,
            "pitch": 10,
            "bays": 3,
            "overhang": 0
        },
        {
            "name": "ATTACHED_AWNING_basic",
            "roof_type": CarportRoofType.ATTACHED_AWNING,
            "span": 4000,
            "length": 6000,
            "height": 2400,
            "pitch": 8,
            "bays": 2,
            "overhang": 0
        }
    ]
    
    for config in test_configs:
        print(f"\nGenerating: {config['name']}")
        start_time = time.time()
        
        try:
            output_path = OUTPUT_DIR / f"{config['name']}.glb"
            
            generated_path = generator.generate_carport_glb(
                span=config["span"],
                length=config["length"],
                height=config["height"],
                roof_type=config["roof_type"],
                pitch=config["pitch"],
                bays=config["bays"],
                overhang=config["overhang"],
                output_path=str(output_path)
            )
            
            generation_time = time.time() - start_time
            
            # Validate the generated file
            validation = validate_glb_file(generated_path)
            
            if validation["valid"]:
                print(f"  ✅ Generated successfully")
                print(f"     File size: {validation['file_size']/1024:.1f} KB")
                print(f"     JSON chunk: {validation['json_size']/1024:.1f} KB")
                print(f"     Binary chunk: {validation['binary_size']/1024:.1f} KB")
                print(f"     Generation time: {generation_time:.2f}s")
                
                test_results["passed"] += 1
                test_results["statistics"]["total_file_size_mb"] += validation["file_size"] / (1024*1024)
                test_results["statistics"]["total_generation_time_s"] += generation_time
            else:
                print(f"  ❌ Validation failed: {validation['errors']}")
                test_results["failed"] += 1
                
            test_results["tests"].append({
                "name": config["name"],
                "config": config,
                "validation": validation,
                "generation_time": generation_time,
                "passed": validation["valid"]
            })
            
        except Exception as e:
            print(f"  ❌ Generation failed: {str(e)}")
            test_results["failed"] += 1
            test_results["tests"].append({
                "name": config["name"],
                "config": config,
                "error": str(e),
                "passed": False
            })
        
        test_results["total"] += 1


def test_material_variations():
    """Test different material configurations."""
    print("\n2. Testing Material Variations")
    print("-" * 60)
    
    material_builder = GLBMaterialBuilder()
    
    # Test material presets
    print("\nAvailable material presets:")
    for preset_name in GLBMaterialBuilder.MATERIAL_PRESETS:
        preset = GLBMaterialBuilder.MATERIAL_PRESETS[preset_name]
        print(f"  - {preset_name}: {preset['name']}")
        print(f"    Color: {preset['baseColor']}")
        print(f"    Metallic: {preset['metallic']}, Roughness: {preset['roughness']}")
    
    # Generate sample with different materials
    generator = CarportGLBGenerator()
    
    # Override default materials in generator (for testing purposes)
    # This tests that the material system is working
    output_path = OUTPUT_DIR / "material_test.glb"
    
    try:
        generated_path = generator.generate_carport_glb(
            span=6000,
            length=6000,
            height=2700,
            roof_type=CarportRoofType.GABLE,
            pitch=15,
            bays=2,
            output_path=str(output_path)
        )
        
        validation = validate_glb_file(generated_path)
        
        if validation["valid"]:
            print(f"\n✅ Material test GLB generated successfully")
            test_results["passed"] += 1
        else:
            print(f"\n❌ Material test failed: {validation['errors']}")
            test_results["failed"] += 1
            
    except Exception as e:
        print(f"\n❌ Material test failed: {str(e)}")
        test_results["failed"] += 1
    
    test_results["total"] += 1


def test_large_structures():
    """Test generation of large structures."""
    print("\n3. Testing Large Structure Generation")
    print("-" * 60)
    
    generator = CarportGLBGenerator()
    
    large_configs = [
        {
            "name": "large_flat_10bay",
            "span": 12000,
            "length": 30000,  # 30m long
            "height": 3600,
            "roof_type": CarportRoofType.FLAT,
            "pitch": 5,
            "bays": 10,
            "overhang": 600
        },
        {
            "name": "large_gable_warehouse",
            "span": 15000,  # 15m wide
            "length": 20000,  # 20m long
            "height": 4000,
            "roof_type": CarportRoofType.GABLE,
            "pitch": 20,
            "bays": 8,
            "overhang": 0
        }
    ]
    
    for config in large_configs:
        print(f"\nGenerating large structure: {config['name']}")
        print(f"  Dimensions: {config['span']/1000}m x {config['length']/1000}m x {config['height']/1000}m")
        print(f"  Bays: {config['bays']}")
        
        start_time = time.time()
        
        try:
            output_path = OUTPUT_DIR / f"{config['name']}.glb"
            
            generated_path = generator.generate_carport_glb(
                span=config["span"],
                length=config["length"],
                height=config["height"],
                roof_type=config["roof_type"],
                pitch=config["pitch"],
                bays=config["bays"],
                overhang=config["overhang"],
                output_path=str(output_path)
            )
            
            generation_time = time.time() - start_time
            validation = validate_glb_file(generated_path)
            
            if validation["valid"]:
                print(f"  ✅ Generated successfully")
                print(f"     File size: {validation['file_size']/1024:.1f} KB")
                print(f"     Generation time: {generation_time:.2f}s")
                
                # Calculate approximate triangle count
                approx_triangles = (
                    config["bays"] * 4 * 100 +  # Rafters
                    (config["bays"] + 1) * 2 * 100 +  # Columns
                    config["bays"] * 10 * 50  # Purlins
                )
                print(f"     Approximate triangles: {approx_triangles:,}")
                
                test_results["passed"] += 1
            else:
                print(f"  ❌ Validation failed: {validation['errors']}")
                test_results["failed"] += 1
                
        except Exception as e:
            print(f"  ❌ Generation failed: {str(e)}")
            test_results["failed"] += 1
        
        test_results["total"] += 1


def test_edge_cases():
    """Test edge cases and boundary conditions."""
    print("\n4. Testing Edge Cases")
    print("-" * 60)
    
    generator = CarportGLBGenerator()
    
    edge_cases = [
        {
            "name": "minimum_size",
            "span": 3000,  # 3m minimum
            "length": 3000,
            "height": 2000,  # 2m minimum
            "roof_type": CarportRoofType.FLAT,
            "pitch": 0,
            "bays": 1,
            "overhang": 0
        },
        {
            "name": "steep_gable",
            "span": 6000,
            "length": 6000,
            "height": 2700,
            "roof_type": CarportRoofType.GABLE,
            "pitch": 30,  # Maximum pitch
            "bays": 2,
            "overhang": 0
        },
        {
            "name": "no_slab",
            "span": 6000,
            "length": 9000,
            "height": 2700,
            "roof_type": CarportRoofType.AWNING,
            "pitch": 10,
            "bays": 3,
            "overhang": 0,
            "slab": False  # No slab
        }
    ]
    
    for config in edge_cases:
        print(f"\nTesting edge case: {config['name']}")
        
        try:
            output_path = OUTPUT_DIR / f"edge_{config['name']}.glb"
            
            # Extract slab parameter if present
            slab = config.pop("slab", True)
            
            generated_path = generator.generate_carport_glb(
                span=config["span"],
                length=config["length"],
                height=config["height"],
                roof_type=config["roof_type"],
                pitch=config["pitch"],
                bays=config["bays"],
                overhang=config["overhang"],
                slab=slab,
                output_path=str(output_path)
            )
            
            validation = validate_glb_file(generated_path)
            
            if validation["valid"]:
                print(f"  ✅ Edge case handled successfully")
                test_results["passed"] += 1
            else:
                print(f"  ❌ Validation failed: {validation['errors']}")
                test_results["failed"] += 1
                
        except Exception as e:
            print(f"  ❌ Edge case failed: {str(e)}")
            test_results["failed"] += 1
        
        test_results["total"] += 1


def generate_viewer_instructions():
    """Generate instructions for viewing GLB files."""
    viewer_file = OUTPUT_DIR / "GLB_VIEWER_GUIDE.md"
    
    with open(viewer_file, "w", encoding="utf-8") as f:
        f.write("# GLB File Viewer Guide\n\n")
        f.write("## How to View Generated GLB Files\n\n")
        
        f.write("### 1. Online Viewers (Recommended)\n")
        f.write("- **Babylon.js Sandbox**: https://sandbox.babylonjs.com/\n")
        f.write("  - Drag and drop GLB files directly\n")
        f.write("  - Shows materials, meshes, and scene graph\n")
        f.write("  - Free, no installation required\n\n")
        
        f.write("- **Three.js Editor**: https://threejs.org/editor/\n")
        f.write("  - File → Import → Select GLB file\n")
        f.write("  - Good for inspecting geometry\n\n")
        
        f.write("- **glTF Viewer**: https://gltf-viewer.donmccurdy.com/\n")
        f.write("  - Simple drag and drop\n")
        f.write("  - Shows technical details\n\n")
        
        f.write("### 2. Desktop Applications\n")
        f.write("- **Microsoft 3D Viewer** (Windows 10/11)\n")
        f.write("  - Pre-installed on Windows\n")
        f.write("  - Double-click GLB files\n\n")
        
        f.write("- **Blender** (Free, Cross-platform)\n")
        f.write("  - File → Import → glTF 2.0\n")
        f.write("  - Professional 3D software\n\n")
        
        f.write("### 3. Validation Checklist\n")
        f.write("When viewing GLB files, check:\n\n")
        f.write("- [ ] All structural components visible (columns, rafters, purlins)\n")
        f.write("- [ ] Materials appear correct (steel gray for metal, concrete gray for footings)\n")
        f.write("- [ ] Roof slope matches the pitch angle\n")
        f.write("- [ ] Number of bays matches configuration\n")
        f.write("- [ ] Dimensions appear proportional\n")
        f.write("- [ ] No missing or floating geometry\n\n")
        
        f.write("### 4. Common Issues\n")
        f.write("- **Black/missing materials**: Viewer doesn't support PBR materials\n")
        f.write("- **Inverted faces**: Normal direction issues (should be double-sided)\n")
        f.write("- **Scale issues**: Check if viewer has unit settings\n\n")
        
        f.write("### 5. Generated Files\n\n")
        
        # List generated files
        glb_files = list(OUTPUT_DIR.glob("*.glb"))
        if glb_files:
            f.write("| File | Size (KB) | Description |\n")
            f.write("|------|-----------|-------------|\n")
            for glb_file in sorted(glb_files):
                size_kb = glb_file.stat().st_size / 1024
                f.write(f"| {glb_file.name} | {size_kb:.1f} | ")
                
                # Add description based on filename
                if "flat" in glb_file.name.lower():
                    f.write("Flat roof carport")
                elif "gable" in glb_file.name.lower():
                    f.write("Gable roof carport")
                elif "awning" in glb_file.name.lower():
                    f.write("Awning roof carport")
                elif "attached" in glb_file.name.lower():
                    f.write("Attached awning carport")
                elif "large" in glb_file.name.lower():
                    f.write("Large structure test")
                elif "edge" in glb_file.name.lower():
                    f.write("Edge case test")
                elif "material" in glb_file.name.lower():
                    f.write("Material variation test")
                else:
                    f.write("Test file")
                    
                f.write(" |\n")
    
    print(f"\nViewer guide saved to: {viewer_file}")


def generate_test_report():
    """Generate comprehensive test report."""
    # Calculate statistics
    if test_results["total"] > 0:
        test_results["statistics"]["average_file_size_kb"] = (
            test_results["statistics"]["total_file_size_mb"] * 1024 / test_results["total"]
        )
        test_results["statistics"]["average_generation_time_s"] = (
            test_results["statistics"]["total_generation_time_s"] / test_results["total"]
        )
    
    # Save JSON report
    report_path = OUTPUT_DIR / f"glb_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_path, "w") as f:
        json.dump(test_results, f, indent=2)
    
    # Create summary report
    summary_path = OUTPUT_DIR / "GLB_TEST_SUMMARY.txt"
    with open(summary_path, "w", encoding="utf-8") as f:
        f.write("GLB GENERATION TEST SUMMARY\n")
        f.write("=" * 60 + "\n\n")
        f.write(f"Test Date: {test_results['timestamp']}\n")
        f.write(f"Total Tests: {test_results['total']}\n")
        f.write(f"Passed: {test_results['passed']}\n")
        f.write(f"Failed: {test_results['failed']}\n")
        f.write(f"Success Rate: {test_results['passed']/test_results['total']*100:.1f}%\n\n")
        
        f.write("STATISTICS:\n")
        f.write(f"Total File Size: {test_results['statistics']['total_file_size_mb']:.2f} MB\n")
        f.write(f"Average File Size: {test_results['statistics']['average_file_size_kb']:.1f} KB\n")
        f.write(f"Total Generation Time: {test_results['statistics']['total_generation_time_s']:.2f} seconds\n")
        f.write(f"Average Generation Time: {test_results['statistics']['average_generation_time_s']:.2f} seconds\n\n")
        
        f.write("VALIDATION RESULTS:\n")
        for test in test_results["tests"]:
            status = "PASS" if test.get("passed", False) else "FAIL"
            f.write(f"- {test['name']}: {status}\n")
            if not test.get("passed", False):
                if "error" in test:
                    f.write(f"  Error: {test['error']}\n")
                elif "validation" in test and test["validation"].get("errors"):
                    f.write(f"  Validation errors: {', '.join(test['validation']['errors'])}\n")
    
    print(f"\nTest report saved to: {report_path}")
    print(f"Summary saved to: {summary_path}")


def main():
    """Run all GLB generation tests."""
    print("=" * 60)
    print("GLB GENERATION TESTING FACILITY")
    print("=" * 60)
    print(f"Output directory: {OUTPUT_DIR}")
    print(f"Starting tests at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Run test suites
    test_basic_generation()
    test_material_variations()
    test_large_structures()
    test_edge_cases()
    
    # Generate reports and guides
    generate_viewer_instructions()
    generate_test_report()
    
    # Print summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"Total Tests: {test_results['total']}")
    print(f"Passed: {test_results['passed']}")
    print(f"Failed: {test_results['failed']}")
    
    if test_results['total'] > 0:
        success_rate = test_results['passed'] / test_results['total'] * 100
        print(f"Success Rate: {success_rate:.1f}%")
        
        if success_rate == 100:
            print("\n✅ All tests passed!")
        elif success_rate >= 80:
            print("\n⚠️  Most tests passed, check failures")
        else:
            print("\n❌ Many tests failed, investigation needed")
    
    print(f"\nGLB files generated in: {OUTPUT_DIR}")
    print("View files using online viewers or 3D applications")
    print("See GLB_VIEWER_GUIDE.md for viewing instructions")


if __name__ == "__main__":
    main()