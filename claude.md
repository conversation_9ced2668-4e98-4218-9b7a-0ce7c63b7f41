# CLAUDE.md - AI Assistant Instructions

## Project Overview
This project is a Python implementation of a carport IFC generator that creates Industry Foundation Classes files. The implementation is 100% aligned with the C# BimCoreLibrary implementation.

## Current Implementation Status

### ✅ Completed
1. **C# Alignment Analysis** - Full understanding of BimCoreLibrary calculations
2. **BREP-based IFC Generation** - Universal compatibility approach
3. **Complete Structural Elements**:
   - Footings (bored and block types)
   - Slabs with proper positioning (horizontal at ground level)
   - Columns with correct heights
   - Rafters with proper angles
   - Purlins with correct spacing
   - Eave purlins with C-section BREP and proper Y-axis orientation
4. **Material System** - Matching C# defaults and engineering overrides
5. **Test Generation** - 50+ carport variations
6. **API System** - Complete FastAPI with Swagger documentation
7. **Comprehensive Documentation** - Detailed README with geometry calculation references

### 🔧 Latest Updates (2025-07-04)
1. **GLB Generation Module** - Created dedicated `src/glb_generation/` aligned with C# ShedGltf
2. **Binary GLB Format** - Proper implementation with JSON/BIN chunks matching C#
3. **PBR Materials** - Steel, concrete, and other materials matching C# defaults
4. **API Enhancement** - Updated V2 endpoints to use new GLB generator
5. **Testing Scripts** - Added `test_glb_generation.py` and `test_api_functionality.py`

### 🔧 Previous Updates (2025-07-03)
1. **Project Review** - Complete verification against C# reference with line-by-line comparison
2. **Documentation Enhancement** - Added detailed geometry calculation references with exact line numbers
3. **Project Cleanup** - Organized and archived legacy implementations
4. **API Enhancement** - Updated Swagger API for better IFC/GLB generation
5. **Testing Facility** - Enhanced comprehensive testing scripts

### 🔧 Previous Fixes (2025-07-01)
1. **Slab BREP** - Fixed to be horizontal at ground level (Z=0 to Z=-thickness)
2. **Eave Purlin BREP** - Fixed C-section generation for Y-axis orientation
3. **Rotation Consistency** - All members now use correct axis rotations matching C#
4. **Codebase Cleanup** - Archived legacy files and organized structure
5. **Roof Type Behaviors** - Fixed overhang handling for each roof type:
   - FLAT: Supports overhang
   - GABLE: No overhang (forced to 0)
   - AWNING: No overhang (forced to 0)
   - ATTACHED_AWNING: No overhang, no right columns
6. **Rafter Calculations** - Fixed for all roof types with proper spans
7. **Purlin Segmentation** - Non-gable purlins now segment between rafters
8. **Gable Purlin Overlap** - Gable purlins now overlap rafters as per C#

### 📁 Project Structure
```
PyModel/
├── src/
│   ├── ifc_generation/          # IFC BREP generation
│   │   ├── ifc_brep_generator.py     # Core BREP geometry generator
│   │   └── carport_generator.py      # High-level carport API
│   ├── business/                # Core business logic
│   │   ├── structure_builder.py      # Main structure builder (fixed)
│   │   ├── building_input.py         # Input models and enums
│   │   └── helpers.py                # Helper utilities
│   ├── geometry/                # Geometry calculations
│   └── materials/               # Material definitions
├── tests/                       # Unit and integration tests
├── test_all_roof_types_comprehensive.py  # Complete roof type validation
├── test_output/                 # Generated IFC files
└── archive/                    # Archived old implementations
```

## Critical C# Alignment Details

### Coordinate System
- **X-axis**: Width direction (span)
- **Y-axis**: Length direction (along building)  
- **Z-axis**: Height direction (vertical)
- Origin at (0,0,0) with footings at ground level

### Key Calculations from C# (DETAILED)

#### 0. Overhang Rules by Roof Type
```csharp
// C# Ref: Various locations in StructureBuilderBase.cs
effectiveOverhang = RoofType == FLAT ? BuildingInput.Overhang : 0;
// GABLE, AWNING, ATTACHED_AWNING all force overhang to 0
```

#### 1. Column Positioning
```csharp
// Left column X offset
leftXOffset = BuildingInput.Overhang + Column.Width/2;
if (RoofType == GABLE) 
    leftXOffset += EavePurlin.Flange;
else
    leftXOffset += Rafter.Flange;

// Y offset varies by frame position
if (i == 0) // First frame
    leftYOffset = 0.5 * Column.Width;
else if (i == Bays) // Last frame  
    leftYOffset = -0.5 * Column.Width;
else // Middle frames
    leftYOffset = RoofType == GABLE ? 0 : 0.5 * (Rafter.Flange + Column.Width);
```

#### 2. Column Heights
```csharp
// Left column height
leftColumnEndZ = BuildingInput.Height + Rafter.Flange * tan(pitch);

// Right column height
rightColumnEndZ = leftColumnEndZ;
if (RoofType != GABLE)
    rightColumnEndZ += (Span - Column.Width) * tan(pitch);
```

#### 3. Rafter Calculations

##### Flat/Awning/AttachedAwning Rafters
```csharp
// C# Ref: FindFlatRafters method
startX = 0;
endX = Span + 2 * Overhang;
if (RoofType == ATTACHED_AWNING)
    endX -= Purlin.Flange;

startZ = Height - Overhang * tan(pitch) - 0.5 * Rafter.Web * cos(pitch);
endZ = Height + (Span + Overhang) * tan(pitch) - 0.5 * Rafter.Web * cos(pitch);

// Rotation: 0 for normal frames, π for last frame
rotation = (i == Bays) ? Math.PI : 0;
```

##### Gable Rafters
```csharp
// C# Ref: FindGableRafters method
// Left rafter
startX = Column.Width + EavePurlin.Flange + 0.5 * Rafter.Web * sin(pitch);
endX = 0.5 * Span - 0.5 * Rafter.Web * sin(pitch);
startZ = Height + (Column.Width + EavePurlin.Flange) * tan(pitch) 
         - Purlin.Web / cos(pitch) - Rafter.Web * 0.5 * cos(pitch);
endZ = (endX - startX) * tan(pitch) + startZ;

// Right rafter (mirror of left)
rightStartX = Span - Column.Width - EavePurlin.Flange - 0.5 * Rafter.Web * sin(pitch);
rightEndX = 0.5 * Span + 0.5 * Rafter.Web * sin(pitch);
```

#### 4. Purlin Calculations

##### Flat/Awning Purlins (Segmented)
```csharp
// C# Ref: FindPurlinsFlat method
// X positioning
x_interval = (Span - EavePurlin.Flange) / (PurlinRows - 1);
purlinX = (purlinID - 1) * x_interval + 0.5 * Rafter.Flange + Overhang;
purlinZ = Height + (purlinID - 1) * x_interval * tan(pitch);

// Y segmentation (between rafters)
if (bayID == 0) {
    yStart = Rafter.Width;
    yEnd = (bayID + 1) * BaySize - 0.5 * Rafter.Width;
} else if (bayID == Bays - 1) {
    yStart = bayID * BaySize + 0.5 * Rafter.Width;
    yEnd = (bayID + 1) * BaySize - Rafter.Width;
} else {
    yStart = bayID * BaySize + 0.5 * Rafter.Width;
    yEnd = (bayID + 1) * BaySize - 0.5 * Rafter.Width;
}
```

##### Gable Purlins (Overlapping)
```csharp
// C# Ref: FindPurlinsGable method
// X positioning
gableStartX = 2 * PostOffset + EavePurlin.Flange + 0.5 * Rafter.Width * sin(pitch);
gableEndX = 0.5 * Span - 0.5 * Rafter.Width * sin(pitch);
x_interval = (gableEndX - gableStartX) / PurlinRows;

// Y overlap (extends beyond rafters)
if (bayID == 0) {
    yStart = 0;
    yEnd = (bayID + 1) * BaySize + Rafter.Flange;
} else if (bayID == Bays - 1) {
    yStart = bayID * BaySize - Rafter.Width;
    yEnd = Length;
} else {
    yStart = bayID * BaySize - Rafter.Flange;
    yEnd = (bayID + 1) * BaySize + Rafter.Flange;
}
```
leftPurlinX = (purlinID - 1) * x_interval + Overhang;
rightPurlinX = Span - (purlinID - 1) * x_interval + Overhang;
```

#### 5. Eave Purlin Positioning
```csharp
// Gable eave purlins
leftX = EavePurlin.Flange * 0.5;
rightX = Span - EavePurlin.Flange * 0.5;
Z = Height - 0.5 * EavePurlin.Web;

// Flat/Awning eave purlins
leftX = 0 + EavePurlin.Width / 2;
rightX = Span + 2 * Overhang - EavePurlin.Width / 2;
leftZ = Height - Overhang * tan(pitch) - 0.5 * EavePurlin.Web;
rightZ = Height + (Span + Overhang) * tan(pitch) - 0.5 * EavePurlin.Web;
```

### Important Implementation Files

#### Core Files
- **src/business/structure_builder.py**: Contains all fixed calculations
  - Lines 555-646: Column positioning with roof type logic
  - Lines 647-776: Rafter calculations (flat vs gable)
  - Lines 812-1023: Purlin calculations (segmented vs overlapping)
  - Lines 1025-1124: Eave purlin positioning
  - Lines 1337-1386: Helper methods (_get_overhang_for_roof_type)

- **src/ifc_generation/ifc_brep_generator.py**: BREP geometry generation
  - Lines 59-131: SHS profile (columns)
  - Lines 133-234: C-section profile (rafters/purlins)
  - Lines 236-318: TopHat profile (gable purlins)
  - Lines 691-777: Rafter IFC generation with rotation
  - Lines 779-892: Purlin IFC generation
  - Lines 894-997: Eave purlin IFC generation

#### Test Files
- **test_all_roof_types_comprehensive.py**: Complete validation suite
  - Tests all 4 roof types with proper overhang behavior
  - Validates column counts (attached awning has no right columns)
  - Checks purlin segmentation vs overlap
  - Includes engineering override tests

### Material Defaults (Must Match C#)
```python
# From C# CarportBuilder constructor
COLUMN = "SHS07507525"      # 75x75x2.5mm
RAFTER = "C15015"           # 150x64x1.5mm  
PURLIN_GABLE = "TH064100"   # TopHat 64x100mm
PURLIN_FLAT = "C15015"      # C-section 150x64x1.5mm
EAVE_PURLIN = "C15015"      # C-section 150x64x1.5mm
APEX_BRACE = "C10010"       # 100x50x1.0mm
KNEE_BRACE = "C10010"       # 100x50x1.0mm
FOOTING = "Bored 300x300"   # 300mm dia x 300mm depth
```

## BREP Implementation Details

### Why BREP?
- **Universal Compatibility**: Works in ALL IFC viewers
- **Accurate Geometry**: Exact representation of profiles
- **No Schema Issues**: Avoids IFCARBITRARYCLOSEDPROFILEDEF problems

### Profile Types Implemented

1. **SHS (Square Hollow Section)**
   - Hollow rectangular profile
   - Inner void with wall thickness
   - 10 faces total

2. **C-Section**
   - 12 vertices for complete profile
   - Includes lips on flanges
   - Web facing specific direction

3. **TopHat Section**  
   - 8 vertices for profile
   - Base flange wider than top
   - Used for gable purlins

4. **Footings**
   - Cylindrical (16 segments) for bored
   - Rectangular block for pad footings

### Rotation Handling
```python
# Rafter rotation (align with slope)
if length > 0:
    z_dir = create_direction(dx/length, dy/length, dz/length)
    x_dir = create_direction(dy/length, -dx/length, 0)
    local_axis = IFCAXIS2PLACEMENT3D(pt, z_dir, x_dir)

# Purlin rotation (around Y-axis)
if rotation != 0:
    x_dir = create_direction(cos(rotation), 0, sin(rotation))
    z_dir = create_direction(-sin(rotation), 0, cos(rotation))
    local_axis = IFCAXIS2PLACEMENT3D(pt, z_dir, x_dir)

# Eave purlin rotation (in XY plane)
if rotation != 0:
    x_dir = create_direction(cos(rotation), sin(rotation), 0)
    local_axis = IFCAXIS2PLACEMENT3D(pt, '$', x_dir)
```

## Engineering Data Override

When `EngData` is provided, it overrides defaults:
```python
# Create engineering data (all fields required)
eng_data = EngData(
    ENG_COLUMN="SHS10010040",      # Column material code (check available)
    ENG_RAFTER="C20019",           # Rafter material code (check available)
    ENG_PURLINSIZE="C15019",       # Purlin material code (check available)
    ENG_PURLINROW=7,                # Number of purlin rows (int)
    ENG_FOOTINGTYPE="block",       # "bored" or "block"
    ENG_FOOTINGDIA="400",          # Diameter/width in mm (string)
    ENG_FOOTINGDEPTH="600"         # Depth in mm (string)
)

# Available materials (from src/materials/helpers.py):
# C-sections: C10010, C10012, C10015, C10019, C15015, C15019, C20019, C20024, etc.
# SHS sections: SHS07507525, SHS07507540, SHS10010040
# TopHat sections: TH064100, TH075125, TH096100, etc.

# Apply engineering overrides
if eng_data:
    rafter = get_frame_material(eng_data.ENG_RAFTER)
    column = get_frame_material(eng_data.ENG_COLUMN)
    purlin = get_frame_material(eng_data.ENG_PURLINSIZE)
    purlin_rows = eng_data.ENG_PURLINROW
    
    # Footing type
    is_bored = eng_data.ENG_FOOTINGTYPE.lower() == "bored"
    footing = create_bored(dia, depth) if is_bored else create_block(dia, depth)
```

## Common Issues and Solutions

### 1. Coordinate System Confusion
- **Issue**: Mixing up Y and Z axes
- **Solution**: Y is always length (along building), Z is always height

### 2. Material Thickness in Calculations
- **Issue**: Forgetting to account for material dimensions
- **Solution**: Always include flange/web/width adjustments in positioning

### 3. Rotation Direction
- **Issue**: Incorrect rotation angles
- **Solution**: Follow right-hand rule, test with known configurations

### 4. Unit Consistency
- **Issue**: Mixing meters and millimeters
- **Solution**: Always use millimeters internally, IFC uses MILLI.METRE

## Testing Approach

### Test Scripts Available
```bash
# Comprehensive test facility
python test_comprehensive_facility.py --suite all

# Test all roof types (IFC)
python test_all_roof_types_comprehensive.py

# Test GLB generation
python test_glb_generation.py

# Test API functionality
python test_api_functionality.py
```

### Key Testing Scripts
- **test_comprehensive_facility.py**: Main test orchestrator
- **test_all_roof_types_comprehensive.py**: IFC generation for all roof types
- **test_glb_generation.py**: GLB file generation and validation
- **test_api_functionality.py**: API endpoint testing (requires running API server)

### Validation Steps
1. Visual inspection in IFC viewer
2. Check material assignments
3. Verify dimensions match input
4. Compare with C# output

## Future Enhancements

1. **Connection Details**: Brackets between members
2. **Cladding**: Roof sheeting representation  
3. **Fasteners**: Bolts and screws
4. **Bracing**: Apex and knee braces
5. **Gutters**: Downpipes and guttering

## Quick Reference

### Generate IFC File
```python
from src.ifc_generation.carport_generator import CarportIFCGenerator
from src.business.building_input import CarportRoofType

gen = CarportIFCGenerator()
path = gen.generate_carport(
    span=6000, length=9000, height=2700,
    roof_type=CarportRoofType.GABLE,
    pitch=15, bays=3
)
```

### Generate GLB File
```python
from src.glb_generation.carport_glb_generator import CarportGLBGenerator

glb_gen = CarportGLBGenerator()
path = glb_gen.generate_carport_glb(
    span=6000, length=9000, height=2700,
    roof_type=CarportRoofType.GABLE,
    pitch=15, bays=3,
    output_path="carport.glb"
)
```

### With Engineering Data
```python
# Create engineering data with all required fields
eng = EngData(
    ENG_COLUMN="SHS10010040",  # Must be available in materials catalog
    ENG_RAFTER="C20019",       # Must be available in materials catalog
    ENG_PURLINSIZE="C15019",   # Must be available in materials catalog  
    ENG_PURLINROW=7,
    ENG_FOOTINGTYPE="block",
    ENG_FOOTINGDIA="400",
    ENG_FOOTINGDEPTH="600"
)

# Use with generator
path = gen.generate_carport(
    span=6000, length=9000, height=2700,
    roof_type=CarportRoofType.GABLE,
    pitch=15, bays=3,
    eng_data=eng
)
```

## Important Notes

1. **Always match C# behavior** - When in doubt, check StructureBuilderBase.cs
2. **Use BREP for geometry** - Most reliable IFC representation
3. **Test with multiple viewers** - Ensure universal compatibility
4. **Document deviations** - If Python differs from C#, document why
5. **Maintain material codes** - Use exact C# material naming