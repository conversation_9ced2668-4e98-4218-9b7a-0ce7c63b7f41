#!/usr/bin/env python3
"""Basic test to verify geometry module functionality."""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from geometry import Vec2, Vec3, Mat4, Line2, <PERSON>2, <PERSON><PERSON>, <PERSON><PERSON>3, Plane3


def test_vec2():
    """Test Vec2 basic operations."""
    print("\n=== Testing Vec2 ===")
    v1 = Vec2(3.0, 4.0)
    v2 = Vec2(1.0, 2.0)
    
    print(f"v1 = {v1}")
    print(f"v2 = {v2}")
    print(f"v1.length() = {v1.length()}")
    print(f"v1 + v2 = {v1 + v2}")
    print(f"Vec2.dot(v1, v2) = {Vec2.dot(v1, v2)}")
    print(f"Vec2.normal(v1) = {Vec2.normal(v1)}")
    
    # Test intersection
    a1 = Vec2(0, 0)
    a2 = Vec2(4, 4)
    b1 = Vec2(0, 4)
    b2 = Vec2(4, 0)
    inters = Vec2.inters(a1, a2, b1, b2)
    print(f"Line intersection: {inters}")
    

def test_vec3():
    """Test Vec3 basic operations."""
    print("\n=== Testing Vec3 ===")
    v1 = Vec3(1.0, 2.0, 3.0)
    v2 = Vec3(4.0, 5.0, 6.0)
    
    print(f"v1 = {v1}")
    print(f"v2 = {v2}")
    print(f"v1.length() = {v1.length()}")
    print(f"Vec3.dot(v1, v2) = {Vec3.dot(v1, v2)}")
    print(f"Vec3.cross(v1, v2) = {Vec3.cross(v1, v2)}")
    print(f"Vec3.distance(v1, v2) = {Vec3.distance(v1, v2)}")
    

def test_mat4():
    """Test Mat4 transformations."""
    print("\n=== Testing Mat4 ===")
    
    # Translation
    trans = Mat4.create_translation(10, 20, 30)
    p = Vec3(1, 2, 3)
    p_trans = trans.transform_position(p)
    print(f"Translation of {p} by (10, 20, 30) = {p_trans}")
    
    # Rotation
    rot = Mat4.create_rotation_z(Geo.DEG90)  # 90 degrees
    v = Vec3(1, 0, 0)
    v_rot = rot.transform_vector(v)
    print(f"Rotation of {v} by 90° around Z = {v_rot}")
    
    # Scale
    scale = Mat4.create_scale(2.0)
    v_scaled = scale.transform_vector(v)
    print(f"Scale of {v} by 2.0 = {v_scaled}")
    

def test_lines():
    """Test Line operations."""
    print("\n=== Testing Lines ===")
    
    line1 = Line2(Vec2(0, 0), Vec2(4, 4))
    line2 = Line2(Vec2(0, 4), Vec2(4, 0))
    
    print(f"line1 = {line1}")
    print(f"line2 = {line2}")
    print(f"line1.magnitude() = {line1.magnitude()}")
    print(f"Line2.inters(line1, line2) = {Line2.inters(line1, line2)}")
    

def test_boxes():
    """Test Box operations."""
    print("\n=== Testing Boxes ===")
    
    box = Box2.from_center(Vec2(5, 5), Vec2(10, 10))
    print(f"box = {box}")
    print(f"box.size() = {box.size()}")
    print(f"box.contains(Vec2(7, 7)) = {box.contains(Vec2(7, 7))}")
    print(f"box.contains(Vec2(20, 20)) = {box.contains(Vec2(20, 20))}")
    

def test_geo_helpers():
    """Test Geo helper functions."""
    print("\n=== Testing Geo Helpers ===")
    
    # Angle normalization
    angle = Geo.DEG360 + Geo.DEG45
    normalized = Geo.normalize_angle(angle)
    print(f"normalize_angle({angle}) = {normalized}")
    
    # Polar coordinates
    polar = Geo.polar(Geo.DEG45, 10)
    print(f"polar(45°, 10) = {polar}")
    
    # Bay sizes
    bays = Geo.get_bay_sizes(10000, 3, 10)
    print(f"get_bay_sizes(10000, 3, 10) = {bays}")
    print(f"Sum of bays = {sum(bays)}")
    

def test_plane():
    """Test Plane operations."""
    print("\n=== Testing Plane3 ===")
    
    # Create plane from three points
    p1 = Vec3(0, 0, 0)
    p2 = Vec3(1, 0, 0)
    p3 = Vec3(0, 1, 0)
    plane = Plane3.from_three_points(p1, p2, p3)
    print(f"Plane from points: normal={plane.normal}, d={plane.d}")
    
    # Test point distance
    test_point = Vec3(0, 0, 5)
    distance = plane.distance_to_point(test_point)
    print(f"Distance from {test_point} to plane = {distance}")
    

def main():
    """Run all basic tests."""
    print("BIM Backend Python - Basic Geometry Tests")
    print("=" * 50)
    
    test_vec2()
    test_vec3()
    test_mat4()
    test_lines()
    test_boxes()
    test_geo_helpers()
    test_plane()
    
    print("\n" + "=" * 50)
    print("✅ All basic tests completed successfully!")


if __name__ == "__main__":
    main()