#!/usr/bin/env python3
"""
Comprehensive Test Facility for BIM Backend

This module provides a complete testing framework with automated test discovery,
execution, reporting, and validation for all components of the BIM Backend system.
"""

import sys
import os
import json
import time
import asyncio
import traceback
from datetime import datetime
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
import argparse

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))


@dataclass
class TestResult:
    """Test result data structure"""
    name: str
    category: str
    status: str  # 'passed', 'failed', 'skipped', 'error'
    duration: float
    error: Optional[str] = None
    details: Optional[Dict[str, Any]] = None


class TestFacility:
    """Comprehensive test facility for BIM Backend"""
    
    def __init__(self, verbose: bool = True):
        self.verbose = verbose
        self.results: List[TestResult] = []
        self.start_time = None
        self.end_time = None
        
    def log(self, message: str, level: str = "INFO"):
        """Log message with timestamp"""
        if self.verbose:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(f"[{timestamp}] [{level}] {message}")
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all test categories"""
        self.start_time = time.time()
        self.log("Starting comprehensive test suite", "INFO")
        
        # Run test categories
        self.test_dependencies()
        self.test_geometry()
        self.test_materials()
        self.test_bim_model()
        self.test_business_logic()
        self.test_api_layer()
        self.test_integration()
        self.test_performance()
        self.test_security()
        self.test_error_handling()
        
        self.end_time = time.time()
        return self.generate_report()
    
    def test_dependencies(self):
        """Test all required dependencies"""
        self.log("Testing dependencies...", "INFO")
        
        dependencies = {
            "numpy": "Mathematical operations",
            "scipy": "Scientific computing",
            "triangle": "Triangulation",
            "shapely": "Geometric operations",
            "pyclipper": "Polygon clipping",
            "fastapi": "API framework",
            "pydantic": "Data validation",
            "cryptography": "Encryption",
            "httpx": "HTTP client"
        }
        
        for module, description in dependencies.items():
            start = time.time()
            try:
                __import__(module)
                self.add_result(
                    name=f"Import {module}",
                    category="dependencies",
                    status="passed",
                    duration=time.time() - start,
                    details={"description": description}
                )
            except ImportError as e:
                self.add_result(
                    name=f"Import {module}",
                    category="dependencies",
                    status="failed",
                    duration=time.time() - start,
                    error=str(e),
                    details={"description": description}
                )
    
    def test_geometry(self):
        """Test geometry components"""
        self.log("Testing geometry components...", "INFO")
        
        # Test Vec2
        start = time.time()
        try:
            from geometry.primitives import Vec2
            
            v1 = Vec2(3.0, 4.0)
            v2 = Vec2(1.0, 2.0)
            
            tests = [
                ("Vec2.length", lambda: abs(v1.length() - 5.0) < 0.0001),
                ("Vec2.dot", lambda: abs(Vec2.dot(v1, v2) - 11.0) < 0.0001),
                ("Vec2.normalized", lambda: abs(v1.normalized().length() - 1.0) < 0.0001),
                ("Vec2 addition", lambda: (v1 + v2).x == 4.0 and (v1 + v2).y == 6.0),
                ("Vec2 subtraction", lambda: (v1 - v2).x == 2.0 and (v1 - v2).y == 2.0),
            ]
            
            for test_name, test_func in tests:
                if test_func():
                    self.add_result(test_name, "geometry", "passed", 0.001)
                else:
                    self.add_result(test_name, "geometry", "failed", 0.001)
                    
        except Exception as e:
            self.add_result("Vec2 tests", "geometry", "error", time.time() - start, str(e))
        
        # Test Vec3
        start = time.time()
        try:
            from geometry.primitives import Vec3
            
            v1 = Vec3(1.0, 2.0, 3.0)
            v2 = Vec3(4.0, 5.0, 6.0)
            
            tests = [
                ("Vec3.cross", lambda: Vec3.cross(Vec3.unit_x(), Vec3.unit_y()).z == 1.0),
                ("Vec3.length", lambda: abs(v1.length() - 3.7416573867739413) < 0.0001),
                ("Vec3 operations", lambda: (v2 - v1).length() > 0),
            ]
            
            for test_name, test_func in tests:
                if test_func():
                    self.add_result(test_name, "geometry", "passed", 0.001)
                else:
                    self.add_result(test_name, "geometry", "failed", 0.001)
                    
        except Exception as e:
            self.add_result("Vec3 tests", "geometry", "error", time.time() - start, str(e))
        
        # Test Mat4
        start = time.time()
        try:
            from geometry.matrix import Mat4
            from geometry.helpers import Geo
            
            m1 = Mat4.create_rotation_z(Geo.DEG45)
            m2 = Mat4.create_translation(10, 20, 30)
            m3 = m2 * m1
            
            self.add_result("Mat4 operations", "geometry", "passed", time.time() - start)
            
        except Exception as e:
            self.add_result("Mat4 tests", "geometry", "error", time.time() - start, str(e))
    
    def test_materials(self):
        """Test materials system"""
        self.log("Testing materials system...", "INFO")
        
        start = time.time()
        try:
            from materials.base import FrameMaterial, FrameMaterialType
            from materials.visual import ColorMaterial
            from materials.helpers import MaterialLibrary
            
            # Test frame material
            frame = FrameMaterial(
                name="C15024",
                material_type=FrameMaterialType.C,
                width=150.0,
                height=24.0,
                thickness=1.5
            )
            
            if frame.web == 24.0 and frame.flange == 150.0:
                self.add_result("FrameMaterial properties", "materials", "passed", 0.001)
            else:
                self.add_result("FrameMaterial properties", "materials", "failed", 0.001)
            
            # Test color material
            color = ColorMaterial(
                name="Monument",
                rgb_hex="#5A5956",
                finish="Colorbond"
            )
            
            if color.r == 90 and color.g == 89 and color.b == 86:
                self.add_result("ColorMaterial RGB", "materials", "passed", 0.001)
            else:
                self.add_result("ColorMaterial RGB", "materials", "failed", 0.001)
            
            # Test material library
            lib = MaterialLibrary.get_default()
            if len(lib.get_frame_materials()) > 0 and len(lib.get_color_materials()) > 0:
                self.add_result("MaterialLibrary", "materials", "passed", 0.001)
            else:
                self.add_result("MaterialLibrary", "materials", "failed", 0.001)
                
        except Exception as e:
            self.add_result("Materials tests", "materials", "error", time.time() - start, str(e))
    
    def test_bim_model(self):
        """Test BIM data model"""
        self.log("Testing BIM data model...", "INFO")
        
        start = time.time()
        try:
            from bim.shed_bim import ShedBim, ShedBimPartMain
            from bim.wall_roof import ShedBimSide, ShedBimRoof
            from bim.components import ShedBimColumn
            from geometry.primitives import Vec3
            
            # Create structure
            main = ShedBimPartMain(
                roof_type="Flat",
                roof_left=ShedBimRoof(cladding_material="Colorbond", cladding_reversed=False),
                side_left=ShedBimSide(),
                side_right=ShedBimSide()
            )
            
            # Add column
            main.side_left.columns.append(
                ShedBimColumn(
                    base=Vec3(0, 0, 0),
                    top=Vec3(0, 0, 2400),
                    frame_material="C15024"
                )
            )
            
            if len(main.get_sides()) == 2 and len(main.get_roofs()) == 1:
                self.add_result("BIM structure creation", "bim", "passed", time.time() - start)
            else:
                self.add_result("BIM structure creation", "bim", "failed", time.time() - start)
                
        except Exception as e:
            self.add_result("BIM model tests", "bim", "error", time.time() - start, str(e))
    
    def test_business_logic(self):
        """Test business logic layer"""
        self.log("Testing business logic...", "INFO")
        
        start = time.time()
        try:
            from business.building_input import BuildingInput, BuildingType, CarportRoofType
            from business.structure_builder import CarportBuilder
            
            # Test building input
            building_input = BuildingInput(
                building_type=BuildingType.CARPORT,
                roof_type=CarportRoofType.FLAT,
                span=3000.0,
                length=3000.0,
                height=2400.0
            )
            
            # Test overhang validation
            building_input.overhang = 800.0
            if building_input.overhang == 600.0:  # Should be clamped
                self.add_result("BuildingInput validation", "business", "passed", 0.001)
            else:
                self.add_result("BuildingInput validation", "business", "failed", 0.001)
            
            # Test carport builder
            carport = CarportBuilder.create_carport(building_input)
            if carport and carport.main:
                self.add_result("CarportBuilder", "business", "passed", time.time() - start)
            else:
                self.add_result("CarportBuilder", "business", "failed", time.time() - start)
                
        except Exception as e:
            self.add_result("Business logic tests", "business", "error", time.time() - start, str(e))
    
    def test_api_layer(self):
        """Test API layer components"""
        self.log("Testing API layer...", "INFO")
        
        # Test encryption
        start = time.time()
        try:
            from services.encryption import AESEncryption
            
            aes = AESEncryption("test-key")
            plaintext = "Hello, BIM!"
            encrypted = aes.encrypt(plaintext)
            decrypted = aes.decrypt(encrypted)
            
            if decrypted == plaintext:
                self.add_result("AES encryption", "api", "passed", time.time() - start)
            else:
                self.add_result("AES encryption", "api", "failed", time.time() - start)
                
        except Exception as e:
            self.add_result("Encryption tests", "api", "error", time.time() - start, str(e))
        
        # Test API models
        start = time.time()
        try:
            from api.models import CarportRequest, CarportResponse, ResponseStatus
            
            request = CarportRequest(encrypted_data="test")
            response = CarportResponse(
                success=True,
                status=ResponseStatus.SUCCESS,
                message="Test"
            )
            
            self.add_result("API models", "api", "passed", time.time() - start)
            
        except Exception as e:
            self.add_result("API model tests", "api", "error", time.time() - start, str(e))
    
    def test_integration(self):
        """Test full system integration"""
        self.log("Testing full integration...", "INFO")
        
        start = time.time()
        try:
            from business.building_input import BuildingInput, BuildingType, CarportRoofType
            from business.structure_builder import CarportBuilder
            from services.encryption import AESEncryption
            
            # Create full pipeline
            building_input = BuildingInput(
                building_type=BuildingType.CARPORT,
                roof_type=CarportRoofType.FLAT,
                span=6000.0,
                length=6000.0,
                height=2400.0,
                bays=2
            )
            
            # Generate carport
            carport = CarportBuilder.create_carport(building_input)
            
            # Test encryption flow
            aes = AESEncryption("integration-key")
            data = {"span": building_input.span}
            encrypted = aes.encrypt_json(data)
            decrypted = aes.decrypt_json(encrypted)
            
            if carport and carport.main and decrypted["span"] == 6000.0:
                self.add_result("Full integration", "integration", "passed", time.time() - start)
            else:
                self.add_result("Full integration", "integration", "failed", time.time() - start)
                
        except Exception as e:
            self.add_result("Integration tests", "integration", "error", time.time() - start, str(e))
    
    def test_performance(self):
        """Test performance metrics"""
        self.log("Testing performance...", "INFO")
        
        start = time.time()
        try:
            from business.building_input import BuildingInput, BuildingType
            from business.structure_builder import CarportBuilder
            
            # Benchmark carport generation
            times = []
            for i in range(5):
                t_start = time.time()
                
                building_input = BuildingInput(
                    building_type=BuildingType.CARPORT,
                    span=6000.0,
                    length=6000.0,
                    height=2400.0
                )
                
                CarportBuilder.create_carport(building_input)
                times.append(time.time() - t_start)
            
            avg_time = sum(times) / len(times)
            
            details = {
                "average_time": avg_time,
                "min_time": min(times),
                "max_time": max(times),
                "iterations": len(times)
            }
            
            if avg_time < 1.0:  # Should complete in under 1 second
                self.add_result("Performance benchmark", "performance", "passed", avg_time, details=details)
            else:
                self.add_result("Performance benchmark", "performance", "failed", avg_time, details=details)
                
        except Exception as e:
            self.add_result("Performance tests", "performance", "error", time.time() - start, str(e))
    
    def test_security(self):
        """Test security features"""
        self.log("Testing security...", "INFO")
        
        start = time.time()
        try:
            from services.encryption import AESEncryption
            
            # Test encryption strength
            aes = AESEncryption("secure-key-12345")
            
            # Test different data types
            test_cases = [
                "Simple string",
                "Special chars: !@#$%^&*()",
                "Unicode: 你好世界 🌍",
                "Long text" * 100
            ]
            
            all_passed = True
            for test_data in test_cases:
                encrypted = aes.encrypt(test_data)
                decrypted = aes.decrypt(encrypted)
                if decrypted != test_data:
                    all_passed = False
                    break
            
            if all_passed:
                self.add_result("Encryption security", "security", "passed", time.time() - start)
            else:
                self.add_result("Encryption security", "security", "failed", time.time() - start)
                
        except Exception as e:
            self.add_result("Security tests", "security", "error", time.time() - start, str(e))
    
    def test_error_handling(self):
        """Test error handling"""
        self.log("Testing error handling...", "INFO")
        
        start = time.time()
        try:
            from business.building_input import BuildingInput
            
            # Test invalid input handling
            error_caught = False
            try:
                BuildingInput(span=-1000)  # Invalid negative span
            except (ValueError, TypeError):
                error_caught = True
            
            if error_caught:
                self.add_result("Error handling", "errors", "passed", time.time() - start)
            else:
                self.add_result("Error handling", "errors", "failed", time.time() - start)
                
        except Exception as e:
            self.add_result("Error handling tests", "errors", "error", time.time() - start, str(e))
    
    def add_result(self, name: str, category: str, status: str, duration: float, 
                   error: Optional[str] = None, details: Optional[Dict[str, Any]] = None):
        """Add a test result"""
        result = TestResult(
            name=name,
            category=category,
            status=status,
            duration=duration,
            error=error,
            details=details
        )
        self.results.append(result)
        
        # Log result
        symbol = {"passed": "✅", "failed": "❌", "skipped": "⏭️", "error": "💥"}.get(status, "❓")
        self.log(f"{symbol} {name} [{category}] - {duration:.3f}s", 
                 "INFO" if status == "passed" else "ERROR")
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report"""
        total_duration = self.end_time - self.start_time if self.end_time else 0
        
        # Calculate statistics
        by_status = {}
        by_category = {}
        
        for result in self.results:
            # By status
            by_status[result.status] = by_status.get(result.status, 0) + 1
            
            # By category
            if result.category not in by_category:
                by_category[result.category] = {"passed": 0, "failed": 0, "error": 0, "skipped": 0}
            by_category[result.category][result.status] += 1
        
        report = {
            "summary": {
                "total_tests": len(self.results),
                "passed": by_status.get("passed", 0),
                "failed": by_status.get("failed", 0),
                "errors": by_status.get("error", 0),
                "skipped": by_status.get("skipped", 0),
                "duration": total_duration,
                "timestamp": datetime.now().isoformat()
            },
            "by_category": by_category,
            "results": [asdict(r) for r in self.results],
            "environment": {
                "python_version": sys.version,
                "platform": sys.platform,
                "cwd": os.getcwd()
            }
        }
        
        return report
    
    def save_report(self, report: Dict[str, Any], filename: str = None):
        """Save test report to file"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"test_report_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2)
        
        self.log(f"Test report saved to {filename}", "INFO")
    
    def print_summary(self, report: Dict[str, Any]):
        """Print test summary"""
        print("\n" + "="*60)
        print("TEST SUMMARY")
        print("="*60)
        
        summary = report["summary"]
        print(f"Total Tests: {summary['total_tests']}")
        print(f"Passed: {summary['passed']} ✅")
        print(f"Failed: {summary['failed']} ❌")
        print(f"Errors: {summary['errors']} 💥")
        print(f"Skipped: {summary['skipped']} ⏭️")
        print(f"Duration: {summary['duration']:.2f} seconds")
        
        print("\nBy Category:")
        for category, stats in report["by_category"].items():
            total = sum(stats.values())
            passed_pct = (stats["passed"] / total * 100) if total > 0 else 0
            print(f"  {category}: {stats['passed']}/{total} ({passed_pct:.1f}% passed)")
        
        # Show failures
        failures = [r for r in report["results"] if r["status"] in ["failed", "error"]]
        if failures:
            print(f"\nFailures ({len(failures)}):")
            for failure in failures[:10]:  # Show first 10
                print(f"  - {failure['name']} [{failure['category']}]: {failure.get('error', 'Failed')}")
            if len(failures) > 10:
                print(f"  ... and {len(failures) - 10} more")
        
        print("="*60)


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="BIM Backend Test Facility")
    parser.add_argument("--category", help="Run specific test category")
    parser.add_argument("--save-report", action="store_true", help="Save test report to file")
    parser.add_argument("--quiet", action="store_true", help="Reduce output verbosity")
    parser.add_argument("--performance", action="store_true", help="Run performance tests only")
    
    args = parser.parse_args()
    
    # Create test facility
    facility = TestFacility(verbose=not args.quiet)
    
    # Run tests
    if args.category:
        # Run specific category
        method_name = f"test_{args.category}"
        if hasattr(facility, method_name):
            facility.start_time = time.time()
            getattr(facility, method_name)()
            facility.end_time = time.time()
        else:
            print(f"Unknown test category: {args.category}")
            sys.exit(1)
    elif args.performance:
        # Run performance tests only
        facility.start_time = time.time()
        facility.test_performance()
        facility.end_time = time.time()
    else:
        # Run all tests
        facility.run_all_tests()
    
    # Generate report
    report = facility.generate_report()
    
    # Save report if requested
    if args.save_report:
        facility.save_report(report)
    
    # Print summary
    facility.print_summary(report)
    
    # Exit with appropriate code
    if report["summary"]["failed"] > 0 or report["summary"]["errors"] > 0:
        sys.exit(1)
    else:
        sys.exit(0)


if __name__ == "__main__":
    main()