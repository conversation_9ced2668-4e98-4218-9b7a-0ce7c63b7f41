#!/usr/bin/env python3
"""Simple test of geometry methods without imports."""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Direct imports to avoid numpy dependency
from src.geometry.primitives import Vec2
from src.geometry.lines import Line1, Line2
from src.geometry.helpers import Geo

print("Testing new geometry methods...")
print("=" * 40)

# Test 1: ln_up/ln_down
print("\n1. Testing ln_up/ln_down:")
line = Line2(Vec2(0, 0), Vec2(10, 0))
up = Geo.ln_up_line(line, 5)
down = Geo.ln_down_line(line, 5)
print(f"   Original line: {line}")
print(f"   Up 5: {up}")
print(f"   Down 5: {down}")

# Test 2: poly_offset
print("\n2. Testing poly_offset:")
square = [Vec2(0, 0), Vec2(10, 0), Vec2(10, 10), Vec2(0, 10)]
inset = Geo.poly_offset(square, -2)
print(f"   Square vertices: {len(square)}")
print(f"   Inset vertices: {len(inset)}")
print(f"   First inset point: {inset[0]}")

# Test 3: ln_range
print("\n3. Testing ln_range:")
line1 = Geo.ln_range(5.0, 10.0)
print(f"   Center=5, Size=10: {line1}")

# Test 4: ln_left_right
print("\n4. Testing ln_left_right:")
line = Line2(Vec2(10, 5), Vec2(0, 5))
ordered = Geo.ln_left_right(line)
print(f"   Original: start=({line.start.x},{line.start.y}) end=({line.end.x},{line.end.y})")
print(f"   Ordered: start=({ordered.start.x},{ordered.start.y}) end=({ordered.end.x},{ordered.end.y})")

print("\n" + "=" * 40)
print("All tests completed successfully!")