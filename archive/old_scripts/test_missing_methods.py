#!/usr/bin/env python3
"""Test script for newly implemented geometry methods."""

from src.geometry.primitives import Vec2
from src.geometry.lines import Line2
from src.geometry.boxes import Box2
from src.geometry.helpers import <PERSON>eo


def test_ln_up_down():
    """Test ln_up and ln_down methods."""
    print("Testing ln_up/ln_down...")
    
    # Horizontal line
    line_h = Line2(Vec2(0, 0), Vec2(10, 0))
    up_h = Geo.ln_up_line(line_h, 5)
    down_h = Geo.ln_down_line(line_h, 5)
    
    print(f"  Horizontal line: {line_h}")
    print(f"  Up 5: {up_h}")
    print(f"  Down 5: {down_h}")
    
    # Vertical line
    line_v = Line2(Vec2(0, 0), Vec2(0, 10))
    up_v = Geo.ln_up_line(line_v, 5)
    down_v = Geo.ln_down_line(line_v, 5)
    
    print(f"  Vertical line: {line_v}")
    print(f"  Up 5: {up_v}")
    print(f"  Down 5: {down_v}")
    
    # Diagonal line
    line_d = Line2(Vec2(0, 0), Vec2(10, 10))
    up_d = Geo.ln_up_line(line_d, 5)
    down_d = Geo.ln_down_line(line_d, 5)
    
    print(f"  Diagonal line: {line_d}")
    print(f"  Up 5: {up_d}")
    print(f"  Down 5: {down_d}")
    print()


def test_poly_offset():
    """Test polygon offset method."""
    print("Testing poly_offset...")
    
    # Square
    square = [
        Vec2(0, 0),
        Vec2(10, 0),
        Vec2(10, 10),
        Vec2(0, 10)
    ]
    
    # Inset by 2
    inset = Geo.poly_offset(square, -2)
    print(f"  Original square: {[str(p) for p in square]}")
    print(f"  Inset by 2: {[str(p) for p in inset]}")
    
    # Outset by 2
    outset = Geo.poly_offset(square, 2)
    print(f"  Outset by 2: {[str(p) for p in outset]}")
    print()


def test_ln_offset_polyline():
    """Test polyline offset method."""
    print("Testing ln_offset_polyline...")
    
    # L-shaped polyline
    polyline = [
        Vec2(0, 0),
        Vec2(10, 0),
        Vec2(10, 10)
    ]
    
    offset_left = Geo.ln_offset_polyline(polyline, -2)
    offset_right = Geo.ln_offset_polyline(polyline, 2)
    
    print(f"  Original polyline: {[str(p) for p in polyline]}")
    print(f"  Offset left by 2: {[str(p) for p in offset_left]}")
    print(f"  Offset right by 2: {[str(p) for p in offset_right]}")
    print()


def test_ln_ordering():
    """Test line ordering methods."""
    print("Testing ln_low_high and ln_left_right...")
    
    lines = [
        Line2(Vec2(5, 5), Vec2(0, 0)),
        Line2(Vec2(0, 5), Vec2(10, 5)),
        Line2(Vec2(5, 0), Vec2(5, 10))
    ]
    
    for line in lines:
        low_high = Geo.ln_low_high(line)
        left_right = Geo.ln_left_right(line)
        
        print(f"  Original: {line}")
        print(f"  Low-High: {low_high}")
        print(f"  Left-Right: {left_right}")
        print()


def test_ln_extend_to_line():
    """Test line extension to intersection."""
    print("Testing ln_extend_to_line...")
    
    line1 = Line2(Vec2(0, 0), Vec2(5, 0))
    line2 = Line2(Vec2(10, -5), Vec2(10, 5))
    
    extended = Geo.ln_extend_to_line(line1, line2)
    
    print(f"  Line 1: {line1}")
    print(f"  Line 2: {line2}")
    print(f"  Extended: {extended}")
    print()


def test_ln_range():
    """Test Line1 creation from center and size."""
    print("Testing ln_range...")
    
    line1 = Geo.ln_range(5.0, 10.0)
    line2 = Geo.ln_range(0.0, 20.0)
    
    print(f"  Center=5, Size=10: {line1}")
    print(f"  Center=0, Size=20: {line2}")
    print()


def main():
    """Run all tests."""
    print("=" * 60)
    print("Testing newly implemented geometry methods")
    print("=" * 60)
    print()
    
    test_ln_up_down()
    test_poly_offset()
    test_ln_offset_polyline()
    test_ln_ordering()
    test_ln_extend_to_line()
    test_ln_range()
    
    print("=" * 60)
    print("All tests completed!")
    print("=" * 60)


if __name__ == "__main__":
    main()