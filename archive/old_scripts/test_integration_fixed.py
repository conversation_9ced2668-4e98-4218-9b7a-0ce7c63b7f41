#!/usr/bin/env python3
"""
Fixed Integration Test Suite for Tasks 1-5

This version properly handles package imports and tests all components.
"""

import sys
import os

# Add src to Python path before any imports
project_root = os.path.dirname(os.path.abspath(__file__))
src_path = os.path.join(project_root, 'src')
sys.path.insert(0, src_path)

import json
import traceback
from datetime import datetime
from typing import Dict, List, Tuple, Any

# Test result tracking
test_results = {
    "passed": 0,
    "failed": 0,
    "errors": [],
    "summary": {}
}


def print_header(text: str):
    """Print a formatted header"""
    print(f"\n{'='*60}")
    print(f"  {text}")
    print(f"{'='*60}")


def print_subheader(text: str):
    """Print a formatted subheader"""
    print(f"\n--- {text} ---")


def test_result(test_name: str, passed: bool, error: str = None):
    """Record and print test result"""
    if passed:
        test_results["passed"] += 1
        print(f"✅ {test_name}")
    else:
        test_results["failed"] += 1
        print(f"❌ {test_name}")
        if error:
            print(f"   Error: {error}")
            test_results["errors"].append(f"{test_name}: {error}")


def test_task1_geometry():
    """Test Task 1: Geometry primitives"""
    print_header("TASK 1: GEOMETRY PRIMITIVES")
    
    # Test Vec2
    print_subheader("Testing Vec2")
    try:
        from geometry.primitives import Vec2
        
        # Basic operations
        v1 = Vec2(3.0, 4.0)
        v2 = Vec2(1.0, 2.0)
        
        # Length
        assert abs(v1.length() - 5.0) < 0.0001, f"Vec2 length incorrect: {v1.length()}"
        test_result("Vec2.length()", True)
        
        # Dot product
        dot = Vec2.dot(v1, v2)
        assert abs(dot - 11.0) < 0.0001, f"Vec2 dot product incorrect: {dot}"
        test_result("Vec2.dot()", True)
        
        # Normalization
        normal = Vec2.normal(v1)
        assert abs(normal.length() - 1.0) < 0.0001, f"Vec2 normalization incorrect"
        test_result("Vec2.normal()", True)
        
        # Test operators
        v3 = v1 + v2
        assert v3.x == 4.0 and v3.y == 6.0, f"Vec2 addition incorrect"
        test_result("Vec2 addition operator", True)
        
    except Exception as e:
        test_result("Vec2 import/basic ops", False, str(e))
        traceback.print_exc()
    
    # Test Vec3
    print_subheader("Testing Vec3")
    try:
        from geometry.primitives import Vec3
        
        v1 = Vec3(1.0, 2.0, 3.0)
        v2 = Vec3(4.0, 5.0, 6.0)
        
        # Cross product
        cross = Vec3.cross(Vec3.unit_x(), Vec3.unit_y())
        assert cross.x == 0 and cross.y == 0 and cross.z == 1, f"Vec3 cross product incorrect"
        test_result("Vec3.cross()", True)
        
        # Distance (if method exists)
        try:
            # Try subtract and length
            diff = v2 - v1
            dist = diff.length()
            assert abs(dist - 5.196152422706632) < 0.0001, f"Vec3 distance incorrect: {dist}"
            test_result("Vec3 distance calculation", True)
        except:
            test_result("Vec3 distance calculation", False, "Method not implemented")
        
    except Exception as e:
        test_result("Vec3 operations", False, str(e))
    
    # Test Mat4
    print_subheader("Testing Mat4")
    try:
        from geometry.matrix import Mat4
        from geometry.helpers import Geo
        
        # Rotation matrix
        m = Mat4.create_rotation_z(Geo.DEG45)
        test_result("Mat4.create_rotation_z()", True)
        
        # Translation
        m2 = Mat4.create_translation(10, 20, 30)
        test_result("Mat4.create_translation()", True)
        
        # Matrix multiplication
        combined = m2 * m
        test_result("Mat4 multiplication", True)
        
    except Exception as e:
        test_result("Mat4 operations", False, str(e))
    
    # Test geometric helpers
    print_subheader("Testing Geometric Helpers")
    try:
        from geometry.lines import Line2, Line3
        from geometry.boxes import Box2, Box3
        from geometry.plane import Plane3
        
        # Line intersection
        line1 = Line2(Vec2(0, 0), Vec2(4, 4))
        line2 = Line2(Vec2(0, 4), Vec2(4, 0))
        inters = Line2.inters(line1, line2)
        assert inters is not None, "Line intersection failed"
        test_result("Line2.inters()", True)
        
        # Box operations - fix the call
        try:
            box = Box2(Vec2(0, 0), Vec2(10, 10))
            assert box.contains(Vec2(5, 5)), "Box containment failed"
            test_result("Box2.contains()", True)
        except:
            # Try alternative constructor
            box = Box2.from_center(Vec2(5, 5), Vec2(10, 10))
            assert box.contains(Vec2(5, 5)), "Box containment failed"
            test_result("Box2.contains() via from_center", True)
        
        # Plane creation
        plane = Plane3.from_three_points(
            Vec3(0, 0, 0),
            Vec3(1, 0, 0),
            Vec3(0, 1, 0)
        )
        test_result("Plane3.from_three_points()", True)
        
    except Exception as e:
        test_result("Geometric helpers", False, str(e))


def test_task2_materials():
    """Test Task 2: Materials system"""
    print_header("TASK 2: MATERIALS SYSTEM")
    
    # Test material imports
    print_subheader("Testing Material Classes")
    try:
        from materials.base import BracketMaterial, FrameMaterial, FrameMaterialType
        from materials.visual import ColorMaterial, CladdingMaterial
        from materials.structural import FootingMaterial, FastenerMaterial
        from materials.accessories import FlashingMaterial, DownpipeMaterial
        
        # Test FrameMaterial
        frame = FrameMaterial(
            name="C15024",
            material_type=FrameMaterialType.C,
            width=150.0,
            height=24.0,
            thickness=1.5
        )
        assert frame.web == 24.0, "FrameMaterial web property incorrect"
        assert frame.flange == 150.0, "FrameMaterial flange property incorrect"
        test_result("FrameMaterial properties", True)
        
        # Test ColorMaterial
        color = ColorMaterial(
            name="Colorbond Monument",
            rgb_hex="#5A5956",
            finish="Colorbond"
        )
        assert color.r == 90 and color.g == 89 and color.b == 86, "Color RGB conversion failed"
        test_result("ColorMaterial RGB conversion", True)
        
        # Test CladdingMaterial
        cladding = CladdingMaterial(
            name="Trimdek",
            profile="Trimdeck",
            min_pitch=2.0,
            coverage=760.0,
            thickness=0.42
        )
        test_result("CladdingMaterial creation", True)
        
    except Exception as e:
        test_result("Material system imports", False, str(e))
        traceback.print_exc()
    
    # Test material helpers
    print_subheader("Testing Material Helpers")
    try:
        from materials.helpers import MaterialLibrary
        
        # Test material library
        lib = MaterialLibrary.get_default()
        
        # Check frame materials
        frames = lib.get_frame_materials()
        assert len(frames) > 0, "No frame materials found"
        test_result("MaterialLibrary frame materials", True)
        
        # Check color materials
        colors = lib.get_color_materials()
        assert len(colors) > 0, "No color materials found"
        test_result("MaterialLibrary color materials", True)
        
    except Exception as e:
        test_result("Material helpers", False, str(e))


def test_task3_bim_model():
    """Test Task 3: BIM data model"""
    print_header("TASK 3: BIM DATA MODEL")
    
    print_subheader("Testing BIM Structure Classes")
    try:
        from bim.shed_bim import ShedBim, ShedBimPartMain
        from bim.wall_roof import ShedBimSide, ShedBimRoof
        from bim.components import ShedBimColumn, ShedBimRafter, ShedBimSection
        from geometry.primitives import Vec3
        
        # Create basic structure
        main = ShedBimPartMain(
            roof_type="Flat",
            roof_left=ShedBimRoof(cladding_material="Colorbond", cladding_reversed=False),
            side_left=ShedBimSide(),
            side_right=ShedBimSide()
        )
        
        # Add columns
        main.side_left.columns.append(
            ShedBimColumn(
                base=Vec3(0, 0, 0),
                top=Vec3(0, 0, 2400),
                frame_material="C15024"
            )
        )
        test_result("ShedBim structure creation", True)
        
        # Test component methods
        sides = main.get_sides()
        assert len(sides) == 2, "get_sides() returned wrong count"
        test_result("ShedBimPartMain.get_sides()", True)
        
        roofs = main.get_roofs()
        assert len(roofs) == 1, "get_roofs() returned wrong count"
        test_result("ShedBimPartMain.get_roofs()", True)
        
    except Exception as e:
        test_result("BIM model structures", False, str(e))
        traceback.print_exc()
    
    # Test BIM components
    print_subheader("Testing BIM Components")
    try:
        from bim.components import ShedBimFooting, ShedBimBracket
        from geometry.primitives import Vec3
        
        # Create footing
        footing = ShedBimFooting(
            position=Vec3(0, 0, -300),
            footing_type="bored",
            diameter=450.0,
            depth=600.0
        )
        test_result("ShedBimFooting creation", True)
        
        # Create bracket
        bracket = ShedBimBracket(
            bracket_material="Standard",
            position=Vec3(0, 0, 2400),
            rotation=0.0
        )
        test_result("ShedBimBracket creation", True)
        
    except Exception as e:
        test_result("BIM components", False, str(e))


def test_task4_business_logic():
    """Test Task 4: Business logic"""
    print_header("TASK 4: BUSINESS LOGIC")
    
    print_subheader("Testing BuildingInput")
    try:
        from business.building_input import BuildingInput, BuildingType, CarportRoofType
        
        # Create building input
        building_input = BuildingInput(
            building_type=BuildingType.CARPORT,
            name="Test Carport",
            roof_type=CarportRoofType.FLAT,
            bays=1,
            span=3000.0,
            length=3000.0,
            height=2400.0,
            wind_speed=32,
            slab=True,
            slab_thickness=100.0,
            soil="SAND"
        )
        
        # Test overhang setter
        building_input.overhang = 800.0  # Should be clamped to 600 for 3m span
        assert building_input.overhang == 600.0, f"Overhang not clamped correctly: {building_input.overhang}"
        test_result("BuildingInput overhang validation", True)
        
    except Exception as e:
        test_result("BuildingInput", False, str(e))
        traceback.print_exc()
    
    print_subheader("Testing Engineering Service")
    try:
        from business.engineering import EngData, MockEngineeringService
        
        # Test EngData
        eng_data = EngData(
            ENG_RAFTER="C15024",
            ENG_PURLINSIZE="TH064100",
            ENG_PURLINROW=3,
            ENG_COLUMN="SHS07507525",
            ENG_APEXBRACE=None,
            ENG_FOOTINGTYPE="block",
            ENG_FOOTINGDIA="300",
            ENG_FOOTINGDEPTH="300"
        )
        test_result("EngData creation", True)
        
        # Test mock service
        mock_service = MockEngineeringService()
        import asyncio
        
        async def test_mock():
            result = await mock_service.validate_design(building_input)
            return result is not None
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(test_mock())
        test_result("MockEngineeringService", result)
        
    except Exception as e:
        test_result("Engineering service", False, str(e))
    
    print_subheader("Testing CarportBuilder")
    try:
        from business.structure_builder import CarportBuilder
        
        # Create carport without engineering data
        carport = CarportBuilder.create_carport(building_input)
        assert carport is not None, "CarportBuilder returned None"
        assert carport.main is not None, "Carport has no main structure"
        test_result("CarportBuilder.create_carport()", True)
        
        # Test with engineering data
        carport_eng = CarportBuilder.create_carport(building_input, eng_data)
        assert carport_eng is not None, "CarportBuilder with eng data returned None"
        test_result("CarportBuilder with engineering data", True)
        
    except Exception as e:
        test_result("CarportBuilder", False, str(e))
        traceback.print_exc()


def test_task5_api():
    """Test Task 5: API layer"""
    print_header("TASK 5: API LAYER")
    
    print_subheader("Testing Encryption Service")
    try:
        from services.encryption import AESEncryption
        
        # Test encryption/decryption
        aes = AESEncryption("test-key-12345")
        
        # Test string encryption
        plaintext = "Hello, BIM Backend!"
        encrypted = aes.encrypt(plaintext)
        decrypted = aes.decrypt(encrypted)
        assert decrypted == plaintext, f"Decryption failed: {decrypted} != {plaintext}"
        test_result("AES encryption/decryption", True)
        
        # Test JSON encryption
        test_data = {"building_type": "CARPORT", "span": 3000.0}
        encrypted_json = aes.encrypt_json(test_data)
        decrypted_json = aes.decrypt_json(encrypted_json)
        assert decrypted_json == test_data, "JSON encryption/decryption failed"
        test_result("AES JSON encryption", True)
        
    except Exception as e:
        test_result("Encryption service", False, str(e))
        traceback.print_exc()
    
    print_subheader("Testing API Models")
    try:
        from api.models import CarportRequest, CarportResponse, ResponseStatus
        
        # Test request model
        request = CarportRequest(encrypted_data="base64_encoded_data")
        test_result("CarportRequest model", True)
        
        # Test response model
        response = CarportResponse(
            success=True,
            status=ResponseStatus.SUCCESS,
            message="Test successful",
            file_url="/api/download/test123"
        )
        test_result("CarportResponse model", True)
        
    except Exception as e:
        test_result("API models", False, str(e))
    
    print_subheader("Testing API Application")
    try:
        from api.main import app
        from api.endpoints import router
        
        # Check app instance
        assert app is not None, "FastAPI app not created"
        test_result("FastAPI app creation", True)
        
        # Check routes
        routes = [route.path for route in app.routes]
        assert "/api/health" in str(routes), "Health endpoint not found"
        test_result("API routes registration", True)
        
    except Exception as e:
        test_result("API application", False, str(e))


def test_full_integration():
    """Test full integration from input to API response"""
    print_header("FULL INTEGRATION TEST")
    
    try:
        from business.building_input import BuildingInput, BuildingType, CarportRoofType
        from business.structure_builder import CarportBuilder
        from services.encryption import AESEncryption
        import json
        
        print_subheader("Creating Building Input")
        building_input = BuildingInput(
            building_type=BuildingType.CARPORT,
            name="Integration Test Carport",
            roof_type=CarportRoofType.FLAT,
            validate_engineering=False,
            bays=2,
            span=6000.0,
            length=6000.0,
            height=2400.0,
            wind_speed=32,
            pitch=0.0,
            slab=True,
            slab_thickness=100.0,
            soil="SAND",
            overhang=600.0
        )
        test_result("Building input creation", True)
        
        print_subheader("Generating Carport Model")
        carport = CarportBuilder.create_carport(building_input)
        
        # Verify structure
        assert carport.main is not None, "No main structure"
        assert len(carport.main.side_left.columns) > 0, "No columns generated"
        assert carport.main.roof_left is not None, "No roof generated"
        test_result("Carport generation", True)
        
        print_subheader("Testing Encryption Flow")
        aes = AESEncryption("test-integration-key")
        
        # Encrypt building input
        input_dict = {
            "building_type": building_input.building_type.value,
            "name": building_input.name,
            "roof_type": building_input.roof_type.value,
            "bays": building_input.bays,
            "span": building_input.span,
            "length": building_input.length,
            "height": building_input.height
        }
        
        encrypted = aes.encrypt_json(input_dict)
        decrypted = aes.decrypt_json(encrypted)
        assert decrypted["span"] == 6000.0, "Encryption round-trip failed"
        test_result("End-to-end encryption", True)
        
        print_subheader("Summary")
        print(f"Generated carport with:")
        print(f"  - Main structure: {carport.main.roof_type} roof")
        print(f"  - Columns: {len(carport.main.side_left.columns) + len(carport.main.side_right.columns)}")
        print(f"  - Dimensions: {building_input.span}mm x {building_input.length}mm x {building_input.height}mm")
        
        test_result("Full integration test", True)
        
    except Exception as e:
        test_result("Full integration", False, str(e))
        traceback.print_exc()


def check_dependencies():
    """Check if all required dependencies are available"""
    print_header("DEPENDENCY CHECK")
    
    dependencies = {
        "numpy": "Core numerical operations",
        "scipy": "Scientific computing",
        "triangle": "Triangulation",
        "shapely": "Geometric operations",
        "pyclipper": "Polygon clipping",
        "fastapi": "API framework",
        "pydantic": "Data validation",
        "cryptography": "Encryption",
        "httpx": "HTTP client"
    }
    
    missing = []
    for module, description in dependencies.items():
        try:
            __import__(module)
            test_result(f"{module} ({description})", True)
        except ImportError:
            test_result(f"{module} ({description})", False, "Not installed")
            missing.append(module)
    
    if missing:
        print(f"\nMissing dependencies: {', '.join(missing)}")
        print("Install with: pip install -r requirements.txt")
    
    return len(missing) == 0


def main():
    """Run all integration tests"""
    print(f"\n{'#'*60}")
    print(f"  BIM BACKEND INTEGRATION TEST SUITE")
    print(f"  Testing Tasks 1-5 Implementation")
    print(f"  {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"{'#'*60}")
    
    # Check dependencies first
    deps_ok = check_dependencies()
    
    if deps_ok:
        # Run all tests
        test_task1_geometry()
        test_task2_materials()
        test_task3_bim_model()
        test_task4_business_logic()
        test_task5_api()
        test_full_integration()
    else:
        print("\n⚠️  Cannot run all tests due to missing dependencies")
    
    # Print summary
    print(f"\n{'='*60}")
    print(f"  TEST SUMMARY")
    print(f"{'='*60}")
    print(f"Total Tests: {test_results['passed'] + test_results['failed']}")
    print(f"Passed: {test_results['passed']} ✅")
    print(f"Failed: {test_results['failed']} ❌")
    
    if test_results['errors']:
        print(f"\nErrors:")
        for error in test_results['errors'][:10]:  # Show first 10 errors
            print(f"  - {error}")
        if len(test_results['errors']) > 10:
            print(f"  ... and {len(test_results['errors']) - 10} more")
    
    print(f"\n{'='*60}")
    
    # Return success if all tests passed
    return test_results['failed'] == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)