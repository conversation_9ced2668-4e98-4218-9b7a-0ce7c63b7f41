#!/usr/bin/env python3
"""
API Test Script

Simple script to test the BIM Backend API endpoints.
"""

import requests
import json
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from services.encryption import AESEncryption


def test_health_check(base_url: str):
    """Test the health check endpoint"""
    print("\n=== Testing Health Check ===")
    response = requests.get(f"{base_url}/api/health")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    return response.status_code == 200


def test_sample_request(base_url: str):
    """Test getting a sample request"""
    print("\n=== Testing Sample Request ===")
    response = requests.get(f"{base_url}/api/carport/sample-request")
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Sample payload: {json.dumps(data['decrypted_payload'], indent=2)}")
        print(f"Encrypted data length: {len(data['encrypted_request']['encrypted_data'])}")
    return response.status_code == 200


def test_carport_creation(base_url: str, encryption_key: str):
    """Test creating a carport"""
    print("\n=== Testing Carport Creation ===")
    
    # Create sample building input
    building_input = {
        "building_type": "CARPORT",
        "name": "Test Carport",
        "roof_type": "FLAT",
        "validate_engineering": False,
        "bays": 1,
        "span": 3000.0,
        "length": 3000.0,
        "height": 2400.0,
        "wind_speed": 32,
        "pitch": 0.0,
        "slab": True,
        "slab_thickness": 100.0,
        "soil": "SAND",
        "overhang": 300.0
    }
    
    # Encrypt the payload
    aes = AESEncryption(encryption_key)
    encrypted_data = aes.encrypt(json.dumps(building_input))
    
    # Make request
    request_data = {"encrypted_data": encrypted_data}
    response = requests.post(
        f"{base_url}/api/carport/create",
        json=request_data,
        headers={"Content-Type": "application/json"}
    )
    
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    
    if response.status_code == 200:
        data = response.json()
        if data.get("file_url"):
            # Test download
            print(f"\nTesting file download from: {data['file_url']}")
            download_response = requests.get(f"{base_url}{data['file_url']}")
            print(f"Download status: {download_response.status_code}")
            if download_response.status_code == 200:
                print(f"Downloaded {len(download_response.content)} bytes")
    
    return response.status_code == 200


def test_invalid_encryption(base_url: str):
    """Test with invalid encrypted data"""
    print("\n=== Testing Invalid Encryption ===")
    
    request_data = {"encrypted_data": "invalid_base64_data!!!"}
    response = requests.post(
        f"{base_url}/api/carport/create",
        json=request_data,
        headers={"Content-Type": "application/json"}
    )
    
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    return response.status_code == 400


def main():
    """Run all tests"""
    # Configuration
    base_url = os.getenv("API_BASE_URL", "http://localhost:8000")
    encryption_key = os.getenv("API_ENCRYPTION_KEY", "development-key-change-in-production")
    
    print(f"Testing API at: {base_url}")
    print(f"Using encryption key: {'[FROM ENV]' if 'API_ENCRYPTION_KEY' in os.environ else '[DEFAULT DEV KEY]'}")
    
    # Run tests
    tests_passed = 0
    tests_total = 0
    
    tests = [
        ("Health Check", lambda: test_health_check(base_url)),
        ("Sample Request", lambda: test_sample_request(base_url)),
        ("Carport Creation", lambda: test_carport_creation(base_url, encryption_key)),
        ("Invalid Encryption", lambda: test_invalid_encryption(base_url))
    ]
    
    for test_name, test_func in tests:
        tests_total += 1
        try:
            if test_func():
                tests_passed += 1
                print(f"\n✅ {test_name} PASSED")
            else:
                print(f"\n❌ {test_name} FAILED")
        except Exception as e:
            print(f"\n❌ {test_name} ERROR: {e}")
    
    print(f"\n{'='*50}")
    print(f"Tests passed: {tests_passed}/{tests_total}")
    
    return tests_passed == tests_total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)