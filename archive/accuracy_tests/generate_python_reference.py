"""Generate reference data from Python calculations for accuracy verification"""

import sys
import json
import math
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from src.geometry import Vec2, Vec3, Mat4, Box3, Triangle
import decimal

# Set high precision for calculations
decimal.getcontext().prec = 50

def generate_accurate_vector_tests():
    """Generate high-precision vector test data."""
    tests = []
    
    # Test 1: Vector2D operations
    v1 = Vec2(3.14159, 2.71828)
    v2 = Vec2(1.41421, 1.73205)
    
    test1 = {
        "TestName": "Vector2D_Operations",
        "V1": {"X": v1.x, "Y": v1.y},
        "V2": {"X": v2.x, "Y": v2.y},
        "DotProduct": Vec2.dot(v1, v2),
        "CrossProduct": Vec2.cross(v1, v2),
        "Distance": Vec2.distance(v1, v2),
        "Normalized": {
            "X": Vec2.normal(v1).x,
            "Y": Vec2.normal(v1).y
        },
        "Angle": v1.angle(),
        "Interpolated50": {
            "X": (v1 + (v2 - v1) * 0.5).x,
            "Y": (v1 + (v2 - v1) * 0.5).y
        }
    }
    tests.append(test1)
    
    # Test 2: Vector3D operations
    v3_1 = Vec3(1.23456789, 9.87654321, 5.55555555)
    v3_2 = Vec3(3.33333333, 7.77777777, 2.22222222)
    
    cross3 = Vec3.cross(v3_1, v3_2)
    norm3 = Vec3.normal(v3_1)
    
    test2 = {
        "TestName": "Vector3D_Operations",
        "V1": {"X": v3_1.x, "Y": v3_1.y, "Z": v3_1.z},
        "V2": {"X": v3_2.x, "Y": v3_2.y, "Z": v3_2.z},
        "DotProduct": Vec3.dot(v3_1, v3_2),
        "CrossProduct": {
            "X": cross3.x,
            "Y": cross3.y,
            "Z": cross3.z
        },
        "Distance": Vec3.distance(v3_1, v3_2),
        "Normalized": {
            "X": norm3.x,
            "Y": norm3.y,
            "Z": norm3.z
        }
    }
    tests.append(test2)
    
    return tests

def generate_accurate_matrix_tests():
    """Generate high-precision matrix test data."""
    tests = []
    
    # Test 1: Combined transformation
    translation = Mat4.translation(10.5, -20.3, 15.7)
    rotation_x = Mat4.rotation_x(math.pi / 6)  # 30 degrees
    rotation_y = Mat4.rotation_y(math.pi / 4)  # 45 degrees
    rotation_z = Mat4.rotation_z(math.pi / 3)  # 60 degrees
    scale = Mat4.scale(2.5, 1.5, 3.0)
    
    combined = scale * rotation_x * rotation_y * rotation_z * translation
    test_point = Vec3(1.0, 2.0, 3.0)
    transformed = combined.transform_point(test_point)
    
    test1 = {
        "TestName": "Matrix_Combined_Transform",
        "InputPoint": {"X": test_point.x, "Y": test_point.y, "Z": test_point.z},
        "TransformationOrder": ["Translate", "RotateZ", "RotateY", "RotateX", "Scale"],
        "FinalMatrix": combined.m,
        "OutputPoint": {
            "X": transformed.x,
            "Y": transformed.y,
            "Z": transformed.z
        }
    }
    tests.append(test1)
    
    return tests

def generate_accurate_real_world_tests():
    """Generate high-precision real-world test data."""
    tests = []
    
    # Test 1: Gable roof rafter positions
    span = 6000.0
    length = 9000.0
    height = 2700.0
    pitch_deg = 15.0
    pitch_rad = math.radians(pitch_deg)
    rafter_spacing = 900.0
    
    ridge_height = height + (span / 2) * math.tan(pitch_rad)
    roof_area = 2 * length * (span / 2) / math.cos(pitch_rad)
    
    rafter_positions = []
    x = 0.0
    while x <= length:
        left_end = Vec3(x, 0, height)
        right_end = Vec3(x, span, height)
        apex = Vec3(x, span / 2, height + (span / 2) * math.tan(pitch_rad))
        
        length_val = Vec3.distance(left_end, apex)
        angle = math.atan2(apex.z - left_end.z, apex.y - left_end.y)
        
        rafter_positions.append({
            "X": x,
            "LeftEnd": {"X": left_end.x, "Y": left_end.y, "Z": left_end.z},
            "RightEnd": {"X": right_end.x, "Y": right_end.y, "Z": right_end.z},
            "Apex": {"X": apex.x, "Y": apex.y, "Z": apex.z},
            "Length": length_val,
            "Angle": angle
        })
        x += rafter_spacing
    
    test1 = {
        "Name": "Gable_Roof_Rafter_Positions",
        "Description": "Calculate exact 3D positions of rafter ends for a gable roof",
        "Inputs": {
            "Span": span,
            "Length": length,
            "Height": height,
            "Pitch": pitch_deg,
            "RafterSpacing": rafter_spacing
        },
        "Outputs": {
            "RafterCount": len(rafter_positions),
            "RafterPositions": rafter_positions,
            "RidgeHeight": ridge_height,
            "RoofArea": roof_area
        }
    }
    tests.append(test1)
    
    # Test 2: Column footing placement
    bay_count = 3
    bay_size = 3000.0
    span = 6000.0
    footing_diameter = 600.0
    footing_depth = 900.0
    
    column_positions = []
    total_volume = 0.0
    
    for bay in range(bay_count + 1):
        x = bay * bay_size
        
        # Calculate footing volume (cylinder)
        radius = footing_diameter / 2
        volume = math.pi * (radius ** 2) * footing_depth / 1e9  # m³
        concrete_kg = volume * 2400
        
        column_positions.append({
            "Left": {"X": float(x), "Y": 0.0, "Z": 0.0},
            "Right": {"X": float(x), "Y": span, "Z": 0.0},
            "FootingVolume": volume,
            "ConcreteRequired": concrete_kg
        })
        total_volume += volume
    
    test2 = {
        "Name": "Column_Footing_Placement",
        "Description": "Calculate column positions and footing requirements",
        "Inputs": {
            "BayCount": bay_count,
            "BaySize": bay_size,
            "Span": span,
            "ColumnSize": 100.0,
            "FootingDiameter": footing_diameter,
            "FootingDepth": footing_depth
        },
        "Outputs": {
            "ColumnCount": (bay_count + 1) * 2,
            "ColumnPositions": column_positions,
            "TotalFootingVolume": total_volume * 2,  # Both sides
            "TotalConcreteKg": total_volume * 2 * 2400
        }
    }
    tests.append(test2)
    
    return tests

def save_json_with_precision(filepath, data):
    """Save JSON with high precision numbers."""
    
    def float_to_str(obj):
        if isinstance(obj, float):
            return format(obj, '.15g')
        elif isinstance(obj, dict):
            return {k: float_to_str(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [float_to_str(v) for v in obj]
        return obj
    
    # Convert floats to strings for precision
    data_str = float_to_str(data)
    
    with open(filepath, 'w') as f:
        json.dump(data_str, f, indent=2)

def main():
    """Generate all Python reference data."""
    output_dir = Path(__file__).parent / "reference_data" / "geometry_python"
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Generate vector tests
    vector_tests = generate_accurate_vector_tests()
    save_json_with_precision(output_dir / "vector_tests.json", vector_tests)
    print("Generated vector tests")
    
    # Generate matrix tests
    matrix_tests = generate_accurate_matrix_tests()
    save_json_with_precision(output_dir / "matrix_tests.json", matrix_tests)
    print("Generated matrix tests")
    
    # Generate real-world tests
    real_world_tests = generate_accurate_real_world_tests()
    save_json_with_precision(output_dir / "real_world_tests.json", real_world_tests)
    print("Generated real-world tests")
    
    print(f"\nReference data saved to: {output_dir}")

if __name__ == "__main__":
    main()