"""Debug geometry calculations to find accuracy issues"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from src.geometry import Vec2, Vec3, Mat4
import math

# Test Vec2 operations
v1 = Vec2(3.14159, 2.71828)
v2 = Vec2(1.41421, 1.73205)

print("Vec2 Operations:")
print(f"v1 = {v1}")
print(f"v2 = {v2}")

# Dot product: a.x*b.x + a.y*b.y
dot = Vec2.dot(v1, v2)
print(f"\nDot product: {dot}")
print(f"Manual calc: {v1.x * v2.x + v1.y * v2.y}")

# Cross product (2D): a.x*b.y - a.y*b.x
cross = Vec2.cross(v1, v2)
print(f"\nCross product: {cross}")
print(f"Manual calc: {v1.x * v2.y - v1.y * v2.x}")

# Distance
dist = Vec2.distance(v1, v2)
print(f"\nDistance: {dist}")
dx = v2.x - v1.x
dy = v2.y - v1.y
print(f"Manual calc: {math.sqrt(dx*dx + dy*dy)}")

# Normalized
norm = v1.normalized()
print(f"\nNormalized: {norm}")
length = v1.length()
print(f"Length: {length}")
print(f"Manual norm: ({v1.x/length}, {v1.y/length})")

# Test Vec3 operations
v3_1 = Vec3(1.23456789, 9.87654321, 5.55555555)
v3_2 = Vec3(3.33333333, 7.77777777, 2.22222222)

print("\n" + "="*50)
print("Vec3 Operations:")
print(f"v1 = {v3_1}")
print(f"v2 = {v3_2}")

# Dot product
dot3 = Vec3.dot(v3_1, v3_2)
print(f"\nDot product: {dot3}")
print(f"Manual calc: {v3_1.x * v3_2.x + v3_1.y * v3_2.y + v3_1.z * v3_2.z}")

# Cross product
cross3 = Vec3.cross(v3_1, v3_2)
print(f"\nCross product: {cross3}")
print(f"Manual X: {v3_1.y * v3_2.z - v3_1.z * v3_2.y}")
print(f"Manual Y: {v3_1.z * v3_2.x - v3_1.x * v3_2.z}")
print(f"Manual Z: {v3_1.x * v3_2.y - v3_1.y * v3_2.x}")

# Test matrix operations
print("\n" + "="*50)
print("Matrix Operations:")

# Create same transformations as C# reference
translation = Mat4.translation(10.5, -20.3, 15.7)
rotation_x = Mat4.rotation_x(math.pi / 6)  # 30 degrees
rotation_y = Mat4.rotation_y(math.pi / 4)  # 45 degrees
rotation_z = Mat4.rotation_z(math.pi / 3)  # 60 degrees
scale = Mat4.scale(2.5, 1.5, 3.0)

print(f"\nTranslation matrix:")
print(f"  Translation values: (10.5, -20.3, 15.7)")

# Test point transformation
test_pt = Vec3(1.0, 2.0, 3.0)
print(f"\nTest point: {test_pt}")

# Transform step by step
pt1 = translation.transform_point(test_pt)
print(f"After translation: {pt1}")

pt2 = rotation_z.transform_point(pt1)
print(f"After rotation Z: {pt2}")

pt3 = rotation_y.transform_point(pt2)
print(f"After rotation Y: {pt3}")

pt4 = rotation_x.transform_point(pt3)
print(f"After rotation X: {pt4}")

pt5 = scale.transform_point(pt4)
print(f"After scale: {pt5}")

# Combined transform
combined = scale * rotation_x * rotation_y * rotation_z * translation
result = combined.transform_point(test_pt)
print(f"\nCombined result: {result}")

# Check real-world calculation
print("\n" + "="*50)
print("Real-world test:")
span = 6000.0
height = 2700.0
pitch_rad = math.radians(15.0)

ridge_height = height + (span / 2) * math.tan(pitch_rad)
print(f"Ridge height: {ridge_height}")
print(f"Expected: 3504.0771513")

roof_area = 2 * 9000 * (span / 2) / math.cos(pitch_rad)
print(f"Roof area: {roof_area}")
print(f"Expected: 55908.27897384")