"""
Geometry Accuracy Tests - Compare Python implementation with C# reference data
Tests ensure numerical accuracy to 10 decimal places for real-world scenarios
"""

import sys
import json
import math
from pathlib import Path
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass

sys.path.append(str(Path(__file__).parent.parent))

from src.geometry import Vec2, Vec3, Mat4, Box3, Triangle
from src.geometry.helpers import Geo


@dataclass
class AccuracyResult:
    test_name: str
    passed: bool
    max_error: float
    errors: List[str]
    details: Dict[str, Any]


class GeometryAccuracyTester:
    def __init__(self):
        self.tolerance = 1e-10  # 10 decimal places
        self.results = []
        
    def load_reference_data(self, filename: str) -> List[Dict]:
        """Load C# generated reference data."""
        filepath = Path(__file__).parent / "reference_data" / "geometry" / filename
        with open(filepath, 'r') as f:
            return json.load(f)
    
    def compare_values(self, actual: float, expected: float, name: str) -> <PERSON><PERSON>[bool, float]:
        """Compare two floating point values."""
        if isinstance(expected, (int, float)) and isinstance(actual, (int, float)):
            error = abs(actual - expected)
            passed = error <= self.tolerance
            return passed, error
        else:
            return actual == expected, 0.0
    
    def test_vector_operations(self):
        """Test vector operations against C# reference."""
        print("Testing Vector Operations...")
        reference = self.load_reference_data("vector_tests.json")
        
        for test in reference:
            result = AccuracyResult(
                test_name=test["TestName"],
                passed=True,
                max_error=0.0,
                errors=[],
                details={}
            )
            
            if test["TestName"] == "Vector2D_Operations":
                # Create vectors
                v1 = Vec2(test["V1"]["X"], test["V1"]["Y"])
                v2 = Vec2(test["V2"]["X"], test["V2"]["Y"])
                
                # Test dot product
                dot = Vec2.dot(v1, v2)
                passed, error = self.compare_values(dot, test["DotProduct"], "DotProduct")
                if not passed:
                    result.errors.append(f"DotProduct: expected {test['DotProduct']}, got {dot}")
                result.max_error = max(result.max_error, error)
                result.passed &= passed
                
                # Test cross product
                cross = Vec2.cross(v1, v2)
                passed, error = self.compare_values(cross, test["CrossProduct"], "CrossProduct")
                if not passed:
                    result.errors.append(f"CrossProduct: expected {test['CrossProduct']}, got {cross}")
                result.max_error = max(result.max_error, error)
                result.passed &= passed
                
                # Test distance
                dist = Vec2.distance(v1, v2)
                passed, error = self.compare_values(dist, test["Distance"], "Distance")
                if not passed:
                    result.errors.append(f"Distance: expected {test['Distance']}, got {dist}")
                result.max_error = max(result.max_error, error)
                result.passed &= passed
                
                # Test normalization
                norm = Vec2.normal(v1)
                passed_x, error_x = self.compare_values(norm.x, test["Normalized"]["X"], "Normalized.X")
                passed_y, error_y = self.compare_values(norm.y, test["Normalized"]["Y"], "Normalized.Y")
                if not (passed_x and passed_y):
                    result.errors.append(f"Normalized: expected ({test['Normalized']['X']}, {test['Normalized']['Y']}), got ({norm.x}, {norm.y})")
                result.max_error = max(result.max_error, error_x, error_y)
                result.passed &= passed_x and passed_y
                
                # Test angle
                angle = v1.angle()
                passed, error = self.compare_values(angle, test["Angle"], "Angle")
                if not passed:
                    result.errors.append(f"Angle: expected {test['Angle']}, got {angle}")
                result.max_error = max(result.max_error, error)
                result.passed &= passed
                
                # Test interpolation
                interp = v1 + (v2 - v1) * 0.5
                passed_x, error_x = self.compare_values(interp.x, test["Interpolated50"]["X"], "Interpolated.X")
                passed_y, error_y = self.compare_values(interp.y, test["Interpolated50"]["Y"], "Interpolated.Y")
                if not (passed_x and passed_y):
                    result.errors.append(f"Interpolated50: expected ({test['Interpolated50']['X']}, {test['Interpolated50']['Y']}), got ({interp.x}, {interp.y})")
                result.max_error = max(result.max_error, error_x, error_y)
                result.passed &= passed_x and passed_y
                
                result.details = {
                    "operations_tested": 6,
                    "max_error": result.max_error,
                    "dot_product": dot,
                    "cross_product": cross,
                    "distance": dist
                }
                
            elif test["TestName"] == "Vector3D_Operations":
                # Create vectors
                v1 = Vec3(test["V1"]["X"], test["V1"]["Y"], test["V1"]["Z"])
                v2 = Vec3(test["V2"]["X"], test["V2"]["Y"], test["V2"]["Z"])
                
                # Test dot product
                dot = Vec3.dot(v1, v2)
                passed, error = self.compare_values(dot, test["DotProduct"], "DotProduct")
                if not passed:
                    result.errors.append(f"DotProduct: expected {test['DotProduct']}, got {dot}")
                result.max_error = max(result.max_error, error)
                result.passed &= passed
                
                # Test cross product
                cross = Vec3.cross(v1, v2)
                passed_x, error_x = self.compare_values(cross.x, test["CrossProduct"]["X"], "Cross.X")
                passed_y, error_y = self.compare_values(cross.y, test["CrossProduct"]["Y"], "Cross.Y")
                passed_z, error_z = self.compare_values(cross.z, test["CrossProduct"]["Z"], "Cross.Z")
                if not (passed_x and passed_y and passed_z):
                    result.errors.append(f"CrossProduct: expected {test['CrossProduct']}, got ({cross.x}, {cross.y}, {cross.z})")
                result.max_error = max(result.max_error, error_x, error_y, error_z)
                result.passed &= passed_x and passed_y and passed_z
                
                # Test distance
                dist = Vec3.distance(v1, v2)
                passed, error = self.compare_values(dist, test["Distance"], "Distance")
                if not passed:
                    result.errors.append(f"Distance: expected {test['Distance']}, got {dist}")
                result.max_error = max(result.max_error, error)
                result.passed &= passed
                
                # Test normalization
                norm = Vec3.normal(v1)
                passed_x, error_x = self.compare_values(norm.x, test["Normalized"]["X"], "Normalized.X")
                passed_y, error_y = self.compare_values(norm.y, test["Normalized"]["Y"], "Normalized.Y")
                passed_z, error_z = self.compare_values(norm.z, test["Normalized"]["Z"], "Normalized.Z")
                if not (passed_x and passed_y and passed_z):
                    result.errors.append(f"Normalized: expected {test['Normalized']}, got ({norm.x}, {norm.y}, {norm.z})")
                result.max_error = max(result.max_error, error_x, error_y, error_z)
                result.passed &= passed_x and passed_y and passed_z
                
                result.details = {
                    "operations_tested": 4,
                    "max_error": result.max_error
                }
            
            self.results.append(result)
            status = "✅ PASSED" if result.passed else "❌ FAILED"
            print(f"  {result.test_name}: {status} (max error: {result.max_error:.2e})")
    
    def test_matrix_operations(self):
        """Test matrix operations against C# reference."""
        print("\nTesting Matrix Operations...")
        reference = self.load_reference_data("matrix_tests.json")
        
        for test in reference:
            result = AccuracyResult(
                test_name=test["TestName"],
                passed=True,
                max_error=0.0,
                errors=[],
                details={}
            )
            
            if test["TestName"] == "Matrix_Combined_Transform":
                # Recreate the transformation sequence
                translation = Mat4.translation(10.5, -20.3, 15.7)
                rotation_x = Mat4.rotation_x(math.pi / 6)  # 30 degrees
                rotation_y = Mat4.rotation_y(math.pi / 4)  # 45 degrees
                rotation_z = Mat4.rotation_z(math.pi / 3)  # 60 degrees
                scale = Mat4.scale(2.5, 1.5, 3.0)
                
                # Combine in same order as C#
                combined = scale * rotation_x * rotation_y * rotation_z * translation
                
                # Test transformation
                input_pt = Vec3(test["InputPoint"]["X"], test["InputPoint"]["Y"], test["InputPoint"]["Z"])
                output_pt = combined.transform_point(input_pt)
                
                # Compare output point
                passed_x, error_x = self.compare_values(output_pt.x, test["OutputPoint"]["X"], "Output.X")
                passed_y, error_y = self.compare_values(output_pt.y, test["OutputPoint"]["Y"], "Output.Y")
                passed_z, error_z = self.compare_values(output_pt.z, test["OutputPoint"]["Z"], "Output.Z")
                
                if not (passed_x and passed_y and passed_z):
                    result.errors.append(f"OutputPoint: expected {test['OutputPoint']}, got ({output_pt.x}, {output_pt.y}, {output_pt.z})")
                result.max_error = max(result.max_error, error_x, error_y, error_z)
                result.passed &= passed_x and passed_y and passed_z
                
                # Compare matrix elements
                ref_matrix = test["FinalMatrix"]
                for i in range(4):
                    for j in range(4):
                        actual = combined.m[i][j]
                        expected = ref_matrix[i][j]
                        passed, error = self.compare_values(actual, expected, f"M[{i},{j}]")
                        if not passed:
                            result.errors.append(f"Matrix[{i},{j}]: expected {expected}, got {actual}")
                        result.max_error = max(result.max_error, error)
                        result.passed &= passed
                
                result.details = {
                    "operations_tested": 19,  # 16 matrix elements + 3 point coordinates
                    "max_error": result.max_error,
                    "output_point": (output_pt.x, output_pt.y, output_pt.z)
                }
            
            self.results.append(result)
            status = "✅ PASSED" if result.passed else "❌ FAILED"
            print(f"  {result.test_name}: {status} (max error: {result.max_error:.2e})")
    
    def test_real_world_scenarios(self):
        """Test real-world building calculations against C# reference."""
        print("\nTesting Real-World Scenarios...")
        reference = self.load_reference_data("real_world_tests.json")
        
        for test in reference:
            result = AccuracyResult(
                test_name=test["Name"],
                passed=True,
                max_error=0.0,
                errors=[],
                details={}
            )
            
            if test["Name"] == "Gable_Roof_Rafter_Positions":
                # Extract inputs
                span = test["Inputs"]["Span"]
                length = test["Inputs"]["Length"]
                height = test["Inputs"]["Height"]
                pitch_deg = test["Inputs"]["Pitch"]
                pitch_rad = math.radians(pitch_deg)
                rafter_spacing = test["Inputs"]["RafterSpacing"]
                
                # Calculate ridge height
                ridge_height = height + (span / 2) * math.tan(pitch_rad)
                passed, error = self.compare_values(ridge_height, test["Outputs"]["RidgeHeight"], "RidgeHeight")
                if not passed:
                    result.errors.append(f"RidgeHeight: expected {test['Outputs']['RidgeHeight']}, got {ridge_height}")
                result.max_error = max(result.max_error, error)
                result.passed &= passed
                
                # Calculate roof area
                roof_area = 2 * length * (span / 2) / math.cos(pitch_rad)
                passed, error = self.compare_values(roof_area, test["Outputs"]["RoofArea"], "RoofArea")
                if not passed:
                    result.errors.append(f"RoofArea: expected {test['Outputs']['RoofArea']}, got {roof_area}")
                result.max_error = max(result.max_error, error)
                result.passed &= passed
                
                # Test rafter positions
                ref_rafters = test["Outputs"]["RafterPositions"]
                rafter_count = 0
                for i, ref_rafter in enumerate(ref_rafters):
                    x = ref_rafter["X"]
                    
                    # Calculate positions
                    left_end = Vec3(x, 0, height)
                    right_end = Vec3(x, span, height)
                    apex = Vec3(x, span / 2, height + (span / 2) * math.tan(pitch_rad))
                    
                    # Compare apex position
                    passed_x, error_x = self.compare_values(apex.x, ref_rafter["Apex"]["X"], f"Rafter{i}.Apex.X")
                    passed_y, error_y = self.compare_values(apex.y, ref_rafter["Apex"]["Y"], f"Rafter{i}.Apex.Y")
                    passed_z, error_z = self.compare_values(apex.z, ref_rafter["Apex"]["Z"], f"Rafter{i}.Apex.Z")
                    
                    if not (passed_x and passed_y and passed_z):
                        result.errors.append(f"Rafter{i}.Apex: expected {ref_rafter['Apex']}, got ({apex.x}, {apex.y}, {apex.z})")
                    result.max_error = max(result.max_error, error_x, error_y, error_z)
                    result.passed &= passed_x and passed_y and passed_z
                    
                    # Calculate and compare length
                    length_val = Vec3.distance(left_end, apex)
                    passed, error = self.compare_values(length_val, ref_rafter["Length"], f"Rafter{i}.Length")
                    if not passed:
                        result.errors.append(f"Rafter{i}.Length: expected {ref_rafter['Length']}, got {length_val}")
                    result.max_error = max(result.max_error, error)
                    result.passed &= passed
                    
                    # Calculate and compare angle
                    angle = math.atan2(apex.z - left_end.z, apex.y - left_end.y)
                    passed, error = self.compare_values(angle, ref_rafter["Angle"], f"Rafter{i}.Angle")
                    if not passed:
                        result.errors.append(f"Rafter{i}.Angle: expected {ref_rafter['Angle']}, got {angle}")
                    result.max_error = max(result.max_error, error)
                    result.passed &= passed
                    
                    rafter_count += 1
                
                result.details = {
                    "rafters_tested": rafter_count,
                    "calculations_per_rafter": 5,
                    "total_comparisons": 2 + rafter_count * 5,
                    "max_error": result.max_error
                }
            
            elif test["Name"] == "Column_Footing_Placement":
                # Extract inputs
                bay_count = test["Inputs"]["BayCount"]
                bay_size = test["Inputs"]["BaySize"]
                span = test["Inputs"]["Span"]
                footing_diameter = test["Inputs"]["FootingDiameter"]
                footing_depth = test["Inputs"]["FootingDepth"]
                
                # Calculate column positions and footing volumes
                column_positions = []
                total_volume = 0.0
                
                for bay in range(bay_count + 1):
                    x = bay * bay_size
                    
                    # Calculate footing volume (cylinder)
                    radius = footing_diameter / 2
                    volume = math.pi * (radius ** 2) * footing_depth / 1e9  # Convert to m³
                    concrete_kg = volume * 2400  # Concrete density
                    
                    column_positions.append({
                        "x": x,
                        "volume": volume,
                        "concrete": concrete_kg
                    })
                    total_volume += volume
                
                # Compare total footing volume
                expected_total = test["Outputs"]["TotalFootingVolume"]
                passed, error = self.compare_values(total_volume * 2, expected_total, "TotalFootingVolume")
                if not passed:
                    result.errors.append(f"TotalFootingVolume: expected {expected_total}, got {total_volume * 2}")
                result.max_error = max(result.max_error, error)
                result.passed &= passed
                
                # Compare total concrete
                total_concrete = total_volume * 2 * 2400
                expected_concrete = test["Outputs"]["TotalConcreteKg"]
                passed, error = self.compare_values(total_concrete, expected_concrete, "TotalConcreteKg")
                if not passed:
                    result.errors.append(f"TotalConcreteKg: expected {expected_concrete}, got {total_concrete}")
                result.max_error = max(result.max_error, error)
                result.passed &= passed
                
                result.details = {
                    "columns_tested": len(column_positions),
                    "total_comparisons": 2,
                    "max_error": result.max_error
                }
            
            self.results.append(result)
            status = "✅ PASSED" if result.passed else "❌ FAILED"
            print(f"  {result.test_name}: {status} (max error: {result.max_error:.2e})")
    
    def generate_report(self):
        """Generate accuracy test report."""
        print("\n" + "="*60)
        print("GEOMETRY ACCURACY TEST REPORT")
        print("="*60)
        
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results if r.passed)
        
        print(f"\nTotal Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {total_tests - passed_tests}")
        print(f"Success Rate: {passed_tests/total_tests*100:.1f}%")
        
        # Find maximum error across all tests
        max_error_overall = max(r.max_error for r in self.results)
        print(f"\nMaximum Error (all tests): {max_error_overall:.2e}")
        print(f"Decimal Places Accuracy: {-math.log10(max_error_overall) if max_error_overall > 0 else 'Perfect':.1f}")
        
        # List any failures
        failures = [r for r in self.results if not r.passed]
        if failures:
            print("\n❌ FAILED TESTS:")
            for failure in failures:
                print(f"\n{failure.test_name}:")
                print(f"  Max Error: {failure.max_error:.2e}")
                for error in failure.errors[:5]:  # Show first 5 errors
                    print(f"  - {error}")
                if len(failure.errors) > 5:
                    print(f"  ... and {len(failure.errors) - 5} more errors")
        else:
            print("\n✅ ALL TESTS PASSED!")
            print("The Python implementation matches C# reference data with high accuracy.")
        
        # Summary by category
        print("\n" + "-"*40)
        print("SUMMARY BY CATEGORY:")
        
        categories = {
            "Vector Operations": ["Vector2D_Operations", "Vector3D_Operations"],
            "Matrix Operations": ["Matrix_Combined_Transform"],
            "Real-World Scenarios": ["Gable_Roof_Rafter_Positions", "Column_Footing_Placement"]
        }
        
        for category, test_names in categories.items():
            category_results = [r for r in self.results if r.test_name in test_names]
            if category_results:
                passed = sum(1 for r in category_results if r.passed)
                max_error = max(r.max_error for r in category_results)
                print(f"\n{category}:")
                print(f"  Tests: {len(category_results)}")
                print(f"  Passed: {passed}")
                print(f"  Max Error: {max_error:.2e}")
        
        # Save detailed report
        report_path = Path(__file__).parent / "reports" / "geometry_accuracy_report.json"
        report_path.parent.mkdir(exist_ok=True)
        
        report_data = {
            "summary": {
                "total_tests": total_tests,
                "passed": passed_tests,
                "failed": total_tests - passed_tests,
                "max_error": max_error_overall,
                "decimal_accuracy": -math.log10(max_error_overall) if max_error_overall > 0 else float('inf')
            },
            "results": [
                {
                    "test_name": r.test_name,
                    "passed": r.passed,
                    "max_error": r.max_error,
                    "error_count": len(r.errors),
                    "details": r.details
                }
                for r in self.results
            ]
        }
        
        with open(report_path, 'w') as f:
            json.dump(report_data, f, indent=2)
        
        print(f"\nDetailed report saved to: {report_path}")
        
        return passed_tests == total_tests
    
    def run_all_tests(self):
        """Run all accuracy tests."""
        print("Running Geometry Accuracy Tests")
        print("Testing against C# reference data with 10 decimal place accuracy")
        print("-" * 60)
        
        self.test_vector_operations()
        self.test_matrix_operations()
        self.test_real_world_scenarios()
        
        return self.generate_report()


if __name__ == "__main__":
    tester = GeometryAccuracyTester()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)