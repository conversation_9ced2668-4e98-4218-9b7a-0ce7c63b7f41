[{"Name": "<PERSON><PERSON>_<PERSON><PERSON>_Ra<PERSON>_Positions", "Description": "Calculate exact 3D positions of rafter ends for a gable roof", "Inputs": {"Span": "6000", "Length": "9000", "Height": "2700", "Pitch": "15", "RafterSpacing": "900"}, "Outputs": {"RafterCount": 11, "RafterPositions": [{"X": "0", "LeftEnd": {"X": "0", "Y": 0, "Z": "2700"}, "RightEnd": {"X": "0", "Y": "6000", "Z": "2700"}, "Apex": {"X": "0", "Y": "3000", "Z": "3503.84757729337"}, "Length": "3105.82854123025", "Angle": "0.261799387799149"}, {"X": "900", "LeftEnd": {"X": "900", "Y": 0, "Z": "2700"}, "RightEnd": {"X": "900", "Y": "6000", "Z": "2700"}, "Apex": {"X": "900", "Y": "3000", "Z": "3503.84757729337"}, "Length": "3105.82854123025", "Angle": "0.261799387799149"}, {"X": "1800", "LeftEnd": {"X": "1800", "Y": 0, "Z": "2700"}, "RightEnd": {"X": "1800", "Y": "6000", "Z": "2700"}, "Apex": {"X": "1800", "Y": "3000", "Z": "3503.84757729337"}, "Length": "3105.82854123025", "Angle": "0.261799387799149"}, {"X": "2700", "LeftEnd": {"X": "2700", "Y": 0, "Z": "2700"}, "RightEnd": {"X": "2700", "Y": "6000", "Z": "2700"}, "Apex": {"X": "2700", "Y": "3000", "Z": "3503.84757729337"}, "Length": "3105.82854123025", "Angle": "0.261799387799149"}, {"X": "3600", "LeftEnd": {"X": "3600", "Y": 0, "Z": "2700"}, "RightEnd": {"X": "3600", "Y": "6000", "Z": "2700"}, "Apex": {"X": "3600", "Y": "3000", "Z": "3503.84757729337"}, "Length": "3105.82854123025", "Angle": "0.261799387799149"}, {"X": "4500", "LeftEnd": {"X": "4500", "Y": 0, "Z": "2700"}, "RightEnd": {"X": "4500", "Y": "6000", "Z": "2700"}, "Apex": {"X": "4500", "Y": "3000", "Z": "3503.84757729337"}, "Length": "3105.82854123025", "Angle": "0.261799387799149"}, {"X": "5400", "LeftEnd": {"X": "5400", "Y": 0, "Z": "2700"}, "RightEnd": {"X": "5400", "Y": "6000", "Z": "2700"}, "Apex": {"X": "5400", "Y": "3000", "Z": "3503.84757729337"}, "Length": "3105.82854123025", "Angle": "0.261799387799149"}, {"X": "6300", "LeftEnd": {"X": "6300", "Y": 0, "Z": "2700"}, "RightEnd": {"X": "6300", "Y": "6000", "Z": "2700"}, "Apex": {"X": "6300", "Y": "3000", "Z": "3503.84757729337"}, "Length": "3105.82854123025", "Angle": "0.261799387799149"}, {"X": "7200", "LeftEnd": {"X": "7200", "Y": 0, "Z": "2700"}, "RightEnd": {"X": "7200", "Y": "6000", "Z": "2700"}, "Apex": {"X": "7200", "Y": "3000", "Z": "3503.84757729337"}, "Length": "3105.82854123025", "Angle": "0.261799387799149"}, {"X": "8100", "LeftEnd": {"X": "8100", "Y": 0, "Z": "2700"}, "RightEnd": {"X": "8100", "Y": "6000", "Z": "2700"}, "Apex": {"X": "8100", "Y": "3000", "Z": "3503.84757729337"}, "Length": "3105.82854123025", "Angle": "0.261799387799149"}, {"X": "9000", "LeftEnd": {"X": "9000", "Y": 0, "Z": "2700"}, "RightEnd": {"X": "9000", "Y": "6000", "Z": "2700"}, "Apex": {"X": "9000", "Y": "3000", "Z": "3503.84757729337"}, "Length": "3105.82854123025", "Angle": "0.261799387799149"}], "RidgeHeight": "3503.84757729337", "RoofArea": "55904913.7421445"}}, {"Name": "Column_Footing_Placement", "Description": "Calculate column positions and footing requirements", "Inputs": {"BayCount": 3, "BaySize": "3000", "Span": "6000", "ColumnSize": "100", "FootingDiameter": "600", "FootingDepth": "900"}, "Outputs": {"ColumnCount": 8, "ColumnPositions": [{"Left": {"X": "0", "Y": "0", "Z": "0"}, "Right": {"X": "0", "Y": "6000", "Z": "0"}, "FootingVolume": "0.254469004940773", "ConcreteRequired": "610.725611857856"}, {"Left": {"X": "3000", "Y": "0", "Z": "0"}, "Right": {"X": "3000", "Y": "6000", "Z": "0"}, "FootingVolume": "0.254469004940773", "ConcreteRequired": "610.725611857856"}, {"Left": {"X": "6000", "Y": "0", "Z": "0"}, "Right": {"X": "6000", "Y": "6000", "Z": "0"}, "FootingVolume": "0.254469004940773", "ConcreteRequired": "610.725611857856"}, {"Left": {"X": "9000", "Y": "0", "Z": "0"}, "Right": {"X": "9000", "Y": "6000", "Z": "0"}, "FootingVolume": "0.254469004940773", "ConcreteRequired": "610.725611857856"}], "TotalFootingVolume": "2.03575203952619", "TotalConcreteKg": "4885.80489486285"}}]