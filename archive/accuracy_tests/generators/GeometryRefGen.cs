using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Text.Json.Serialization;

// This C# code generates reference data for geometry accuracy testing
// It performs complex calculations that the Python implementation must match exactly

public class GeometryReferenceGenerator
{
    public class TestVector2D
    {
        public double X { get; set; }
        public double Y { get; set; }
        public double Length { get; set; }
        public double Angle { get; set; }
    }

    public class TestVector3D
    {
        public double X { get; set; }
        public double Y { get; set; }
        public double Z { get; set; }
        public double Length { get; set; }
    }

    public class TestMatrix4x4
    {
        public double[,] Values { get; set; } = new double[4, 4];
        public TestVector3D TransformedPoint { get; set; }
    }

    public class TransformationChainTest
    {
        public string Description { get; set; }
        public List<string> Operations { get; set; } = new List<string>();
        public TestVector3D InputPoint { get; set; }
        public TestVector3D OutputPoint { get; set; }
        public TestMatrix4x4 FinalMatrix { get; set; }
    }

    public class IntersectionTest
    {
        public string Type { get; set; }
        public object Input1 { get; set; }
        public object Input2 { get; set; }
        public object Result { get; set; }
        public bool HasIntersection { get; set; }
    }

    public class PolygonTest
    {
        public List<TestVector2D> Points { get; set; } = new List<TestVector2D>();
        public double Area { get; set; }
        public double Perimeter { get; set; }
        public TestVector2D Centroid { get; set; }
        public bool IsClockwise { get; set; }
        public List<int[]> Triangulation { get; set; }
    }

    public class RealWorldScenario
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public Dictionary<string, object> Inputs { get; set; }
        public Dictionary<string, object> Outputs { get; set; }
    }

    public static void Main()
    {
        var generator = new GeometryReferenceGenerator();
        generator.GenerateAllReferenceData();
    }

    public void GenerateAllReferenceData()
    {
        var outputDir = "reference_data/geometry/";
        Directory.CreateDirectory(outputDir);

        // 1. Vector Operations
        GenerateVectorTests(Path.Combine(outputDir, "vector_tests.json"));

        // 2. Matrix Transformations
        GenerateMatrixTests(Path.Combine(outputDir, "matrix_tests.json"));

        // 3. Complex Transformation Chains
        GenerateTransformationChains(Path.Combine(outputDir, "transformation_chains.json"));

        // 4. Intersection Tests
        GenerateIntersectionTests(Path.Combine(outputDir, "intersection_tests.json"));

        // 5. Polygon Operations
        GeneratePolygonTests(Path.Combine(outputDir, "polygon_tests.json"));

        // 6. Real-world Building Scenarios
        GenerateRealWorldTests(Path.Combine(outputDir, "real_world_tests.json"));

        Console.WriteLine("Reference data generation complete!");
    }

    private void GenerateVectorTests(string filepath)
    {
        var tests = new List<object>();

        // Test 1: Complex vector operations
        var v1 = new Vec2(3.14159, 2.71828);
        var v2 = new Vec2(1.41421, 1.73205);
        
        tests.Add(new
        {
            TestName = "Vector2D_Operations",
            V1 = new { v1.X, v1.Y },
            V2 = new { v2.X, v2.Y },
            DotProduct = Vec2.Dot(v1, v2),
            CrossProduct = v1.X * v2.Y - v1.Y * v2.X,
            Distance = Vec2.Distance(v1, v2),
            Normalized = Vec2.Normal(v1),
            Angle = Math.Atan2(v1.Y, v1.X),
            Interpolated50 = v1 + (v2 - v1) * 0.5
        });

        // Test 2: 3D vector operations with precision
        var v3d1 = new Vec3(1.23456789, 9.87654321, 5.55555555);
        var v3d2 = new Vec3(3.33333333, 7.77777777, 2.22222222);
        
        tests.Add(new
        {
            TestName = "Vector3D_Operations",
            V1 = new { v3d1.X, v3d1.Y, v3d1.Z },
            V2 = new { v3d2.X, v3d2.Y, v3d2.Z },
            DotProduct = Vec3.Dot(v3d1, v3d2),
            CrossProduct = Vec3.Cross(v3d1, v3d2),
            Distance = Vec3.Distance(v3d1, v3d2),
            Normalized = Vec3.Normal(v3d1),
            AngleRadians = Vec3.Angle(v3d1, v3d2),
            Projected = Vec3.Project(v3d1, v3d2)
        });

        SaveJson(filepath, tests);
    }

    private void GenerateMatrixTests(string filepath)
    {
        var tests = new List<object>();

        // Test 1: Combined transformation
        var translation = Mat4.CreateTranslation(10.5, -20.3, 15.7);
        var rotationX = Mat4.CreateRotationX(Math.PI / 6); // 30 degrees
        var rotationY = Mat4.CreateRotationY(Math.PI / 4); // 45 degrees
        var rotationZ = Mat4.CreateRotationZ(Math.PI / 3); // 60 degrees
        var scale = Mat4.CreateScale(2.5, 1.5, 3.0);

        var combined = scale * rotationX * rotationY * rotationZ * translation;
        var testPoint = new Vec3(1.0, 2.0, 3.0);
        var transformed = combined.TransformPosition(testPoint);

        tests.Add(new
        {
            TestName = "Matrix_Combined_Transform",
            InputPoint = new { testPoint.X, testPoint.Y, testPoint.Z },
            TransformationOrder = new[] { "Translate", "RotateZ", "RotateY", "RotateX", "Scale" },
            FinalMatrix = MatrixToArray(combined),
            OutputPoint = new { transformed.X, transformed.Y, transformed.Z },
            Determinant = combined.GetDeterminant(),
            InverseMatrix = MatrixToArray(combined.GetInverse())
        });

        // Test 2: Projection matrix
        var perspective = Mat4.CreatePerspective(
            Math.PI / 4,  // 45 degree FOV
            16.0 / 9.0,   // Aspect ratio
            0.1,          // Near plane
            1000.0        // Far plane
        );

        tests.Add(new
        {
            TestName = "Perspective_Projection",
            FOV_Radians = Math.PI / 4,
            AspectRatio = 16.0 / 9.0,
            NearPlane = 0.1,
            FarPlane = 1000.0,
            Matrix = MatrixToArray(perspective),
            TestPoints = new[]
            {
                ProjectPoint(perspective, new Vec3(0, 0, -1)),
                ProjectPoint(perspective, new Vec3(1, 1, -10)),
                ProjectPoint(perspective, new Vec3(-5, 3, -50))
            }
        });

        SaveJson(filepath, tests);
    }

    private void GenerateTransformationChains(string filepath)
    {
        var tests = new List<TransformationChainTest>();

        // Test 1: Carport column positioning
        var test1 = new TransformationChainTest
        {
            Description = "Position carport column with base rotation and offset",
            InputPoint = new TestVector3D { X = 0, Y = 0, Z = 0 }
        };

        var m1 = Mat4.Identity;
        m1 = m1 * Mat4.CreateTranslation(5000, 0, 0);  // Move to column position
        test1.Operations.Add("Translate(5000, 0, 0)");
        
        m1 = m1 * Mat4.CreateRotationY(Math.PI / 12);  // 15 degree tilt
        test1.Operations.Add("RotateY(15°)");
        
        m1 = m1 * Mat4.CreateTranslation(0, 2400, 0);  // Column height
        test1.Operations.Add("Translate(0, 2400, 0)");

        var output1 = m1.TransformPosition(new Vec3(0, 0, 0));
        test1.OutputPoint = new TestVector3D { X = output1.X, Y = output1.Y, Z = output1.Z };
        test1.FinalMatrix = new TestMatrix4x4 { Values = MatrixToArray(m1) };

        tests.Add(test1);

        // Test 2: Roof rafter with compound angles
        var test2 = new TransformationChainTest
        {
            Description = "Roof rafter with pitch and skew",
            InputPoint = new TestVector3D { X = 100, Y = 0, Z = 0 }
        };

        var m2 = Mat4.Identity;
        m2 = m2 * Mat4.CreateTranslation(3000, 2400, 1500);
        test2.Operations.Add("Translate(3000, 2400, 1500)");
        
        m2 = m2 * Mat4.CreateRotationZ(Math.PI / 6);  // 30 degree pitch
        test2.Operations.Add("RotateZ(30°)");
        
        m2 = m2 * Mat4.CreateRotationY(Math.PI / 18); // 10 degree skew
        test2.Operations.Add("RotateY(10°)");
        
        m2 = m2 * Mat4.CreateScale(1.0, 1.0, 6000);   // Stretch to length
        test2.Operations.Add("Scale(1, 1, 6000)");

        var output2 = m2.TransformPosition(new Vec3(100, 0, 0));
        test2.OutputPoint = new TestVector3D { X = output2.X, Y = output2.Y, Z = output2.Z };
        test2.FinalMatrix = new TestMatrix4x4 { Values = MatrixToArray(m2) };

        tests.Add(test2);

        SaveJson(filepath, tests);
    }

    private void GenerateIntersectionTests(string filepath)
    {
        var tests = new List<IntersectionTest>();

        // Test 1: 2D Line-Line intersection
        var line1Start = new Vec2(0, 0);
        var line1End = new Vec2(10, 10);
        var line2Start = new Vec2(0, 10);
        var line2End = new Vec2(10, 0);
        
        var intersection2D = Vec2.Inters(line1Start, line1End, line2Start, line2End);
        
        tests.Add(new IntersectionTest
        {
            Type = "Line2D_Line2D",
            Input1 = new { Start = line1Start, End = line1End },
            Input2 = new { Start = line2Start, End = line2End },
            Result = intersection2D,
            HasIntersection = intersection2D != null
        });

        // Test 2: Ray-Plane intersection
        var rayOrigin = new Vec3(5, 10, 15);
        var rayDirection = new Vec3(0, -1, 0).Normalized();
        var planePoint = new Vec3(0, 0, 0);
        var planeNormal = new Vec3(0, 1, 0);
        
        var t = RayPlaneIntersection(rayOrigin, rayDirection, planePoint, planeNormal);
        var intersectionPoint = t.HasValue ? rayOrigin + rayDirection * t.Value : null;
        
        tests.Add(new IntersectionTest
        {
            Type = "Ray_Plane",
            Input1 = new { Origin = rayOrigin, Direction = rayDirection },
            Input2 = new { Point = planePoint, Normal = planeNormal },
            Result = new { T = t, Point = intersectionPoint },
            HasIntersection = t.HasValue
        });

        // Test 3: Box-Box overlap
        var box1Min = new Vec3(0, 0, 0);
        var box1Max = new Vec3(10, 10, 10);
        var box2Min = new Vec3(5, 5, 5);
        var box2Max = new Vec3(15, 15, 15);
        
        var box1 = new Box3(box1Min, box1Max);
        var box2 = new Box3(box2Min, box2Max);
        var overlaps = box1.Intersects(box2);
        
        tests.Add(new IntersectionTest
        {
            Type = "Box3D_Box3D",
            Input1 = new { Min = box1Min, Max = box1Max },
            Input2 = new { Min = box2Min, Max = box2Max },
            Result = new { Overlaps = overlaps, 
                          IntersectionMin = overlaps ? Vec3.Max(box1Min, box2Min) : null,
                          IntersectionMax = overlaps ? Vec3.Min(box1Max, box2Max) : null },
            HasIntersection = overlaps
        });

        SaveJson(filepath, tests);
    }

    private void GeneratePolygonTests(string filepath)
    {
        var tests = new List<PolygonTest>();

        // Test 1: Carport floor outline
        var carportOutline = new List<Vec2>
        {
            new Vec2(0, 0),
            new Vec2(6000, 0),
            new Vec2(6000, 3000),
            new Vec2(0, 3000)
        };

        var test1 = new PolygonTest
        {
            Points = carportOutline.Select(p => new TestVector2D { X = p.X, Y = p.Y }).ToList(),
            Area = CalculatePolygonArea(carportOutline),
            Perimeter = CalculatePolygonPerimeter(carportOutline),
            Centroid = CalculatePolygonCentroid(carportOutline),
            IsClockwise = IsPolygonClockwise(carportOutline),
            Triangulation = TriangulatePolygon(carportOutline)
        };

        tests.Add(test1);

        // Test 2: Complex roof outline with cutout
        var roofOutline = new List<Vec2>
        {
            new Vec2(0, 0),
            new Vec2(8000, 0),
            new Vec2(8000, 2000),
            new Vec2(6000, 4000),
            new Vec2(2000, 4000),
            new Vec2(0, 2000)
        };

        var test2 = new PolygonTest
        {
            Points = roofOutline.Select(p => new TestVector2D { X = p.X, Y = p.Y }).ToList(),
            Area = CalculatePolygonArea(roofOutline),
            Perimeter = CalculatePolygonPerimeter(roofOutline),
            Centroid = CalculatePolygonCentroid(roofOutline),
            IsClockwise = IsPolygonClockwise(roofOutline),
            Triangulation = TriangulatePolygon(roofOutline)
        };

        tests.Add(test2);

        SaveJson(filepath, tests);
    }

    private void GenerateRealWorldTests(string filepath)
    {
        var tests = new List<RealWorldScenario>();

        // Test 1: Calculate rafter end positions for gable roof
        var test1 = new RealWorldScenario
        {
            Name = "Gable_Roof_Rafter_Positions",
            Description = "Calculate exact 3D positions of rafter ends for a gable roof",
            Inputs = new Dictionary<string, object>
            {
                ["Span"] = 6000.0,
                ["Length"] = 9000.0,
                ["Height"] = 2700.0,
                ["Pitch"] = 15.0, // degrees
                ["RafterSpacing"] = 900.0
            }
        };

        var span = 6000.0;
        var length = 9000.0;
        var height = 2700.0;
        var pitchRad = 15.0 * Math.PI / 180.0;
        var rafterSpacing = 900.0;

        var rafterPositions = new List<object>();
        for (double x = 0; x <= length; x += rafterSpacing)
        {
            var leftEnd = new Vec3(x, 0, height);
            var rightEnd = new Vec3(x, span, height);
            var apex = new Vec3(x, span / 2, height + (span / 2) * Math.Tan(pitchRad));
            
            rafterPositions.Add(new
            {
                X = x,
                LeftEnd = new { leftEnd.X, leftEnd.Y, leftEnd.Z },
                RightEnd = new { rightEnd.X, rightEnd.Y, rightEnd.Z },
                Apex = new { apex.X, apex.Y, apex.Z },
                Length = Vec3.Distance(leftEnd, apex),
                Angle = Math.Atan2(apex.Z - leftEnd.Z, apex.Y - leftEnd.Y)
            });
        }

        test1.Outputs = new Dictionary<string, object>
        {
            ["RafterCount"] = rafterPositions.Count,
            ["RafterPositions"] = rafterPositions,
            ["RidgeHeight"] = height + (span / 2) * Math.Tan(pitchRad),
            ["RoofArea"] = 2 * length * (span / 2) / Math.Cos(pitchRad)
        };

        tests.Add(test1);

        // Test 2: Column and footing calculations
        var test2 = new RealWorldScenario
        {
            Name = "Column_Footing_Placement",
            Description = "Calculate column positions and footing requirements",
            Inputs = new Dictionary<string, object>
            {
                ["BayCount"] = 3,
                ["BaySize"] = 3000.0,
                ["Span"] = 6000.0,
                ["ColumnSize"] = 100.0,
                ["FootingDiameter"] = 600.0,
                ["FootingDepth"] = 900.0
            }
        };

        var bayCount = 3;
        var baySize = 3000.0;
        var columnPositions = new List<object>();

        for (int bay = 0; bay <= bayCount; bay++)
        {
            var x = bay * baySize;
            columnPositions.Add(new
            {
                Left = new { X = x, Y = 0, Z = 0 },
                Right = new { X = x, Y = span, Z = 0 },
                FootingVolume = Math.PI * Math.Pow(300, 2) * 900 / 1000000000.0, // m³
                ConcreteRequired = Math.PI * Math.Pow(300, 2) * 900 / 1000000000.0 * 2400 // kg
            });
        }

        test2.Outputs = new Dictionary<string, object>
        {
            ["ColumnCount"] = (bayCount + 1) * 2,
            ["ColumnPositions"] = columnPositions,
            ["TotalFootingVolume"] = columnPositions.Count * Math.PI * Math.Pow(300, 2) * 900 / 1000000000.0,
            ["TotalConcreteKg"] = columnPositions.Count * Math.PI * Math.Pow(300, 2) * 900 / 1000000000.0 * 2400
        };

        tests.Add(test2);

        SaveJson(filepath, tests);
    }

    // Helper methods
    private double[,] MatrixToArray(Mat4 matrix)
    {
        var result = new double[4, 4];
        result[0, 0] = matrix.M11; result[0, 1] = matrix.M12; result[0, 2] = matrix.M13; result[0, 3] = matrix.M14;
        result[1, 0] = matrix.M21; result[1, 1] = matrix.M22; result[1, 2] = matrix.M23; result[1, 3] = matrix.M24;
        result[2, 0] = matrix.M31; result[2, 1] = matrix.M32; result[2, 2] = matrix.M33; result[2, 3] = matrix.M34;
        result[3, 0] = matrix.M41; result[3, 1] = matrix.M42; result[3, 2] = matrix.M43; result[3, 3] = matrix.M44;
        return result;
    }

    private object ProjectPoint(Mat4 projection, Vec3 point)
    {
        var transformed = projection.TransformPosition(point);
        return new { 
            X = transformed.X / transformed.W, 
            Y = transformed.Y / transformed.W, 
            Z = transformed.Z / transformed.W,
            W = transformed.W
        };
    }

    private double? RayPlaneIntersection(Vec3 rayOrigin, Vec3 rayDir, Vec3 planePoint, Vec3 planeNormal)
    {
        var denom = Vec3.Dot(rayDir, planeNormal);
        if (Math.Abs(denom) < 1e-6) return null;
        
        var t = Vec3.Dot(planePoint - rayOrigin, planeNormal) / denom;
        return t >= 0 ? t : null;
    }

    private double CalculatePolygonArea(List<Vec2> points)
    {
        double area = 0;
        for (int i = 0; i < points.Count; i++)
        {
            var j = (i + 1) % points.Count;
            area += points[i].X * points[j].Y;
            area -= points[j].X * points[i].Y;
        }
        return Math.Abs(area) / 2.0;
    }

    private double CalculatePolygonPerimeter(List<Vec2> points)
    {
        double perimeter = 0;
        for (int i = 0; i < points.Count; i++)
        {
            var j = (i + 1) % points.Count;
            perimeter += Vec2.Distance(points[i], points[j]);
        }
        return perimeter;
    }

    private TestVector2D CalculatePolygonCentroid(List<Vec2> points)
    {
        double cx = 0, cy = 0;
        double area = CalculatePolygonArea(points);
        
        for (int i = 0; i < points.Count; i++)
        {
            var j = (i + 1) % points.Count;
            var factor = points[i].X * points[j].Y - points[j].X * points[i].Y;
            cx += (points[i].X + points[j].X) * factor;
            cy += (points[i].Y + points[j].Y) * factor;
        }
        
        var scale = 1.0 / (6.0 * area);
        return new TestVector2D { X = cx * scale, Y = cy * scale };
    }

    private bool IsPolygonClockwise(List<Vec2> points)
    {
        double sum = 0;
        for (int i = 0; i < points.Count; i++)
        {
            var j = (i + 1) % points.Count;
            sum += (points[j].X - points[i].X) * (points[j].Y + points[i].Y);
        }
        return sum > 0;
    }

    private List<int[]> TriangulatePolygon(List<Vec2> points)
    {
        // Simple ear clipping triangulation
        var triangles = new List<int[]>();
        var indices = Enumerable.Range(0, points.Count).ToList();
        
        while (indices.Count > 3)
        {
            for (int i = 0; i < indices.Count; i++)
            {
                var prev = indices[(i - 1 + indices.Count) % indices.Count];
                var curr = indices[i];
                var next = indices[(i + 1) % indices.Count];
                
                if (IsEar(points, indices, prev, curr, next))
                {
                    triangles.Add(new[] { prev, curr, next });
                    indices.RemoveAt(i);
                    break;
                }
            }
        }
        
        if (indices.Count == 3)
            triangles.Add(indices.ToArray());
            
        return triangles;
    }

    private bool IsEar(List<Vec2> points, List<int> indices, int i1, int i2, int i3)
    {
        var p1 = points[i1];
        var p2 = points[i2];
        var p3 = points[i3];
        
        // Check if triangle is counter-clockwise
        var area = (p2.X - p1.X) * (p3.Y - p1.Y) - (p3.X - p1.X) * (p2.Y - p1.Y);
        if (area <= 0) return false;
        
        // Check if any other point is inside this triangle
        foreach (var idx in indices)
        {
            if (idx == i1 || idx == i2 || idx == i3) continue;
            if (PointInTriangle(points[idx], p1, p2, p3)) return false;
        }
        
        return true;
    }

    private bool PointInTriangle(Vec2 p, Vec2 a, Vec2 b, Vec2 c)
    {
        var v0 = c - a;
        var v1 = b - a;
        var v2 = p - a;
        
        var dot00 = Vec2.Dot(v0, v0);
        var dot01 = Vec2.Dot(v0, v1);
        var dot02 = Vec2.Dot(v0, v2);
        var dot11 = Vec2.Dot(v1, v1);
        var dot12 = Vec2.Dot(v1, v2);
        
        var invDenom = 1 / (dot00 * dot11 - dot01 * dot01);
        var u = (dot11 * dot02 - dot01 * dot12) * invDenom;
        var v = (dot00 * dot12 - dot01 * dot02) * invDenom;
        
        return (u >= 0) && (v >= 0) && (u + v <= 1);
    }

    private void SaveJson(string filepath, object data)
    {
        var options = new JsonSerializerOptions
        {
            WriteIndented = true,
            NumberHandling = JsonNumberHandling.AllowNamedFloatingPointLiterals
        };
        
        var json = JsonSerializer.Serialize(data, options);
        File.WriteAllText(filepath, json);
    }
}