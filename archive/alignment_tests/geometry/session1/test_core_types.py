"""
Geometry Core Types Alignment Tests - Session 1
Tests Vec2, Vec3, Mat4, Quaternion, and Angle classes for C#/Python alignment.
"""

import sys
import math
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from src.geometry import Vec2, Vec3, Mat4, <PERSON><PERSON>rn<PERSON>, Angle
import json


class AlignmentTestResult:
    def __init__(self, test_name: str):
        self.test_name = test_name
        self.passed = True
        self.details = []
        self.failures = []
    
    def assert_equal(self, actual, expected, tolerance=1e-6, message=""):
        """Assert values are equal within tolerance."""
        if isinstance(actual, (int, float)) and isinstance(expected, (int, float)):
            if abs(actual - expected) > tolerance:
                self.passed = False
                self.failures.append(f"{message}: Expected {expected}, got {actual}")
                return False
        elif actual != expected:
            self.passed = False
            self.failures.append(f"{message}: Expected {expected}, got {actual}")
            return False
        return True
    
    def add_detail(self, detail: str):
        self.details.append(detail)


class GeometryCoreAlignmentTests:
    def __init__(self):
        self.results = []
    
    def test_vec2_operations(self):
        """Test Vec2 operations match C# implementation."""
        result = AlignmentTestResult("Vec2 Operations")
        
        # Test creation and properties
        v1 = Vec2(3.0, 4.0)
        result.assert_equal(v1.x, 3.0, message="Vec2.x")
        result.assert_equal(v1.y, 4.0, message="Vec2.y")
        
        # Test length (C# Vec2.Length)
        result.assert_equal(v1.length(), 5.0, message="Vec2.length")
        result.assert_equal(v1.length_squared(), 25.0, message="Vec2.length_squared")
        
        # Test arithmetic operations
        v2 = Vec2(1.0, 2.0)
        v_add = v1 + v2
        result.assert_equal(v_add.x, 4.0, message="Vec2 addition x")
        result.assert_equal(v_add.y, 6.0, message="Vec2 addition y")
        
        v_sub = v1 - v2
        result.assert_equal(v_sub.x, 2.0, message="Vec2 subtraction x")
        result.assert_equal(v_sub.y, 2.0, message="Vec2 subtraction y")
        
        v_mul = v1 * 2.0
        result.assert_equal(v_mul.x, 6.0, message="Vec2 scalar multiplication x")
        result.assert_equal(v_mul.y, 8.0, message="Vec2 scalar multiplication y")
        
        # Test dot product
        dot = Vec2.dot(v1, v2)
        result.assert_equal(dot, 11.0, message="Vec2 dot product")
        
        # Test cross product (2D)
        cross = Vec2.cross(v1, v2)
        result.assert_equal(cross, 2.0, message="Vec2 cross product")
        
        # Test normalization
        v_norm = Vec2.normal(v1)
        result.assert_equal(v_norm.x, 0.6, message="Vec2 normalized x")
        result.assert_equal(v_norm.y, 0.8, message="Vec2 normalized y")
        result.assert_equal(v_norm.length(), 1.0, message="Vec2 normalized length")
        
        # Test distance
        dist = Vec2.distance(v1, v2)
        result.assert_equal(dist, math.sqrt(8), message="Vec2 distance")
        
        # Test angle
        angle_rad = v1.angle()
        angle_deg = math.degrees(angle_rad)
        result.assert_equal(angle_deg, 53.13010235, tolerance=0.00001, message="Vec2 angle")
        
        # Test static constants
        result.assert_equal(Vec2.origin().x, 0.0, message="Vec2.origin x")
        result.assert_equal(Vec2.origin().y, 0.0, message="Vec2.origin y")
        result.assert_equal(Vec2.unit_x().x, 1.0, message="Vec2.unit_x x")
        result.assert_equal(Vec2.unit_y().y, 1.0, message="Vec2.unit_y y")
        
        result.add_detail(f"Tested {15} Vec2 operations")
        self.results.append(result)
        
    def test_vec3_operations(self):
        """Test Vec3 operations match C# implementation."""
        result = AlignmentTestResult("Vec3 Operations")
        
        # Test creation and properties
        v1 = Vec3(2.0, 3.0, 6.0)
        result.assert_equal(v1.x, 2.0, message="Vec3.x")
        result.assert_equal(v1.y, 3.0, message="Vec3.y")
        result.assert_equal(v1.z, 6.0, message="Vec3.z")
        
        # Test length (C# Vec3.Length)
        result.assert_equal(v1.length(), 7.0, message="Vec3.length")
        result.assert_equal(v1.length_squared(), 49.0, message="Vec3.length_squared")
        
        # Test arithmetic operations
        v2 = Vec3(1.0, 2.0, 3.0)
        v_add = v1 + v2
        result.assert_equal(v_add.x, 3.0, message="Vec3 addition x")
        result.assert_equal(v_add.y, 5.0, message="Vec3 addition y")
        result.assert_equal(v_add.z, 9.0, message="Vec3 addition z")
        
        # Test dot product
        dot = Vec3.dot(v1, v2)
        result.assert_equal(dot, 26.0, message="Vec3 dot product")
        
        # Test cross product
        cross = Vec3.cross(v1, v2)
        result.assert_equal(cross.x, -3.0, message="Vec3 cross product x")
        result.assert_equal(cross.y, 0.0, message="Vec3 cross product y")
        result.assert_equal(cross.z, 1.0, message="Vec3 cross product z")
        
        # Test normalization
        v_norm = Vec3.normal(v1)
        result.assert_equal(v_norm.length(), 1.0, message="Vec3 normalized length")
        
        # Test transformation
        v3 = Vec3(1.0, 0.0, 0.0)
        # 90 degree rotation around Z axis
        mat = Mat4.rotation_z(math.pi / 2)
        v_transformed = mat.transform_point(v3)
        result.assert_equal(v_transformed.x, 0.0, tolerance=1e-6, message="Vec3 transform x")
        result.assert_equal(v_transformed.y, 1.0, tolerance=1e-6, message="Vec3 transform y")
        result.assert_equal(v_transformed.z, 0.0, tolerance=1e-6, message="Vec3 transform z")
        
        # Test static constants
        result.assert_equal(Vec3.zero().length(), 0.0, message="Vec3.zero")
        result.assert_equal(Vec3.unit_x().x, 1.0, message="Vec3.unit_x")
        result.assert_equal(Vec3.unit_y().y, 1.0, message="Vec3.unit_y")
        result.assert_equal(Vec3.unit_z().z, 1.0, message="Vec3.unit_z")
        
        result.add_detail(f"Tested {17} Vec3 operations")
        self.results.append(result)
    
    def test_mat4_operations(self):
        """Test Mat4 operations match C# implementation."""
        result = AlignmentTestResult("Mat4 Operations")
        
        # Test identity matrix
        identity = Mat4.identity()
        result.assert_equal(identity.m[0][0], 1.0, message="Identity [0,0]")
        result.assert_equal(identity.m[1][1], 1.0, message="Identity [1,1]")
        result.assert_equal(identity.m[2][2], 1.0, message="Identity [2,2]")
        result.assert_equal(identity.m[3][3], 1.0, message="Identity [3,3]")
        result.assert_equal(identity.m[0][1], 0.0, message="Identity [0,1]")
        
        # Test translation
        trans = Mat4.translation(10.0, 20.0, 30.0)
        point = Vec3(1.0, 2.0, 3.0)
        transformed = trans.transform_point(point)
        result.assert_equal(transformed.x, 11.0, message="Translation x")
        result.assert_equal(transformed.y, 22.0, message="Translation y")
        result.assert_equal(transformed.z, 33.0, message="Translation z")
        
        # Test scaling
        scale = Mat4.scale(2.0, 3.0, 4.0)
        scaled = scale.transform_point(point)
        result.assert_equal(scaled.x, 2.0, message="Scale x")
        result.assert_equal(scaled.y, 6.0, message="Scale y")
        result.assert_equal(scaled.z, 12.0, message="Scale z")
        
        # Test rotation around X
        rot_x = Mat4.rotation_x(math.pi / 2)  # 90 degrees
        rotated = rot_x.transform_point(Vec3(0.0, 1.0, 0.0))
        result.assert_equal(rotated.x, 0.0, tolerance=1e-6, message="Rotation X: x")
        result.assert_equal(rotated.y, 0.0, tolerance=1e-6, message="Rotation X: y")
        result.assert_equal(rotated.z, 1.0, tolerance=1e-6, message="Rotation X: z")
        
        # Test rotation around Y
        rot_y = Mat4.rotation_y(math.pi / 2)  # 90 degrees
        rotated = rot_y.transform_point(Vec3(1.0, 0.0, 0.0))
        result.assert_equal(rotated.x, 0.0, tolerance=1e-6, message="Rotation Y: x")
        result.assert_equal(rotated.y, 0.0, tolerance=1e-6, message="Rotation Y: y")
        result.assert_equal(rotated.z, -1.0, tolerance=1e-6, message="Rotation Y: z")
        
        # Test rotation around Z
        rot_z = Mat4.rotation_z(math.pi / 2)  # 90 degrees
        rotated = rot_z.transform_point(Vec3(1.0, 0.0, 0.0))
        result.assert_equal(rotated.x, 0.0, tolerance=1e-6, message="Rotation Z: x")
        result.assert_equal(rotated.y, 1.0, tolerance=1e-6, message="Rotation Z: y")
        result.assert_equal(rotated.z, 0.0, tolerance=1e-6, message="Rotation Z: z")
        
        # Test matrix multiplication
        combined = trans * scale
        combined_point = combined.transform_point(Vec3(1.0, 1.0, 1.0))
        result.assert_equal(combined_point.x, 12.0, message="Combined transform x")
        result.assert_equal(combined_point.y, 23.0, message="Combined transform y")
        result.assert_equal(combined_point.z, 34.0, message="Combined transform z")
        
        # Test inverse
        inv = trans.inverse()
        back = inv.transform_point(transformed)
        result.assert_equal(back.x, point.x, tolerance=1e-6, message="Inverse x")
        result.assert_equal(back.y, point.y, tolerance=1e-6, message="Inverse y")
        result.assert_equal(back.z, point.z, tolerance=1e-6, message="Inverse z")
        
        result.add_detail(f"Tested {25} Mat4 operations")
        self.results.append(result)
    
    def test_quaternion_operations(self):
        """Test Quaternion operations match C# implementation."""
        result = AlignmentTestResult("Quaternion Operations")
        
        # Test identity quaternion
        identity = Quaternion.identity()
        result.assert_equal(identity.w, 1.0, message="Identity w")
        result.assert_equal(identity.x, 0.0, message="Identity x")
        result.assert_equal(identity.y, 0.0, message="Identity y")
        result.assert_equal(identity.z, 0.0, message="Identity z")
        
        # Test from axis angle - 90 degrees around Z
        q = Quaternion.from_axis_angle(Vec3(0, 0, 1), math.pi / 2)
        result.assert_equal(q.w, math.cos(math.pi / 4), tolerance=1e-6, message="From axis angle w")
        result.assert_equal(q.z, math.sin(math.pi / 4), tolerance=1e-6, message="From axis angle z")
        
        # Test quaternion multiplication
        q2 = Quaternion.from_axis_angle(Vec3(0, 0, 1), math.pi / 2)
        q_combined = q * q2  # Should be 180 degrees
        
        # Test rotation of vector
        v = Vec3(1, 0, 0)
        v_rotated = q.rotate_vector(v)
        result.assert_equal(v_rotated.x, 0.0, tolerance=1e-6, message="Rotate vector x")
        result.assert_equal(v_rotated.y, 1.0, tolerance=1e-6, message="Rotate vector y")
        result.assert_equal(v_rotated.z, 0.0, tolerance=1e-6, message="Rotate vector z")
        
        # Test to matrix conversion
        mat = q.to_matrix()
        v_mat = mat.transform_point(v)
        result.assert_equal(v_mat.x, v_rotated.x, tolerance=1e-6, message="Matrix conversion x")
        result.assert_equal(v_mat.y, v_rotated.y, tolerance=1e-6, message="Matrix conversion y")
        result.assert_equal(v_mat.z, v_rotated.z, tolerance=1e-6, message="Matrix conversion z")
        
        # Test normalization
        q_norm = q.normalized()
        length = math.sqrt(q_norm.w**2 + q_norm.x**2 + q_norm.y**2 + q_norm.z**2)
        result.assert_equal(length, 1.0, tolerance=1e-6, message="Normalized length")
        
        result.add_detail(f"Tested {14} Quaternion operations")
        self.results.append(result)
    
    def test_angle_operations(self):
        """Test Angle operations match C# implementation."""
        result = AlignmentTestResult("Angle Operations")
        
        # Test degree/radian conversions
        angle_deg = Angle.from_degrees(90)
        result.assert_equal(angle_deg.radians, math.pi / 2, tolerance=1e-6, message="Degrees to radians")
        result.assert_equal(angle_deg.degrees, 90.0, message="Degrees property")
        
        angle_rad = Angle.from_radians(math.pi)
        result.assert_equal(angle_rad.degrees, 180.0, tolerance=1e-6, message="Radians to degrees")
        result.assert_equal(angle_rad.radians, math.pi, message="Radians property")
        
        # Test arithmetic operations
        a1 = Angle.from_degrees(30)
        a2 = Angle.from_degrees(60)
        a_sum = a1 + a2
        result.assert_equal(a_sum.degrees, 90.0, message="Angle addition")
        
        a_diff = a2 - a1
        result.assert_equal(a_diff.degrees, 30.0, message="Angle subtraction")
        
        a_mul = a1 * 2
        result.assert_equal(a_mul.degrees, 60.0, message="Angle multiplication")
        
        a_div = a2 / 2
        result.assert_equal(a_div.degrees, 30.0, message="Angle division")
        
        # Test normalization
        a_large = Angle.from_degrees(390)
        a_norm = a_large.normalized()
        result.assert_equal(a_norm.degrees, 30.0, tolerance=1e-6, message="Angle normalization positive")
        
        a_negative = Angle.from_degrees(-30)
        a_norm_neg = a_negative.normalized()
        result.assert_equal(a_norm_neg.degrees, 330.0, tolerance=1e-6, message="Angle normalization negative")
        
        # Test trigonometric functions
        a45 = Angle.from_degrees(45)
        result.assert_equal(a45.sin(), math.sqrt(2) / 2, tolerance=1e-6, message="Angle sin")
        result.assert_equal(a45.cos(), math.sqrt(2) / 2, tolerance=1e-6, message="Angle cos")
        result.assert_equal(a45.tan(), 1.0, tolerance=1e-6, message="Angle tan")
        
        result.add_detail(f"Tested {14} Angle operations")
        self.results.append(result)
    
    def generate_report(self):
        """Generate detailed test report."""
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results if r.passed)
        
        report = [
            "# Geometry Core Types Alignment Test Report",
            f"\nTotal Tests: {total_tests}",
            f"Passed: {passed_tests}",
            f"Failed: {total_tests - passed_tests}",
            f"Success Rate: {(passed_tests / total_tests * 100):.1f}%\n",
            "## Test Results\n"
        ]
        
        for result in self.results:
            status = "✅ PASSED" if result.passed else "❌ FAILED"
            report.append(f"### {result.test_name}: {status}")
            
            if result.details:
                report.append("\n**Details:**")
                for detail in result.details:
                    report.append(f"- {detail}")
            
            if result.failures:
                report.append("\n**Failures:**")
                for failure in result.failures:
                    report.append(f"- ❌ {failure}")
            
            report.append("")
        
        # Add summary
        if passed_tests == total_tests:
            report.append("\n## Summary")
            report.append("✅ **All tests passed!** The Python geometry core types implementation is 100% aligned with the C# version.")
        else:
            report.append("\n## Summary")
            report.append("❌ **Some tests failed.** Please review the failures above and fix the implementation.")
        
        return "\n".join(report)
    
    def run_all_tests(self):
        """Run all core type tests."""
        print("Running Geometry Core Types Alignment Tests...")
        print("-" * 50)
        
        self.test_vec2_operations()
        print("✓ Vec2 operations tested")
        
        self.test_vec3_operations()
        print("✓ Vec3 operations tested")
        
        self.test_mat4_operations()
        print("✓ Mat4 operations tested")
        
        self.test_quaternion_operations()
        print("✓ Quaternion operations tested")
        
        self.test_angle_operations()
        print("✓ Angle operations tested")
        
        print("-" * 50)
        
        # Save report
        report = self.generate_report()
        report_path = Path(__file__).parent / "results" / "core_types_report.md"
        report_path.parent.mkdir(exist_ok=True)
        report_path.write_text(report)
        
        print(f"\nReport saved to: {report_path}")
        
        # Print summary
        total = len(self.results)
        passed = sum(1 for r in self.results if r.passed)
        print(f"\nSummary: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
        
        return passed == total


if __name__ == "__main__":
    tester = GeometryCoreAlignmentTests()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)