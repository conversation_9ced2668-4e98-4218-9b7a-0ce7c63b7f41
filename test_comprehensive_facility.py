#!/usr/bin/env python3
"""
Comprehensive Testing Facility for PyModel BIM Backend

This script provides a complete testing environment for all aspects of the BIM system:
- Unit tests
- Integration tests
- Output generation tests
- API tests
- Performance tests
- Validation tests

Date: 2025-07-03
"""

import os
import sys
import time
import json
import asyncio
import subprocess
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Optional
import argparse
import logging

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from src.business.building_input import BuildingInput, CarportRoofType
from src.business.engineering import EngData
from src.ifc_generation.carport_generator import CarportIFCGenerator
from src.glb_generation.carport_glb_generator import CarportGLBGenerator
from src.business.structure_builder import CarportBuilder

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ComprehensiveTestFacility:
    """Main test facility class that orchestrates all testing"""
    
    def __init__(self, output_dir: str = "test_output"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Create subdirectories
        self.dirs = {
            "unit": self.output_dir / "unit_tests",
            "integration": self.output_dir / "integration_tests",
            "ifc": self.output_dir / "ifc_files",
            "glb": self.output_dir / "glb_files",
            "api": self.output_dir / "api_tests",
            "performance": self.output_dir / "performance_tests",
            "validation": self.output_dir / "validation_tests",
            "reports": self.output_dir / "reports"
        }
        
        for dir_path in self.dirs.values():
            dir_path.mkdir(exist_ok=True)
        
        self.test_results = {
            "timestamp": datetime.now().isoformat(),
            "tests_run": 0,
            "tests_passed": 0,
            "tests_failed": 0,
            "details": []
        }
    
    def run_all_tests(self):
        """Run all test suites"""
        logger.info("Starting Comprehensive Test Facility")
        logger.info("=" * 60)
        
        start_time = time.time()
        
        # 1. Run unit tests
        self.run_unit_tests()
        
        # 2. Run integration tests
        self.run_integration_tests()
        
        # 3. Run output generation tests
        self.run_output_generation_tests()
        
        # 4. Run API tests
        self.run_api_tests()
        
        # 5. Run performance tests
        self.run_performance_tests()
        
        # 6. Run validation tests
        self.run_validation_tests()
        
        # Generate report
        total_time = time.time() - start_time
        self.generate_report(total_time)
        
        logger.info("=" * 60)
        logger.info(f"Total tests run: {self.test_results['tests_run']}")
        logger.info(f"Passed: {self.test_results['tests_passed']}")
        logger.info(f"Failed: {self.test_results['tests_failed']}")
        logger.info(f"Total time: {total_time:.2f} seconds")
    
    def run_unit_tests(self):
        """Run pytest unit tests"""
        logger.info("\n1. Running Unit Tests...")
        logger.info("-" * 40)
        
        try:
            result = subprocess.run(
                ["python", "-m", "pytest", "tests/", "-v", "--tb=short"],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                self.test_results["tests_passed"] += 1
                logger.info("✅ Unit tests passed")
            else:
                self.test_results["tests_failed"] += 1
                logger.error("❌ Unit tests failed")
                logger.error(result.stdout)
            
            self.test_results["tests_run"] += 1
            self.test_results["details"].append({
                "test": "Unit Tests",
                "status": "passed" if result.returncode == 0 else "failed",
                "output": result.stdout
            })
            
        except Exception as e:
            logger.error(f"Error running unit tests: {e}")
            self.test_results["tests_failed"] += 1
            self.test_results["tests_run"] += 1
    
    def run_integration_tests(self):
        """Run integration tests for all roof types"""
        logger.info("\n2. Running Integration Tests...")
        logger.info("-" * 40)
        
        test_configs = [
            # FLAT roof tests
            {"name": "flat_standard", "roof_type": CarportRoofType.FLAT, 
             "span": 6000, "length": 9000, "height": 2700, "pitch": 0, 
             "bays": 3, "overhang": 600},
            
            # GABLE roof tests
            {"name": "gable_standard", "roof_type": CarportRoofType.GABLE, 
             "span": 6000, "length": 9000, "height": 2700, "pitch": 15, 
             "bays": 3, "overhang": 0},
            
            # AWNING roof tests
            {"name": "awning_standard", "roof_type": CarportRoofType.AWNING, 
             "span": 5000, "length": 7000, "height": 2400, "pitch": 10, 
             "bays": 2, "overhang": 0},
            
            # ATTACHED_AWNING roof tests
            {"name": "attached_standard", "roof_type": CarportRoofType.ATTACHED_AWNING, 
             "span": 3000, "length": 6000, "height": 2400, "pitch": 10, 
             "bays": 2, "overhang": 0},
        ]
        
        generator = CarportIFCGenerator()
        passed = 0
        
        for config in test_configs:
            try:
                output_path = self.dirs["integration"] / f"{config['name']}.ifc"
                
                generator.generate_carport(
                    span=config["span"],
                    length=config["length"],
                    height=config["height"],
                    roof_type=config["roof_type"],
                    pitch=config["pitch"],
                    bays=config["bays"],
                    overhang=config["overhang"],
                    output_path=str(output_path)
                )
                
                if output_path.exists():
                    logger.info(f"✅ Generated {config['name']}")
                    passed += 1
                else:
                    logger.error(f"❌ Failed to generate {config['name']}")
                    
            except Exception as e:
                logger.error(f"❌ Error generating {config['name']}: {e}")
        
        self.test_results["tests_run"] += len(test_configs)
        self.test_results["tests_passed"] += passed
        self.test_results["tests_failed"] += len(test_configs) - passed
        
        logger.info(f"Integration tests: {passed}/{len(test_configs)} passed")
    
    def run_output_generation_tests(self):
        """Test all output formats"""
        logger.info("\n3. Running Output Generation Tests...")
        logger.info("-" * 40)
        
        # Create test building
        building_input = BuildingInput()
        building_input.roof_type = "GABLE"
        building_input.span = 6000
        building_input.length = 9000
        building_input.height = 2700
        building_input.pitch = 15
        building_input.bays = 3
        
        carport = CarportBuilder.create_carport(building_input)
        
        # Test IFC generation
        try:
            ifc_path = self.dirs["ifc"] / "test_output.ifc"
            generator = CarportIFCGenerator()
            generator.generate_carport(
                span=6000, length=9000, height=2700,
                roof_type=CarportRoofType.GABLE,
                pitch=15, bays=3,
                output_path=str(ifc_path)
            )
            
            if ifc_path.exists():
                logger.info("✅ IFC generation test passed")
                self.test_results["tests_passed"] += 1
            else:
                logger.error("❌ IFC generation test failed")
                self.test_results["tests_failed"] += 1
                
        except Exception as e:
            logger.error(f"❌ IFC generation error: {e}")
            self.test_results["tests_failed"] += 1
        
        self.test_results["tests_run"] += 1
        
        # Test GLB generation
        try:
            glb_path = self.dirs["glb"] / "test_output.glb"
            glb_generator = CarportGLBGenerator()
            generated_path = glb_generator.generate_carport_glb(
                span=6000, length=9000, height=2700,
                roof_type=CarportRoofType.GABLE,
                pitch=15, bays=3,
                output_path=str(glb_path)
            )
            
            if generated_path.exists():
                logger.info("✅ GLB generation test passed")
                self.test_results["tests_passed"] += 1
            else:
                logger.error("❌ GLB generation test failed")
                self.test_results["tests_failed"] += 1
                
        except Exception as e:
            logger.error(f"❌ GLB generation error: {e}")
            self.test_results["tests_failed"] += 1
        
        self.test_results["tests_run"] += 1
    
    def run_api_tests(self):
        """Test API endpoints"""
        logger.info("\n4. Running API Tests...")
        logger.info("-" * 40)
        
        # Check if API is running
        import requests
        
        api_url = "http://localhost:8000"
        
        try:
            # Test health endpoint
            response = requests.get(f"{api_url}/api/health", timeout=5)
            if response.status_code == 200:
                logger.info("✅ API health check passed")
                self.test_results["tests_passed"] += 1
            else:
                logger.error("❌ API health check failed")
                self.test_results["tests_failed"] += 1
            
            self.test_results["tests_run"] += 1
            
            # Test v2 endpoints
            endpoints = [
                "/api/v2/materials",
                "/api/v2/roof-types",
                "/api/v2/examples"
            ]
            
            for endpoint in endpoints:
                try:
                    response = requests.get(f"{api_url}{endpoint}", timeout=5)
                    if response.status_code == 200:
                        logger.info(f"✅ {endpoint} test passed")
                        self.test_results["tests_passed"] += 1
                    else:
                        logger.error(f"❌ {endpoint} test failed")
                        self.test_results["tests_failed"] += 1
                except:
                    logger.error(f"❌ {endpoint} test failed (timeout)")
                    self.test_results["tests_failed"] += 1
                
                self.test_results["tests_run"] += 1
                
        except requests.exceptions.ConnectionError:
            logger.warning("⚠️ API not running - skipping API tests")
            logger.info("Run 'python run_api.py' to start the API server")
    
    def run_performance_tests(self):
        """Test performance metrics"""
        logger.info("\n5. Running Performance Tests...")
        logger.info("-" * 40)
        
        generator = CarportIFCGenerator()
        
        # Test different sizes
        test_cases = [
            {"name": "small", "span": 3000, "length": 3000, "bays": 1},
            {"name": "medium", "span": 6000, "length": 9000, "bays": 3},
            {"name": "large", "span": 12000, "length": 15000, "bays": 5}
        ]
        
        for test in test_cases:
            start_time = time.time()
            
            try:
                output_path = self.dirs["performance"] / f"{test['name']}_perf.ifc"
                generator.generate_carport(
                    span=test["span"],
                    length=test["length"],
                    height=2700,
                    roof_type=CarportRoofType.FLAT,
                    pitch=0,
                    bays=test["bays"],
                    output_path=str(output_path)
                )
                
                gen_time = time.time() - start_time
                file_size = output_path.stat().st_size / 1024  # KB
                
                logger.info(f"✅ {test['name']}: {gen_time:.2f}s, {file_size:.1f}KB")
                self.test_results["tests_passed"] += 1
                
                self.test_results["details"].append({
                    "test": f"Performance - {test['name']}",
                    "generation_time": gen_time,
                    "file_size_kb": file_size
                })
                
            except Exception as e:
                logger.error(f"❌ Performance test {test['name']} failed: {e}")
                self.test_results["tests_failed"] += 1
            
            self.test_results["tests_run"] += 1
    
    def run_validation_tests(self):
        """Test validation and error handling"""
        logger.info("\n6. Running Validation Tests...")
        logger.info("-" * 40)
        
        generator = CarportIFCGenerator()
        
        # Test invalid inputs
        invalid_tests = [
            {"name": "negative_span", "span": -1000, "expected_error": True},
            {"name": "zero_height", "height": 0, "expected_error": True},
            {"name": "invalid_pitch", "pitch": 90, "expected_error": True},
            {"name": "too_many_bays", "bays": 100, "expected_error": True}
        ]
        
        passed = 0
        
        for test in invalid_tests:
            try:
                # Set defaults
                params = {
                    "span": 6000, "length": 9000, "height": 2700,
                    "roof_type": CarportRoofType.FLAT, "pitch": 0, "bays": 3
                }
                
                # Override with test values
                params.update({k: v for k, v in test.items() 
                             if k not in ["name", "expected_error"]})
                
                output_path = self.dirs["validation"] / f"{test['name']}.ifc"
                generator.generate_carport(output_path=str(output_path), **params)
                
                # If we get here and expected error, test failed
                if test.get("expected_error"):
                    logger.error(f"❌ {test['name']}: Expected error but succeeded")
                else:
                    logger.info(f"✅ {test['name']}: Passed")
                    passed += 1
                    
            except Exception as e:
                if test.get("expected_error"):
                    logger.info(f"✅ {test['name']}: Correctly caught error")
                    passed += 1
                else:
                    logger.error(f"❌ {test['name']}: Unexpected error: {e}")
        
        self.test_results["tests_run"] += len(invalid_tests)
        self.test_results["tests_passed"] += passed
        self.test_results["tests_failed"] += len(invalid_tests) - passed
        
        logger.info(f"Validation tests: {passed}/{len(invalid_tests)} passed")
    
    def generate_report(self, total_time: float):
        """Generate comprehensive test report"""
        report_path = self.dirs["reports"] / f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        self.test_results["total_time_seconds"] = total_time
        self.test_results["success_rate"] = (
            self.test_results["tests_passed"] / self.test_results["tests_run"] * 100
            if self.test_results["tests_run"] > 0 else 0
        )
        
        with open(report_path, "w") as f:
            json.dump(self.test_results, f, indent=2)
        
        logger.info(f"\n📊 Test report saved to: {report_path}")
        
        # Also create a summary report
        summary_path = self.dirs["reports"] / "latest_summary.txt"
        with open(summary_path, "w") as f:
            f.write("COMPREHENSIVE TEST FACILITY REPORT\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Date: {self.test_results['timestamp']}\n")
            f.write(f"Total Time: {total_time:.2f} seconds\n\n")
            f.write(f"Tests Run: {self.test_results['tests_run']}\n")
            f.write(f"Tests Passed: {self.test_results['tests_passed']}\n")
            f.write(f"Tests Failed: {self.test_results['tests_failed']}\n")
            f.write(f"Success Rate: {self.test_results['success_rate']:.1f}%\n\n")
            
            if self.test_results["tests_failed"] > 0:
                f.write("FAILED TESTS:\n")
                for detail in self.test_results["details"]:
                    if detail.get("status") == "failed":
                        f.write(f"- {detail.get('test', 'Unknown')}\n")


def main():
    """Main entry point with CLI arguments"""
    parser = argparse.ArgumentParser(
        description="Comprehensive Testing Facility for PyModel BIM Backend"
    )
    
    parser.add_argument(
        "--suite",
        choices=["all", "unit", "integration", "output", "api", "performance", "validation"],
        default="all",
        help="Test suite to run"
    )
    
    parser.add_argument(
        "--output-dir",
        default="test_output",
        help="Output directory for test results"
    )
    
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Enable verbose logging"
    )
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Create test facility
    facility = ComprehensiveTestFacility(args.output_dir)
    
    # Run requested tests
    if args.suite == "all":
        facility.run_all_tests()
    elif args.suite == "unit":
        facility.run_unit_tests()
    elif args.suite == "integration":
        facility.run_integration_tests()
    elif args.suite == "output":
        facility.run_output_generation_tests()
    elif args.suite == "api":
        facility.run_api_tests()
    elif args.suite == "performance":
        facility.run_performance_tests()
    elif args.suite == "validation":
        facility.run_validation_tests()
    
    logger.info("\n✅ Testing complete!")
    
    # Return exit code based on failures
    return 1 if facility.test_results["tests_failed"] > 0 else 0


if __name__ == "__main__":
    sys.exit(main())